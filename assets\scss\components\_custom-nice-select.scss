@use '../utils' as *;

/*----------------------------------------*/
/*  custom nice select
/*----------------------------------------*/
.custom-nice-select {
  &.has-w-full {
    .nice-select {
      width: 100%;
      .list {
        width: 100%;
      }
    }
  }

  .nice-select {
    font-family: var(--td-heading-font);
    background-color: transparent;
    border-radius: 0px;
    border: 1px solid rgba(21, 20, 21, 0.16);
    height: 52px;
    line-height: 50px;
    font-size: 14px;
    font-weight: 400;
    padding-inline-start: 12px;
    padding-inline-end: 28px;

    @media #{$xs} {
      height: 45px;
      line-height: 42px;
    }
  }

  .nice-select:after {
    height: 7px;
    border-color: #151415;
    width: 7px;

    @include rtl {
        right: auto;
        left: 10px;
    }
  }

  .nice-select .list {
    border: 1px solid #E5E5E5;
    background: var(--td-white);
    box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.10);
    border-radius: 0px;
    inset-inline-start: auto;
    inset-inline-end: 0;
    z-index: 90;
    width: 100%;
    
  }

  .nice-select .option {
    padding-inline-start: 16px;
    padding-inline-end: 16px;
    @include rtl {
        text-align: right;
    }
  }

  .nice-select .option:hover,
  .nice-select .option.focus,
  .nice-select .option.selected.focus {
    background-color: #F2F2F2;
  }
}