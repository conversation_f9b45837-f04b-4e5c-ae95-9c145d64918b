@use "../../utils" as *;

/*----------------------------------------*/
/*  testimonial
/*----------------------------------------*/
.testimonial {
  background: var(--td-bg-1);

  .testimonial-box-full {
    display: flex;
    justify-content: center;
    align-items: center;

    .testimonial-box {
      width: 100%;
      max-width: 1095px;
      border: 1px solid #aa8453;
      background: rgba(255, 255, 255, 0.3);
      backdrop-filter: blur(5px);
      padding: 55px 45px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 76px;
      overflow: hidden;

      @media #{$lg} {
        padding: 35px 25px;
        gap: 40px;
      }

      @media #{$md,$xs} {
        padding: 25px 15px;
        gap: 30px;
      }

      @media #{$xs} {
        padding: 15px 5px;
        gap: 10px;
      }

      @media #{$sm} {
        padding: 25px 15px;
        gap: 30px;
      }

      .left,
      .right {
        .testimonial-btn {
          display: inline-flex;
          height: 50px;
          width: 50px;
          justify-content: center;
          align-items: center;
          gap: 10px;
          flex-shrink: 0;
          background: var(--td-primary);
          border: 1px solid #aa8453;
          backdrop-filter: blur(4.5px);

          @media #{$xs} {
            height: 30px;
            width: 30px;
          }

          @media #{$sm} {
            height: 40px;
            width: 40px;
          }

          .arrow {
            color: var(--td-white);
            font-size: 24px;
            transition: all 0.3s ease-in-out;

            @include rtl {
              transform: rotate(180deg);
            }

            @media #{$xs} {
              font-size: 18px;
            }
          }

          &.disabled {
            border: 1px solid var(--td-primary);
            background: transparent;

            .arrow {
              color: var(--td-heading);
            }
          }
        }
      }

      .middle {
        flex: 1;
        text-align: center;
        display: flex;
        flex-direction: column;

        p {
          color: var(--td-white);
          font-size: 18px;
          font-weight: 400;
          line-height: lh(30, 18);
          margin: 0;
        }

        .main-content {
          display: flex;
          flex-direction: column;
          align-items: center;

          .user-reviews {
            .user-review {
              display: none;
              height: 200px;
              overflow: hidden;
            }

            .active-always {
              .user-review {
                display: flex;
                flex-direction: column;
                align-items: center;
                height: 200px;
                overflow: hidden;

                @media #{$md,$xs} {
                  height: 250px;
                }

                @media #{$xs} {
                  height: fit-content;
                  margin-bottom: 20px;
                }
              }
            }
          }

          .star {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
          }

          .description {
            p {
              color: var(--td-text-primary);
              text-align: center;
              font-size: 20px;
              font-weight: 400;
              line-height: lh(30, 20);

              @media #{$xs} {
                font-size: 18px;
              }
            }
          }

          .users-lists {
            display: flex;
            align-items: center;
            gap: 40px;
            width: 292px;
            overflow: hidden;

            @media #{$xs} {
              width: 193px;
              gap: 10px;
            }

            @media #{$sm} {
              width: 252px;
            }

            .users-list {
              display: flex;
              flex-direction: column;
              align-items: center;
              cursor: pointer;

              .img {
                width: 60px;
                height: 60px;
                margin-bottom: 20px;
                transform: translateY(10px);

                @media #{$xs} {
                  height: 50px;
                  width: 50px;
                  transform: translateY(2px);
                }

                img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                }
              }

              .content {
                h5 {
                  color: var(--td-heading);
                  text-align: center;
                  font-size: 18px;
                  font-weight: 400;
                  line-height: normal;
                  visibility: hidden;
                  opacity: 0;
                }
              }
            }

            .active-always {
              .users-list {
                .img {
                  height: 92px;
                  width: 92px;
                  border: 3px solid var(--td-primary);
                  border-radius: 50%;
                  transform: translateY(0px);

                  @media #{$xs} {
                    height: 72px;
                    width: 72px;
                  }
                }

                .content {
                  h5 {
                    visibility: visible;
                    opacity: 1;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}