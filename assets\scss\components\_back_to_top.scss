@use '../utils' as *;

/*----------------------------------------*/
/*  2.2 Back to Top
/*----------------------------------------*/

#back-to-top {
	position: fixed;
	inset-inline-end: 40px;
	bottom: 40px;
	background: var(--td-primary);
	color: #fff;
	z-index: 99;
	width: 40px;
    height: 40px;
	text-align: center;
	line-height: 50px;
	opacity: 0;
	visibility: hidden;
	transition: .3s;
	transform: scale(.7);
	display: flex;
    justify-content: center;
    align-items: center;
    border: 2px solid transparent;

	@media #{$xs} {
		inset-inline-end: 10px;
		bottom: 10px;
		width: 35px;
		height: 35px;
	}

	img {
		width: 25px;

		@media #{$xs} {
			width: 15px;
		}
	}

	&.show {
		opacity: 1;
		visibility: visible;
		transform: scale(1);

		&::before {
			content: '';
			position: absolute;
			top: -7px;
			inset-inline-start: -7px;
			width: 50px;
			height: 50px;
			border: 1px solid var(--td-primary);
			animation: blink-border 1.5s infinite;
			pointer-events: none;

			@media #{$xs} {
				top: -5px;
				inset-inline-start: -5px;
				width: 41px;
				height: 41px;
			}
		}
	}
}

#back-to-top.back-to-top-2 {
    background: var(--td-theme-color-2);
    border-color: var(--td-theme-color-2);
	&::before{
		border-color: var(--td-theme-color-2);
	}
}

#back-to-top.back-to-top-2:hover {
    background: var(--td-theme-color-2);
    border-color: var(--td-theme-color-2);
}


@keyframes blink-border {
	0% {
		opacity: 0;
		transform: scale(1);
	}
	50% {
		opacity: 1;
		transform: scale(1.1);
	}
	100% {
		opacity: 0;
		transform: scale(1);
	}
}
