@use '../utils' as *;

/*----------------------------------------*/
/*  Reservation
/*----------------------------------------*/
.reservation-box {
  padding: 30px;
  background: var(--td-white);
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.10);

  @media #{$xl,$lg,$md,$xs} {
    padding: 20px;
  }

  .title-price {
    display: flex;
    justify-content: space-between;
    align-items: start;
    gap: 10px;
    margin-bottom: 30px;

    .left {
      h5 {
        color: var(--td-heading);
        font-size: 30px;
        font-weight: 400;
        line-height: normal;

        @media #{$lg} {
          font-size: 28px;
        }

        @media #{$md} {
          font-size: 26px;
        }

        @media #{$xs} {
          font-size: 24px;
        }
      }
    }

    .right {
      h4 {
        color: var(--td-heading);
        font-size: 30px;
        font-weight: 400;
        line-height: normal;

        @media #{$lg} {
          font-size: 28px;
        }

        @media #{$md} {
          font-size: 26px;
        }

        @media #{$xs} {
          font-size: 20px;
        }

        .discount {
          color: var(--td-text-primary);
          font-size: 20px;
          font-weight: 400;
          line-height: normal;
          text-decoration-line: line-through;

          @media #{$xs} {
            font-size: 18px;
          }
        }
      }

      p {
        color: var(--td-text-primary);
        font-size: 14px;
        font-weight: 400;
        line-height: normal;
      }
    }
  }



  @media #{$xs} {
    .date-range {
      width: 100%;
      overflow-x: auto;
      overflow-y: hidden;
      --webkit-overflow-scrolling: touch;

      .date-range-full {
        width: 100%;
        min-width: 340px;
        border-collapse: collapse;
      }
    }
  }

  @media #{$sm} {
    .date-range {
      width: unset;
      overflow-x: unset;
      overflow-y: unset;
      --webkit-overflow-scrolling: unset;

      .date-range-full {
        width: unset;
        min-width: unset;
        border-collapse: unset;
      }
    }
  }

  .person-select {
    margin-top: 20px;
  }

  .extra-services {
    margin-top: 30px;

    h5 {
      color: var(--td-heading);
      font-size: 20px;
      font-weight: 400;
      line-height: normal;
    }

    .checkboxes {
      margin-top: 16px;

      .checkbox {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 5px;

        &:last-child {
          margin-bottom: 0;
        }

        .price {
          h6 {
            color: var(--td-text-primary);
            font-family: var(--td-body-font);
            font-size: 16px;
            font-weight: 400;
            line-height: normal;
          }
        }

        .animate-custom {
          .cbx {
            -webkit-user-select: none;
            user-select: none;
            -webkit-tap-highlight-color: transparent;
            cursor: pointer;

            &::before {
              display: none;
            }

            span {
              display: inline-block;
              vertical-align: middle;

              a {
                color: var(--td-primary);

                &:hover {
                  color: var(--td-primary);
                }
              }

              &:first-child {
                position: relative;
                width: 17px;
                height: 17px;
                @include border-radius(0px);
                transform: scale(1);
                vertical-align: middle;
                border: 1px solid #b9b8c3;
                transition: all 0.2s ease;

                svg {
                  position: absolute;
                  z-index: 1;
                  top: 3px;
                  inset-inline-start: 1px;
                  fill: none;
                  stroke: var(--td-white);
                  stroke-width: 1;
                  stroke-linecap: round;
                  stroke-linejoin: round;
                  stroke-dasharray: 16px;
                  stroke-dashoffset: 16px;
                  transition: all 0.3s ease;
                  transition-delay: 0.1s;
                  transform: translate3d(0, 0, 0);
                }

                &:before {
                  content: "";
                  width: 100%;
                  height: 100%;
                  background: var(--td-primary);
                  display: block;
                  transform: scale(0);
                  opacity: 1;
                  border-radius: 50%;
                  transition-delay: 0.2s;
                }
              }

              &:last-child {
                margin-inline-start: 5px;
                color: var(--td-text-primary);
                font-family: var(--td-body-font);
                font-size: 16px;
                font-weight: 400;
                line-height: normal;

                &:after {
                  content: "";
                  position: absolute;
                  top: 8px;
                  inset-inline-start: 0;
                  height: 1px;
                  width: 100%;
                  background: #b9b8c3;
                  transform-origin: 0 0;
                  transform: scaleX(0);
                }
              }
            }

            &:hover {
              span {
                &:first-child {
                  border-color: var(--td-primary);
                }
              }
            }
          }

          .inp-cbx {
            &:checked {
              &+.cbx {
                span {
                  &:first-child {
                    border-color: var(--td-primary);
                    background: var(--td-primary);
                    animation: check-15 0.6s ease;

                    svg {
                      stroke-dashoffset: 0;
                    }

                    &:before {
                      transform: scale(2.2);
                      opacity: 0;
                      transition: all 0.6s ease;
                    }
                  }

                  &:last-child {
                    transition: all 0.3s ease;
                  }
                }
              }
            }
          }

          input[type=checkbox]~label::after {
            display: none;
          }

          input[type=checkbox]~label {
            padding-inline-start: 0;
          }

          &-2 {
            .cbx {
              span {
                &:last-child {
                  color: #6B7280;
                }
              }
            }
          }
        }

        @keyframes check-15 {
          50% {
            transform: scale(1.2);
          }
        }
      }
    }
  }

  .details {
    margin-top: 30px;

    h5 {
      color: var(--td-heading);
      font-size: 20px;
      font-weight: 400;
      line-height: normal;
    }

    .details-box {
      margin-top: 16px;

      .single-details {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;

        &:not(:last-child) {
          border-top: 1px dashed rgba(21, 20, 21, 0.26);

        }

        .left {
          p {
            color: var(--td-text-primary);
            font-family: var(--td-body-font);
            font-size: 16px;
            font-weight: 400;
            line-height: normal;

            span {
              color: var(--td-heading);
              font-size: 20px;
              font-weight: 500;
              line-height: normal;
            }
          }
        }

        .right {
          p {
            color: var(--td-heading);
            font-family: var(--td-body-font);
            font-size: 16px;
            font-weight: 400;
            line-height: normal;

            span {
              color: var(--td-heading);
              font-size: 20px;
              font-weight: 500;
              line-height: normal;
            }
          }
        }

        &:last-child {
          border-bottom: none;
          border-top: 1px solid rgba(21, 20, 21, 0.26);
          padding-top: 15px;
          padding-bottom: 0;
        }
      }
    }
  }

  .reserve-action-btn {
    margin-top: 30px;
  }
}

.common-product-design {
  .product-details {
    .banner-img {
      height: 521px;
      width: 100%;

      @media #{$lg} {
        height: 450px;
      }

      @media #{$md} {
        height: 400px;
      }

      @media #{$xs} {
        height: 300px;
      }

      @media #{$sm} {
        height: 350px;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .product-details-box {
      margin-top: 50px;

      h2 {
        color: var(--td-heading);
        font-size: 30px;
        font-weight: 400;
        line-height: normal;
        margin-bottom: 5px;

        @media #{$lg} {
          font-size: 28px;
        }

        @media #{$md} {
          font-size: 26px;
        }

        @media #{$xs} {
          font-size: 24px;
        }
      }

      .rooms-amenities {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 10px;

        .single-amenities {
          display: flex;
          align-items: center;
          gap: 4px;

          .icon {
            width: 18px;
            height: 18px;
            display: flex;
            justify-content: center;
            align-items: center;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          p {
            font-size: 13px;
            color: var(--td-text-primary);
            line-height: normal;
            margin-bottom: 0;
          }
        }
      }

      .description {
        color: var(--td-text-primary);
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: lh(26, 16);
        margin-top: 16px;
      }
    }
  }

  .services {
    h5 {
      color: var(--td-heading);
      font-size: 24px;
      font-weight: 400;
      line-height: normal;

      @media #{$lg} {
        font-size: 22px;
      }

      @media #{$md} {
        font-size: 20px;
      }

      @media #{$xs} {
        font-size: 18px;
      }
    }

    ul {
      list-style-type: none;
      margin-top: 16px;

      li {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0;
        }

        span {
          display: inline-flex;
          justify-content: center;
          align-items: center;
          width: 14px;
          height: 14px;
          background-color: var(--td-primary);

          .check-icon {
            color: var(--td-white);
            font-size: 11px;
          }
        }

        p {
          color: var(--td-text-primary);
          font-size: 16px;
          font-weight: 500;
          line-height: normal;
        }
      }
    }
  }

  .section-saperate {
    margin: 50px 0;
    color: rgba(0, 0, 0, 0.40);
    border: 0;
    border-top: var(--bs-border-width) solid;
    opacity: .25;

    @media #{$lg} {
      margin: 40px 0;
    }

    @media #{$md} {
      margin: 30px 0;
    }

    @media #{$xs} {
      margin: 20px 0;
    }
  }

  .amenities-box-full {
    h5 {
      color: var(--td-heading);
      font-size: 24px;
      font-weight: 400;
      line-height: normal;

      @media #{$lg} {
        font-size: 22px;
      }

      @media #{$md} {
        font-size: 20px;
      }

      @media #{$xs} {
        font-size: 18px;
      }
    }

    .amenities-box {
      margin-top: 16px;
      display: flex;
      flex-wrap: wrap;

      .amenities {
        display: flex;
        align-items: center;
        gap: 10px;
        background-color: transparent;

        &:not(:last-child) {
          margin-bottom: 16px;
        }

        .icon {
          display: flex;
          width: 24px;
          height: 24px;
          padding: 2px;
          justify-content: center;
          align-items: center;

          @media #{$xs} {
            width: 20px;
            height: 20px;
          }

          img {
            width: 100%;
            height: 100%;
          }
        }

        .text {
          p {
            color: var(--td-text-primary);
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: lh(26, 16);

            @media #{$xs} {
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}

.date-range-input {
  position: relative;

  label {
    position: absolute;
    top: 15px;
    inset-inline-start: 12px;
    color: var(--td-heading);
    font-family: var(--td-heading-font);
    font-size: 14px;
    font-weight: 400;
    line-height: lh(26, 16);
  }

  .flatpickr-range {
    outline: none;
    height: 54px;
    width: 100%;
    padding: 0 12px;
    border-radius: 0px;
    border: 1px solid rgba(21, 20, 21, 0.16);
    color: var(--td-heading);
    background: transparent;
    font-size: 14px;
    text-align: end;
    color: var(--td-heading);

    &:focus {
      border: 1px solid rgba(21, 20, 21, 0.16);
      box-shadow: unset;
      opacity: 1;
    }

    &::placeholder {
      color: var(--td-heading);
      font-size: 14px;
      font-weight: 400;
      line-height: 100%;
      text-align: end;
    }
  }

  // Customize form 
}