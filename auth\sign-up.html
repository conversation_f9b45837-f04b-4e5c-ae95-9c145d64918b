<!DOCTYPE html>
<html class="no-js" lang="zxx">

<head>
  <meta charset="utf-8">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <title>Sign Up || Welcome to Hotelib Hotel, where luxury meets the skyline.</title>
  <meta name="description" content="">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <!-- Place favicon.ico in the root directory -->
  <link rel="shortcut icon" type="image/x-icon" href="../assets/images/favicon.svg">
  <!-- CSS here -->
  <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
  <link rel="stylesheet" href="../assets/css/all.min.css">
  <link rel="stylesheet" href="../assets/css/flag-icon.css">
  <link rel="stylesheet" href="../assets/css/swiper.min.css">
  <link rel="stylesheet" href="../assets/css/magnific-popup.css">
  <link rel="stylesheet" href="../assets/css/nice-select.css">
  <link rel="stylesheet" href="../assets/css/select2.min.css">
  <link rel="stylesheet" href="../assets/css/flatpickr.min.css">
  <link rel="stylesheet" href="../assets/css/flatpicker-range.css">
  <link rel="stylesheet" href="../assets/css/slick.css">
  <link rel="stylesheet" href="../assets/css/styles.css">
</head>

<body>

  <!-- Body main wrapper start -->
  <main>

    <!-- auth area start -->
    <section class="auth-section">
        <div class="auth-main-grid">
            <div class="auth-left">
                <div class="auth-thumb">
                    <img src="../assets/images/auth/signup.png" alt="Signup Thumb">
                </div>
            </div>
            <div class="auth-right">
                <div class="inner">
                    <div class="auth-form-box is-signup">
                        <div class="auth-top-contents">
                            <div class="auth-logo">
                                <a href="../themes/index.html">
                                    <img src="../assets/images/logo/logo.svg" alt="Auth Logo">
                                </a>
                            </div>
                            <div class="intro-contents">
                                <h3 class="title">Create Your Account</h3>
                                <p class="description">Complete Your Booking by Signing In</p>
                            </div>
                        </div>
                        <div class="auth-from-box">
                            <form id="sign-up-form" action="#">
                                <div class="row gy-4">
                                    <div class="col-lg-6">
                                        <div class="td-form-group">
                                            <label class="input-label">First Name <span>*</span></label>
                                            <div class="input-field">
                                                <input type="text" class="form-control" placeholder="" required>
                                            </div>
                                            <p class="feedback-invalid">This field is required</p>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="td-form-group">
                                            <label class="input-label">Last Name <span>*</span></label>
                                            <div class="input-field">
                                                <input type="text" class="form-control" placeholder="" required>
                                            </div>
                                            <p class="feedback-invalid">This field is required</p>
                                        </div>
                                    </div>
        
                                    <div class="col-lg-6">
                                        <div class="td-form-group">
                                            <label class="input-label">Email address <span>*</span></label>
                                            <div class="input-field">
                                                <input type="email" class="form-control" placeholder="" required>
                                            </div>
                                            <p class="feedback-invalid">This field is required</p>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="td-form-group">
                                            <label class="input-label">Username <span>*</span></label>
                                            <div class="input-field">
                                                <input type="text" class="form-control" placeholder="" required>
                                            </div>
                                            <p class="feedback-invalid">This field is required</p>
                                        </div>
                                    </div>

                                    <div class="col-lg-6">
                                        <div class="td-form-group">
                                            <label class="input-label">Country <span>*</span></label>
                                            <div class="common-nice-select-dropdown">
                                                <div class="custom-nice-select has-w-full">
                                                    <select class="nice-select-sort-1">
                                                        <option selected>English (US)</option>
                                                        <option>English (UK)</option>
                                                        <option>French</option>
                                                        <option>German</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <p class="feedback-invalid">This field is required</p>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="td-form-group">
                                            <label class="input-label">Phone <span>*</span></label>
                                            <div class="input-field">
                                                <input type="text" class="form-control" placeholder="" required>
                                            </div>
                                            <p class="feedback-invalid">This field is required</p>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="td-form-group">
                                            <label class="input-label">Gender <span>*</span></label>
                                                <div class="common-nice-select-dropdown">
                                                    <div class="custom-nice-select has-w-full">
                                                        <select class="nice-select-sort-1">
                                                        <option value="Male">Male</option>  
                                                        <option value="Female">Female</option>  
                                                        <option value="Other">Other</option>  
                                                        </select>
                                                    </div>
                                                </div>
                                            <p class="feedback-invalid">This field is required</p>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="td-form-group has-right-icon">
                                            <label class="input-label">Password<span>*</span></label>
                                            <div class="input-field">
                                                <input type="password" class="form-control password-input"
                                                    placeholder="Password" required>
                                                <span class="input-icon eyeicon">
                                                    <!-- Option 1: Image version -->
                                                    <img class="eye-img"
                                                        src="../assets/images/icon/eye.svg" alt="eye">
    
                                                    <!-- Option 2: Font Awesome icon version -->
                                                    <!-- <i class="fa fa-eye" id="eye-icon-i"></i> -->
                                                </span>
                                            </div>
                                            <p class="feedback-invalid">This field is required</p>
                                        </div>
                                    </div>
                                    <div class="col-lg-12">
                                        <div class="td-form-group has-right-icon">
                                            <label class="input-label">Password<span>*</span></label>
                                            <div class="input-field">
                                                <input type="password" class="form-control password-input"
                                                    placeholder="Password" required>
                                                <span class="input-icon eyeicon">
                                                    <!-- Option 1: Image version -->
                                                    <img class="eye-img"
                                                        src="../assets/images/icon/eye.svg" alt="eye">
    
                                                    <!-- Option 2: Font Awesome icon version -->
                                                    <!-- <i class="fa fa-eye" id="eye-icon-i"></i> -->
                                                </span>
                                            </div>
                                            <p class="feedback-invalid">This field is required</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="auth-login-option">
                                    <div class="animate-custom">
                                        <input class="inp-cbx" id="auth_trems" type="checkbox" style="display: none;">
                                        <label class="cbx" for="auth_trems">
                                            <span>
                                                <svg width="12px" height="9px" viewBox="0 0 12 9">
                                                    <polyline points="1 5 4 8 11 1"></polyline>
                                                </svg>
                                            </span>
                                            <span>I agree with Terms & Conditions</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="auth-from-btn-wrap">
                                    <button class="primary-button xl-btn w-100" type="submit">Sign Up</button>
                                </div>
                                <div class="auth-bottom-contents">
                                    <div class="have-auth-account">
                                        <p class="description">Already have an account? <a class="underline-btn"  href="sign-in.html"> Sign In</a></p>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- auth area end -->

  </main>
  <!-- Body main wrapper end -->

  <!-- JS here -->
  <script src="../assets/js/jquery-3.7.1.min.js"></script>
  <script src="../assets/js/bootstrap.bundle.min.js"></script>
  <script src="../assets/js/jquery.nice-select.min.js"></script>
  <script src="../assets/js/magnific-popup.min.js"></script>
  <script src="../assets/js/swiper.min.js"></script>
  <script src="../assets/js/jarallax.min.js"></script>
  <script src="../assets/js/iconify.min.js"></script>
  <script src="../assets/js/moment.min.js"></script>
  <script src="../assets/js/select2.js"></script>
  <script src="../assets/js/flatpickr.js"></script>
  <script src="../assets/js/slick.min.js"></script>
  <script src="../assets/js/main.js"></script>
    <script>
        // Function to toggle password visibility
        function togglePasswordVisibility(event) {
            const eyeIconSpan = event.currentTarget;
            const passwordInput = eyeIconSpan.closest('.input-field').querySelector('input');  
            // For the image version
            const eyeIconImg = eyeIconSpan.querySelector('img');  
            const eyeIconI = eyeIconSpan.querySelector('i');  

            // Toggle logic for the image version
            if (passwordInput.type === "password") {
                passwordInput.type = "text";

                if (eyeIconImg) {
                    eyeIconImg.src = "../assets/images/icon/eye-open.svg"; 
                }
                if (eyeIconI) {
                    eyeIconI.classList.replace('fa-eye', 'fa-eye-slash'); 
                }
            } else {
                passwordInput.type = "password";
                if (eyeIconImg) {
                    eyeIconImg.src = "../assets/images/icon/eye.svg";
                }
                if (eyeIconI) {
                    eyeIconI.classList.replace('fa-eye-slash', 'fa-eye');  
                }
            }
        }

        // Attach event listeners to all eye icon spans
        document.querySelectorAll('.eyeicon').forEach(function (eyeIconSpan) {
            eyeIconSpan.addEventListener('click', togglePasswordVisibility);
        });
    </script>
</body>

</html>