@use '../../utils' as *;

/*----------------------------------------*/
/* Auth styles
/*----------------------------------------*/
.auth-section {
    .auth-left {
        width: 45%;

        @media #{$lg,$xl,$xxl} {
            margin: 30px;
            margin-inline-end: 0;
        }

        @media #{$xs,$sm,$md,$lg} {
            display: none;
        }
    }

    .auth-right {
        margin: 30px;
        width: 55%;

        @media #{$xs,$sm,$md,$lg} {
            width: 100%;
        }

        .inner {
            position: relative;
            padding: 80px 30px;
            height: 100%;
            border: 1px solid #c4a76d;
            border-left: 0;
            display: grid;
            place-items: center;

            @media #{$xs,$sm,$md,$lg,$xl,$xxl} {
                border-left: 1px solid #c4a76d;
            }

            @media #{$md,$lg,$xl} {
                padding: 60px 30px;
            }

            @media #{$xs,} {
                padding: 30px 30px;
            }

            &::before {
                position: absolute;
                content: "";
                bottom: -1px;
                right: 100%;
                width: 100%;
                height: 1px;
                z-index: -1;
                background: #c4a76d;

                @media #{$xs,$sm,$md,$lg,$xl,$xxl} {
                    display: none;
                }

            }

            &::after {
                position: absolute;
                content: "";
                top: 0px;
                inset-inline-start: 0;
                width: 1px;
                height: calc(100% + 13px);
                background: #c4a76d;
                transform: rotate(9deg);
                transform-origin: top center;


                @media #{$xs,$sm,$md,$lg,$xl,$xxl} {
                    display: none;
                }
            }
        }
    }
}

.auth-main-grid {
    display: flex;
    min-height: 100vh;
}

.auth-thumb {
    height: 100%;
    clip-path: polygon(0% 0%, 100% 0%, 81.633% 100%, 0% 100%, 0% 0%);
    position: relative;

    @media #{$xs,$sm,$md,$lg,$xl,$xxl} {
        clip-path: none;
    }

    &::before {
        position: absolute;
        content: "";
        background-image: url(../images/auth/rectangle-shape-01.png);
        top: 50%;
        inset-inline-start: 50%;
        transform: translate(-50%, -50%);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        width: calc(100% - 60px);
        height: calc(100% - 60px);

        @media #{$xs,$sm,$md,$lg,$xl,$xxl} {
            display: none;
        }
    }

    img {
        width: 100%;
        height: 100%;
    }
}

.auth-form-box {
    width: 600px;

    @media #{$xs,$sm,$md,$lg,$xl} {
        width: 100%;
    }

    &.is-signup {
        width: 730px;

        @media #{$xs,$sm,$md,$lg,$xl,$xxl} {
            width: 100%;
        }
    }

    @media #{$xs,$sm,$md,$lg} {
        width: 100%;
    }

    .auth-top-contents {
        .auth-logo {
            margin-bottom: 40px;

            @media #{$xs,$sm,$md} {
                margin-bottom: 30px;
            }

            a {
                display: block;
            }

            img {
                height: 40px;
            }
        }

        .intro-contents {
            margin-bottom: 45px;

            .title {
                font-size: 30px;

                @media #{$md,$lg} {
                    font-size: 26px;
                }

                @media #{$xs,$sm} {
                    font-size: 24px;
                }
            }

            .description {
                margin-top: 3px;
            }
        }
    }

    .auth-login-option {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 12px;
        flex-wrap: wrap;
        margin-top: 12px;
        margin-bottom: 35px;
    }
}

.auth-bottom-contents {
    text-align: center;
}

.have-auth-account {
    margin-top: 12px;

    .description {
        font-size: 16px;
        font-weight: 500;
    }
}