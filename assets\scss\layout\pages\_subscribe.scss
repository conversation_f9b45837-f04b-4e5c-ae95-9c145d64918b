@use '../../utils' as *;

/*----------------------------------------*/
/*  Subscribe
/*----------------------------------------*/
.subscribe-newsletter {

  .subscribe-newsletter-content {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--td-bg-1);
    padding: 50px 0;

    @media #{$md} {
      padding: 40px 0;
    }

    @media #{$xs} {
      padding: 30px 0;
    }

    .subscribe-newsletter-content-full {
      width: 100%;
      max-width: 735px;

      .section-title {
        color: var(--td-heading);
        text-align: center;
        font-size: 48px;
        font-style: normal;
        font-weight: 400;
        line-height: lh(57, 48);
        letter-spacing: 0.48px;
        margin-bottom: 20px;

        @media #{$xl} {
          font-size: 40px;
        }

        @media #{$lg} {
          font-size: 35px;
        }

        @media #{$md} {
          font-size: 30px;
          margin-bottom: 15px;
        }

        @media #{$xs} {
          font-size: 26px;
          margin-bottom: 10px;
        }
      }

      .section-description {
        color: var(--td-text-primary);
        text-align: center;
        font-size: 16px;
        font-weight: 400;
        line-height: lh(26, 16);
        text-transform: capitalize;
        padding: 0 65px;
        margin-bottom: 40px;

        @media #{$xs} {
          font-size: 14px;
          margin-bottom: 20px;
        }


        @media #{$lg} {
          padding: 0 35px;
        }

        @media #{$md} {
          padding: 0 25px;
        }

        @media #{$xs} {
          padding: 0 10px;
        }
      }

      .subscribe-form {
        max-width: 440px;
        margin: 0 auto;
        position: relative;

        .form-control {
          font-family: var(--td-heading-font);
          height: 52px;
          border-radius: 0px;
          border: none;
          background: transparent;
          color: var(--td-heading);
          font-size: 14px;
          font-weight: 400;
          line-height: 100%;
          padding-inline-start: 15px;
          padding-inline-end: 112px;
          border: 1px solid rgba(21, 20, 21, 0.16);

          @media #{$xs} {
            height: 40px;
          }


          &::placeholder {
            color: var(--td-text-primary);
            font-size: 14px;
            font-weight: 400;
            line-height: 100%;
          }

          &:focus {
            border: 1px solid var(--td-primary);
          }
        }

        .subscribe-btn {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          inset-inline-end: 8px;

          .sub-button {
            background-color: var(--td-primary);
            color: var(--td-white);
            display: inline-flex;
            height: 36px;
            padding: 7px 16px;
            justify-content: center;
            align-items: center;
            gap: 10px;
            text-align: center;
            font-family: var(--td-heading-font)!important;
            font-size: 15px;
            font-weight: 400;
            line-height: normal;
            transition: all 0.3s ease-in-out;

            @media #{$xs} {
              height: 28px;
              font-size: 13px;
            }

            &:hover {
              background-color: var(--td-heading);
            }
          }
        }
      }
    }
  }
}