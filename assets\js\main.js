// ==================================================
// * Project Name   : Hotelib - Welcome to Hotelib Hotel, where luxury meets the skyline.
// * File           :  JS Base
// * Version        :  1.0
// * Last change    :  June 2025, Saturday
// * Author         :  tdevs (https://codecanyon.net/user/tdevs/portfolio)
// ==================================================

(function ($) {
  'use strict';

  var windowOn = $(window);

  // windowOn.on('load', function () {
  //   wowAnimation();
  // });

  // preloader
  $(window).on('load', function () {
    $('#preloader').fadeOut(500);
  });

  // back-to-top
  var btn = $('#back-to-top');
  windowOn.scroll(function () {
    if (windowOn.scrollTop() > 300) {
      btn.addClass('show');
    } else {
      btn.removeClass('show');
    }
  });
  btn.on('click', function () {
    $('html, body').animate({ scrollTop: 0 }, 0);
  });

  // Data Css js
  $("[data-background").each(function () {
    $(this).css(
      "background-image",
      "url( " + $(this).attr("data-background") + "  )"
    );
  });

  $("[data-width]").each(function () {
    $(this).css("width", $(this).attr("data-width"));
  });

  $("[data-bg-color]").each(function () {
    $(this).css("background-color", $(this).attr("data-bg-color"));
  });

  // video popup
  $('.popup-video').magnificPopup({
    type: 'iframe'
    // other options
  });

  // image popup
  $('.popup-image').magnificPopup({
    type: 'image',
    // other options
    gallery: {
      enabled: true,
      navigateByImgClick: true,
      preload: [0, 1]
    },
  });

  // jarallax
  if ($('.jarallax').length) {
    $('.jarallax').jarallax({
      speed: 0.2,
    });
  }

  //mobile menu
  $(".htlib-offcanvas-toggle").on('click', function () {
    $(".htlib-offcanvas").addClass("htlib-offcanvas-open");
    $(".htlib-offcanvas-overlay").addClass("htlib-offcanvas-overlay-open");
  });
  $(".htlib-offcanvas-close-toggle,.htlib-offcanvas-overlay").on('click', function () {
    $(".htlib-offcanvas").removeClass("htlib-offcanvas-open");
    $(".htlib-offcanvas-overlay").removeClass("htlib-offcanvas-overlay-open");
  });

  //nice select all
  $(document).ready(function () {
    $('.nice-select-sort-1').niceSelect();
  });
  $(document).ready(function () {
    $('.nice-select-sort-2').niceSelect();
  });



  // toggle custom dropdown
  $(document).ready(function () {
    $('.custom-dropdown-btn').on('click', function (e) {
      e.stopPropagation();

      const $dropdownBox = $(this).closest('.custom-dropdown-box');

      $dropdownBox.find('.custom-dropdown-content').toggleClass('open');
      $dropdownBox.find('.icon').toggleClass('open');
      $dropdownBox.find('.input').toggleClass('open');

      $('.custom-dropdown-box').not($dropdownBox).find('.custom-dropdown-content').removeClass('open');
      $('.custom-dropdown-box').not($dropdownBox).find('.icon').removeClass('open');
      $('.custom-dropdown-box').not($dropdownBox).find('.input').removeClass('open');
    });

    $('.custom-dropdown-content').on('click', function (e) {
      e.stopPropagation();
    });

    $(document).on('click', function () {
      $('.custom-dropdown-content').removeClass('open');
      $('.icon').removeClass('open');
      $('.input').removeClass('open');
    });
  });

  $(document).ready(function () {
    $(".user-dropdown-button").click(function () {
      $(".user-dropdown-content").toggleClass("open");
    });

    // Close dropdown when clicking outside
    $(document).click(function (event) {
      if (!$(event.target).closest(".user-dropdown").length) {
        $(".user-dropdown-content").removeClass("open");
      }
    });
  });


  // counter value related jquery
  $('.counter').each(function () {
    const $counter = $(this);
    const $count = $counter.find('.count');

    const defaultCount = parseInt($counter.data('default'), 10) || 0;
    const minCount = parseInt($counter.data('min'), 10) || 0;

    let count = defaultCount;
    $count.text(count);

    $counter.find('.plus').on('click', function () {
      count++;
      $count.text(count);
      updateSummary();
    });

    $counter.find('.minus').on('click', function () {
      if (count > minCount) {
        count--;
        $count.text(count);
        updateSummary();
      }
    });

    function updateSummary() {
      // Update Room count
      const roomCount = $('.counter[data-default][data-min]').first().find('.count').text();
      $('.room-count').text(roomCount).attr('data-value', roomCount);

      // Update Adult count
      const adultCount = $('.counter').eq(1).find('.count').text();
      $('.adult-count').text(adultCount).attr('data-value', adultCount);

      // Update Child count
      const childCount = $('.counter').eq(2).find('.count').text();
      $('.child-count').text(childCount).attr('data-value', childCount);
    }

    updateSummary();
  });

  //user dropdown
  $(document).ready(function () {
    $('.user-dropdown-btn').on('click', function (e) {
      e.stopPropagation();
      $(this).closest('.user-dropdown-full').find('.user-dropdown-content-2').toggleClass('active');
    });
    $(document).on('click', function () {
      $('.user-dropdown-content-2').removeClass('active');
    });
    $('.user-dropdown-content-2').on('click', function (e) {
      e.stopPropagation();
    });
  });

  // counter js
  $(document).ready(function () {
    let animated = false;

    function animateCounters() {
      $(".count").each(function () {
        var $this = $(this);
        var $number = $this.find(".count-number");
        var countTo = parseInt($this.attr("data-count"));

        $({ countNum: 0 }).animate(
          { countNum: countTo },
          {
            duration: 2000,
            easing: "swing",
            step: function () {
              let displayValue = Math.floor(this.countNum);
              if (countTo >= 1000) {
                displayValue = displayValue.toLocaleString();
              }
              $number.text(displayValue);
            },
            complete: function () {
              let finalValue = Math.floor(countTo);
              if (countTo >= 1000) {
                finalValue = finalValue.toLocaleString();
              }
              $number.text(finalValue);
            }
          }
        );
      });
    }

    const observer = new IntersectionObserver(
      function (entries) {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !animated) {
            animateCounters();
            animated = true;
          }
        });
      },
      {
        threshold: 0.4,
      }
    );

    const target = document.querySelector(".stats");
    if (target) observer.observe(target);
  });

  //testimonial custom
  $(document).ready(function () {
    // Cache DOM elements
    const $userReviews = $('.user-reviews .user-review');
    const $usersLists = $('.users-lists .users-list');
    const $testimonialBtns = $('.testimonial-btn');
    const $activeAlwaysReview = $('.user-reviews .active-always');
    const $activeAlwaysUser = $('.users-lists .active-always');
    const totalItems = $userReviews.length;
    let currentIndex = 1; // Start with second item (index 1)
    let isAnimating = false;

    // Initialize carousel
    function initCarousel() {
      updateActiveContent();
      updateButtonStates();
    }

    // Update active content
    function updateActiveContent() {
      // Get current active elements
      const $currentReview = $userReviews.eq(currentIndex);
      const $currentUser = $usersLists.eq(currentIndex);

      // Update active-always divs
      $activeAlwaysReview.empty().append($currentReview.clone());
      $activeAlwaysUser.empty().append($currentUser.clone());
    }

    // Update button states
    function updateButtonStates() {
      $testimonialBtns.removeClass('disabled');
      if (currentIndex === 0) {
        $('.left .testimonial-btn').addClass('disabled');
      }
      if (currentIndex === totalItems - 1) {
        $('.right .testimonial-btn').addClass('disabled');
      }
    }

    // Navigate to specific item with animation
    function navigateTo(direction) {
      if (isAnimating) return;
      isAnimating = true;

      // Calculate new index
      let newIndex = direction === 'next' ? currentIndex + 1 : currentIndex - 1;

      // Boundary check
      if (newIndex < 0 || newIndex >= totalItems) {
        isAnimating = false;
        return;
      }

      // Animation classes
      const outClass = direction === 'next' ? 'slide-out-left' : 'slide-out-right';
      const inClass = direction === 'next' ? 'slide-in-right' : 'slide-in-left';

      // Animate out current content
      $activeAlwaysReview.find('.user-review').addClass(outClass);
      $activeAlwaysUser.find('.users-list').addClass(outClass);

      setTimeout(() => {
        // Update index
        currentIndex = newIndex;

        // Update content
        updateActiveContent();

        // Animate in new content
        $activeAlwaysReview.find('.user-review').addClass(inClass);
        $activeAlwaysUser.find('.users-list').addClass(inClass);

        // Remove animation classes after animation completes
        setTimeout(() => {
          $activeAlwaysReview.find('.user-review').removeClass(inClass);
          $activeAlwaysUser.find('.users-list').removeClass(inClass);
          isAnimating = false;
        }, 300);

        // Update button states
        updateButtonStates();
      }, 300);
    }

    // Button click handlers
    $('.right .testimonial-btn').click(function () {
      if (!$(this).hasClass('disabled')) {
        navigateTo('next');
      }
    });

    $('.left .testimonial-btn').click(function () {
      if (!$(this).hasClass('disabled')) {
        navigateTo('prev');
      }
    });

    // Add animation CSS
    const style = document.createElement('style');
    style.textContent = `
    .user-review, .users-list {
      transition: transform 0.3s ease;
    }
    .slide-out-left {
      transform: translateX(-100%);
      opacity: 0;
    }
    .slide-out-right {
      transform: translateX(100%);
      opacity: 0;
    }
    .slide-in-right {
      transform: translateX(100%);
      opacity: 0;
    }
    .slide-in-left {
      transform: translateX(-100%);
      opacity: 0;
    }
    .active-always .user-review,
    .active-always .users-list {
      transform: translateX(0);
      opacity: 1;
    }
  `;
    document.head.appendChild(style);

    // Initialize
    initCarousel();
  });

  // Initialize gallery slider
  if ($(".main-slider").length > 0) {
    $('.main-slider').slick({
      slidesToShow: 1,
      slidesToScroll: 1,
      arrows: false,
      fade: true,
      asNavFor: '.nav-slider'
    });

    $('.nav-slider').slick({
      slidesToShow: 3,
      slidesToScroll: 1,
      asNavFor: '.main-slider',
      dots: false,
      arrows: false,
      centerMode: false,
      focusOnSelect: true,
      variableWidth: false,
      infinite: true,
      responsive: [
        {
          breakpoint: 768,
          settings: {
            slidesToShow: 3
          }
        },
        {
          breakpoint: 480,
          settings: {
            slidesToShow: 3
          }
        }
      ]
    });
  }
  // Initialize bootstrap tooltip
  document.addEventListener('DOMContentLoaded', function () {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.forEach(function (tooltipTriggerEl) {
      new bootstrap.Tooltip(tooltipTriggerEl);
    });
  });

  // Initialize upload file
  $(document).on('change', 'input[type="file"]', function (event) {
    var $file = $(this),
      $label = $file.next('label'),
      $labelText = $label.find('span:first'),
      $typeFileText = $label.find('.type-file-text'),
      labelDefault = "Upload Image";

    var fileName = $file.val().split('\\').pop(),
      file = event.target.files[0],
      fileType = file ? file.type.split('/')[0] : null,
      tmppath = file ? URL.createObjectURL(file) : null;

    if (fileName) {
      if (fileType === "image") {

        $label.addClass('file-ok').css('background-image', 'url(' + tmppath + ')');
      } else {

        $label.addClass('file-ok').css('background-image', 'none');
      }
      $labelText.text(fileName);
      $typeFileText.hide();
      $label.siblings('.file-upload-close').show();
    } else {
      resetUpload($file, $label, $labelText, $typeFileText,
        labelDefault);
    }
  });

  $(document).on('click', '.file-upload-close', function () {
    var $button = $(this),
      $uploadWrapper = $button.closest('.upload-custom-file'),
      $fileInput = $uploadWrapper.find('input[type="file"]'),
      $label = $fileInput.next('label'),
      $labelText = $label.find('span:first'),
      $typeFileText = $label.find('.type-file-text'),
      labelDefault = "Upload Image";

    resetUpload($fileInput, $label, $labelText, $typeFileText, labelDefault);
  });

  function resetUpload($fileInput, $label, $labelText, $typeFileText, labelDefault) {
    $fileInput.val('');
    $label.removeClass('file-ok').css('background-image', 'none');
    $labelText.text(labelDefault);
    $typeFileText.show();
    $label.siblings('.file-upload-close').hide();
  }
  // Initialize toster
  document.querySelectorAll('.td-alert-box .close-btn').forEach((btn) => {
    btn.addEventListener('click', function () {
      const alertBox = this.closest('.td-alert-box'); // Find the parent alert box
      alertBox.classList.add('hidden');
      setTimeout(() => {
        alertBox.style.display = 'none';
      }, 400); // Match the transition duration
    });
  });
})(jQuery);