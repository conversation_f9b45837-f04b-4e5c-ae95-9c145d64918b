@use '../utils' as *;

/*----------------------------------------*/
/*  fix design
/*----------------------------------------*/
.seperate-div {
    border-color: rgba(0, 0, 0, 0.1) !important;
}

.testimonial-box {
    .slider-container-2 {
        width: 100%;
        margin: 0 auto;

        .main-slider-2 {
            display: flex;
            align-items: center;
            gap: 100px;

            @media #{$md} {
                gap: 50px;
            }

            @media #{$xs} {
                gap: 10px;
            }

            @media #{$sm} {
                gap: 40px;
            }

            .slick-slide {
                display: flex;
                flex-direction: column;
                align-items: center;
                text-align: center;
                min-height: 120px;
                overflow: hidden;

                .star {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    margin-bottom: 20px;
                }

                p {
                    text-align: center;
                }
            }
        }
    }

    .nav-slider-2 {
        min-height: 100px;
        max-width: 265px;
        margin: 0 auto;
    }

    .nav-slider-2 div {
        padding: 5px;
        display: flex;
        align-items: center;
    }

    .nav-slider-2 img {
        width: 60px;
        height: 60px;
        flex-shrink: 0;
        object-fit: cover;
        border-radius: 50%;
        margin: 0 auto;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    /* Active thumbnail style */
    .slick-current img {
        width: 75px !important;
        height: 75px !important;
        border: 3px solid var(--td-primary) !important;
    }

    .main-slider-2 {
        .slick-current img {
            width: unset!important;
            height: unset!important;
            border: none !important;
        }
    }
}

.slick-arrow.custom-arrow {
    width: 40px;
    height: 40px;
    background: var(--td-primary);
    display: flex !important;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    z-index: 10;
    cursor: pointer;

    &:before {
        display: none;
    }

    svg {
        width: 20px;
        height: 20px;
        fill: white;
    }
}