@use '../utils' as *;

/*----------------------------------------*/
/*  buttons
/*----------------------------------------*/
.primary-button {
  height: 40px;
  padding: 10px 24px;
  background: var(--td-primary);
  border: 1px solid var(--td-primary);
  color: var(--td-white);
  font-size: rem(14);
  font-weight: 400;
  line-height: normal;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease-in-out;
  font-family: var(--td-heading-font);

  @media #{$xs} {
    height: 34px;
    padding: 10px 20px;
  }

  &:hover {
    background: transparent;
    border: 1px solid var(--td-primary);
    color: var(--td-heading);
  }

  &.white-hover {
    &:hover {
      background: transparent;
      border: 1px solid var(--td-white);
      color: var(--td-white);
    }
  }

  &.primary-hover {
    &:hover {
      background: transparent;
      border: 1px solid var(--td-primary);
      color: var(--td-primary);
    }
  }

  &.border-btn {
    background: transparent;
    border: 1px solid var(--td-primary);
    color: var(--td-heading);

    &:hover {
      background: var(--td-primary);
      border: 1px solid var(--td-primary);
      color: var(--td-white);
    }
  }

  &.xl-btn {
    height: 50px;
    padding: 10px 26px;
    font-size: rem(16);

    @media #{$xs} {
      height: 40px;
      padding: 10px 24px;
    }
  }

  &.lg-btn {
    height: 52px;
    padding: 12px 32px;
    display: inline-flex;

    @media #{$xs} {
      height: 42px;
      padding: 10px 24px;
    }
  }

  &.xxl-btn {
    height: 54px;
    padding: 10px 26px;
    color: var(--td-white);
    text-align: center;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  &.px {
    padding: 12px 20px;
  }
}

.ternary-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--td-text-secondary);
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  transition: all 0.3s ease-in-out;
  font-family: var(--td-heading-font);

  @media #{$lg,$md,$xs} {
    font-size: 16px;
  }

  svg {
    transform: translateY(2px);
  }

  &:hover {
    color: var(--td-primary);

    svg {
      path {
        fill: var(--td-primary);
      }
    }
  }
}

// underline btn
.underline-btn {
  font-weight: 500;
  position: relative;
  color: var(--td-primary);

  &::after {
    content: "";
    position: absolute;
    height: 1px;
    transition: .3s;
    inset-inline-start: auto;
    bottom: -2px;
    background: var(--td-primary);
    width: 0;
    inset-inline-end: 0;
  }

  &:hover {
    color: var(--td-primary);

    &::after {
      width: 100%;
      inset-inline-start: 0;
      inset-inline-end: auto;
    }
  }
}