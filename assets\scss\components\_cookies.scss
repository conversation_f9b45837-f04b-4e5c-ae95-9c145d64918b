@use "../utils" as *;

/*----------------------------------------*/
/* cookies style
/*----------------------------------------*/
.caches-privacy {
    max-width: 1060px;
    position: fixed;
    bottom: 20px;
    inset-inline-start: 50%;
    transform: translateX(-50%);
    row-gap: 12px;
    column-gap: 12px;
    border: 1px solid #FFF4E5;
    background: #FFF4E5;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px 24px 20px 24px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 90%;
    z-index: 111;
    transition: 0.3s;

    @include rtl {
        inset-inline-start: auto;
        inset-inline-end: 50%;
    }

    @media #{$xs,$sm} {
        flex-direction: column;
        align-items: self-start;
    }

    @media #{$xxs} {
        padding: 12px 16px 12px 16px;

        .caches-btns {
            .td-btn {
                font-size: 12px;
            }
        }
    }

    .caches-contents {
        display: flex;
        align-items: center;
        gap: 14px;

        @media #{$xxs} {
            flex-direction: column;
            align-items: self-start;
        }

        img {
            height: 35px;
            flex: 0 0 auto;
        }

        .title {
            font-size: 16px;
            margin-bottom: 8px;
            color: var(--td-white);
            font-weight: 600;

            @media #{$xs} {
                font-size: 18px;
            }
        }

        p {
            font-size: 16px;
            margin-bottom: 0;

            a {
                color: $primary;
            }
        }
    }

    .caches-btns {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex: 0 0 auto;
        flex-wrap: wrap;
        gap: 12px;
    }
}