@use '../../utils' as *;

/*----------------------------------------*/
/*  5.1 footer-1
/*----------------------------------------*/
.footer-1 {
    .main-footer {
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;
        position: relative;

        &::after {
            position: absolute;
            top: 0;
            inset-inline-start: 0;
            width: 100%;
            height: 100%;
            content: '';
            background: rgba(21, 20, 21, 0.78);
            z-index: 1;
        }

        .full-footer {
            display: flex;
            align-items: start;
            gap: 135px;
            position: relative;
            z-index: 2;

            @media #{$md,$xs} {
                flex-direction: column;
                align-items: start;
            }

            @media #{$xl} {
                gap: 100px;
            }

            @media #{$lg,$md} {
                gap: 50px;
            }

            @media #{$xs} {
                gap: 40px;
            }

            @media #{$sm} {
                gap: 50px;
            }

            .left {
                width: 30%;

                @media #{$xl} {
                    width: 25%;
                }

                @media #{$lg} {
                    width: 20%;
                }

                @media #{$md,$xs} {
                    width: 100%;
                }

                .logo {
                    margin-bottom: 30px;

                    @media #{$md,$xs} {
                        margin-bottom: 14px;
                    }

                    a {
                        display: inline-block;
                        height: 40px;

                        img {
                            height: 100%;
                        }
                    }
                }

                .description {
                    color: #E3E3E3;
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: lh(26, 16);
                }

                .social-buttons {
                    margin-top: 30px;
                    display: flex;
                    align-items: center;
                    gap: 16px;

                    @media #{$md,$xs} {
                        margin-top: 14px;
                    }

                    a {
                        display: inline-flex;
                        justify-content: center;
                        align-items: center;
                        width: 24px;
                        height: 24px;
                        flex-shrink: 0;
                        transition: all 0.3s ease-in-out;

                        .social-icon {
                            font-size: 24px;
                            color: var(--td-white);
                        }

                        &:hover {

                            .social-icon {
                                color: var(--td-primary);
                            }
                        }
                    }
                }
            }

            .right {
                width: 70%;
                display: grid;
                grid-template-columns: repeat(3, 1fr) 235px;
                gap: 20px;

                @media #{$xl,$lg,$md,$xs} {
                    width: 65%;
                }

                @media #{$lg} {
                    width: 80%;
                }

                @media #{$md,$xs} {
                    width: 100%;
                }

                @media #{$xs} {
                    grid-template-columns: repeat(1, 1fr);
                    gap: 30px;
                }

                @media #{$sm} {
                    grid-template-columns: repeat(2, 1fr);
                    gap: 40px;
                }

                .footer-link-list {
                    h5 {
                        color: var(--td-white);
                        font-size: 18px;
                        font-weight: 400;
                        line-height: lh(24, 18);
                        text-transform: capitalize;
                        margin-bottom: 38px;

                        @media #{$xl,$lg,$md} {
                            margin-bottom: 26px;
                        }

                        @media #{$xs} {
                            margin-bottom: 18px;
                        }
                    }

                    ul {
                        list-style-type: none;

                        li {
                            margin-bottom: 16px;

                            @media #{$xl,$lg,$md} {
                                margin-bottom: 14px;
                            }

                            @media #{$xs} {
                                margin-bottom: 5px;
                            }

                            @media #{$sm} {
                                margin-bottom: 10px;
                            }

                            &:last-child {
                                margin-bottom: 0;
                            }

                            a {
                                color: #E3E3E3;
                                font-family: var(--td-body-font);
                                font-size: 16px;
                                font-style: normal;
                                font-weight: 400;
                                line-height: lh(24, 16);
                                transition: all 0.3s ease-in-out;
                                display: flex;
                                align-items: start;
                                gap: 10px;

                                .contact-icon-box {
                                    display: flex;
                                    justify-content: center;
                                    align-items: center;
                                    transform: translateY(2px);

                                    .contact-icon {
                                        font-size: 20px;
                                    }
                                }



                                &:hover {
                                    color: var(--td-primary);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .bottom-footer {
        background-color: var(--td-heading);
        padding: 26px 0;

        @media #{$xl,$lg,$md,$xs} {
            padding: 20px 0;
        }


        .bottom-footer-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 10px;

            @media #{$xs} {
                flex-direction: column;
                align-items: start;
                gap: 10px;
            }

            .left {

                p {
                    color: var(--td-white);
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: lh(24, 14);
                }
            }

            .right {
                display: flex;
                align-items: center;
                gap: 24px;

                @media #{$xs} {
                    gap: 10px;
                }

                a {
                    color: var(--td-white);
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: lh(24, 14);
                    transition: all 0.3s ease-in-out;

                    &:hover {
                        color: var(--td-primary);
                    }
                }
            }
        }

    }
}