<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>Home || Welcome to Hotelib Hotel, where luxury meets the skyline.</title>
    <meta name="description" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Place favicon.ico in the root directory -->
    <link rel="shortcut icon" type="image/x-icon" href="../assets/images/favicon.svg">
    <style>
        /* Google Font */
        @import url('https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,100..900;1,100..900&family=Marcellus&display=swap');

        /* Root Variables & Typography */
        :root {
            --font-base: 16px;
            --line-height-base: 1.6;
            --font-ratio: 1.25;
            --font-body: "Jost", sans-serif;
            --font-heading: "Marcellus", serif;
            --color-heading: #151415;
            --color-primary: #AA8453;
            --color-body: #444344;
            --color-white: #fff;
        }

        /* Reset Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* Base Body Styles */
        body {
            font-family: var(--font-body);
            font-size: var(--font-base);
            line-height: calc(var(--font-base) * var(--line-height-base));
            color: var(--color-body);
            background-color: hsl(0deg 0% 95.28%);
        }

        /* Typography Styles */
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            margin: 0 0 1rem 0;
            color: var(--font-heading);
            line-height: 1.2;
            word-break: break-word;
            font-weight: 400;
            color: var(--color-heading);
        }

        /* Headings */
        h1 {
            font-size: clamp(1.5rem, 5vw, 1.75rem);
            line-height: clamp(2rem, 6vw, 2.5rem);
        }

        h2 {
            font-size: clamp(1.25rem, 4.5vw, 1.5rem);
            line-height: clamp(1.8rem, 5.5vw, 2.125rem);
        }

        h3 {
            font-size: clamp(1rem, 4vw, 1.25rem);
            line-height: clamp(1.5rem, 5vw, 1.875rem);
        }

        h4 {
            font-size: clamp(0.9375rem, 3.5vw, 1.125rem);
            line-height: clamp(1.375rem, 4.5vw, 1.625rem);
        }

        h5 {
            font-size: clamp(0.875rem, 3vw, 1rem);
            line-height: clamp(1.25rem, 4vw, 1.5rem);
        }

        h6 {
            font-size: clamp(0.8125rem, 2.5vw, 0.875rem);
            line-height: clamp(1.125rem, 3.5vw, 1.25rem);
        }

        /* Paragraph */
        p {
            font-size: 16px;
            line-height: 1.5;
            margin-bottom: 16px;
        }

        /* Small Text */
        small {
            font-size: 14px;
            line-height: 22px;
        }

        img {
            max-width: 100%;
            object-fit: cover;
        }

        .email-container {
            max-width: 640px;
            background: var(--color-white);
            margin: 15px auto;
        }

        /* Header */
        .header {
            padding: 20px 30px 20px;
            position: relative;
        }

        .header img {
            height: 33px;
        }

        /* Hero css */
        .hero {
            padding: 0px 32px 20px;
            position: relative;
        }

        .hero .thumb {
            margin-bottom: 20px;
        }

        .hero .thumb img {
            width: 100%;
        }

        /* Features list */
        .features {
            padding: 0px 30px 30px;
        }

        .features-list {
            text-align: left;
            display: flex;
            flex-direction: column;
            gap: 4px;
            margin-bottom: 30px;
        }

        .feature-item {
            display: flex;
            align-items: flex-start;
        }

        .feature-icon {
            width: 20px;
            min-width: 20px;
            height: 20px;
            border-radius: 50%;
            color: var(--color-white);
            text-align: center;
            line-height: 20px;
            font-size: 12px;
            margin-right: 12px;
            margin-top: 5px;
        }

        .feature-text {
            flex: 1;
        }

        .feature-title {
            font-weight: 500;
            margin-bottom: 5px;
            margin-top: 0;
        }

        .feature-description {
            font-size: 15px;
            color: #4a4a4a;
            margin: 0;
        }

        .primary-button {
            height: 50px;
            padding: 10px 30px;
            background: var(--color-primary);
            border: 1px solid var(--color-primary);
            font-size: 0.875rem;
            font-weight: 400;
            line-height: normal;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease-in-out;
            color: var(--color-white);
            text-decoration: none;
        }

        .primary-button:hover {
            background: transparent;
            border: 1px solid var(--color-primary);
            color: var(--td-heading);
        }

        .primary-button.white-hover:hover {
            background: transparent;
            border: 1px solid var(--td-white);
            color: var(--td-white);
        }

        .primary-button.primary-hover:hover {
            background: transparent;
            border: 1px solid var(--color-primary);
            color: var(--color-primary);
        }

        .primary-button.border-btn {
            background: transparent;
            border: 1px solid var(--color-primary);
            color: var(--td-heading);
        }

        .primary-button.border-btn:hover {
            background: var(--color-primary);
            border: 1px solid var(--color-primary);
            color: var(--td-white);
        }

        /* Footer Styles */
        .footer {
            text-align: center;
            padding: 0px 30px 30px;
        }

        .footer p {
            max-width: 25.75rem;
            margin: 20px auto 0;
            text-align: center;
        }

        /* Footer Links */
        .footer-links {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
            gap: 0.75rem 1rem;
        }

        .footer-links a {
            display: inline-flex;
            align-items: center;
            text-decoration: underline;
            font-size: 16px;
            color: #444344;
        }

        .footer-links a svg:hover {
            stroke: var(--color-primary);
        }

        .footer-links a:hover {
            text-decoration: none;
        }

        /* Social Icons */
        .social-icons {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin: 0 auto;
            margin-bottom: 20px;
        }

        .social-icons a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 2rem;
            height: 2rem;
            color: #4a5cff;
            transition: color 0.3s ease;
        }

        .social-icons a svg *:hover {
            color: var(--color-primary);
        }
    </style>
</head>

<body>
    <div class="email-container">
        <!-- header -->
        <div class="header">
            <a href="index.html">
                <img src="../assets/images/logo/logo.svg" alt="Header Logo">
            </a>
        </div>
        <!-- Hero -->
        <div class="hero">
            <div class="thumb">
                <img src="assets/hero-thumb.png" alt="Hero Thumb">
            </div>
            <h1>Welcome to Hotelib- Elevate Your Stay At City
                Skyline Hotel!</h1>
            <p>
                A serene and beautifully landscaped rooftop oasis offering panoramic 360-degree views of the city
                skyline.
            </p>
            <p>
                Perfect for relaxation, evening strolls, or hosting exclusive outdoor
                events while taking in the stunning urban backdrop
            </p>
        </div>
        <!-- features -->
        <div class="features">
            <h2>Features</h2>
            <div class="features-list">
                <div class="feature-item">
                    <div class="feature-icon">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_261_916)">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M9.9987 0.833313C4.9362 0.833313 0.832031 4.93748 0.832031 9.99998C0.832031 15.0625 4.9362 19.1666 9.9987 19.1666C15.0612 19.1666 19.1654 15.0625 19.1654 9.99998C19.1654 4.93748 15.0612 0.833313 9.9987 0.833313ZM13.972 8.44998C14.0452 8.36635 14.1009 8.26893 14.1359 8.16345C14.1708 8.05798 14.1843 7.94657 14.1756 7.8358C14.1669 7.72503 14.1361 7.61712 14.085 7.51842C14.034 7.41972 13.9637 7.33223 13.8783 7.2611C13.793 7.18996 13.6942 7.13661 13.5879 7.1042C13.4817 7.07178 13.37 7.06095 13.2594 7.07234C13.1489 7.08372 13.0418 7.1171 12.9443 7.17051C12.8469 7.22391 12.7611 7.29627 12.692 7.38331L9.1087 11.6825L7.25453 9.82748C7.09736 9.67568 6.88686 9.59169 6.66836 9.59358C6.44987 9.59548 6.24086 9.68312 6.08635 9.83763C5.93184 9.99214 5.8442 10.2011 5.8423 10.4196C5.8404 10.6381 5.9244 10.8486 6.0762 11.0058L8.5762 13.5058C8.65808 13.5876 8.75611 13.6515 8.86404 13.6934C8.97198 13.7352 9.08745 13.7541 9.20309 13.7489C9.31873 13.7436 9.43201 13.7143 9.53571 13.6629C9.6394 13.6114 9.73124 13.5389 9.80536 13.45L13.972 8.44998Z"
                                    fill="#22C55E" />
                            </g>
                            <defs>
                                <clipPath id="clip0_xzx261_916">
                                    <rect width="20" height="20" fill="white" />
                                </clipPath>
                            </defs>
                        </svg>
                    </div>
                    <div class="feature-text">
                        <p class="feature-title">Panoramic 360-degree city views </p>
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_261_916)">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M9.9987 0.833313C4.9362 0.833313 0.832031 4.93748 0.832031 9.99998C0.832031 15.0625 4.9362 19.1666 9.9987 19.1666C15.0612 19.1666 19.1654 15.0625 19.1654 9.99998C19.1654 4.93748 15.0612 0.833313 9.9987 0.833313ZM13.972 8.44998C14.0452 8.36635 14.1009 8.26893 14.1359 8.16345C14.1708 8.05798 14.1843 7.94657 14.1756 7.8358C14.1669 7.72503 14.1361 7.61712 14.085 7.51842C14.034 7.41972 13.9637 7.33223 13.8783 7.2611C13.793 7.18996 13.6942 7.13661 13.5879 7.1042C13.4817 7.07178 13.37 7.06095 13.2594 7.07234C13.1489 7.08372 13.0418 7.1171 12.9443 7.17051C12.8469 7.22391 12.7611 7.29627 12.692 7.38331L9.1087 11.6825L7.25453 9.82748C7.09736 9.67568 6.88686 9.59169 6.66836 9.59358C6.44987 9.59548 6.24086 9.68312 6.08635 9.83763C5.93184 9.99214 5.8442 10.2011 5.8423 10.4196C5.8404 10.6381 5.9244 10.8486 6.0762 11.0058L8.5762 13.5058C8.65808 13.5876 8.75611 13.6515 8.86404 13.6934C8.97198 13.7352 9.08745 13.7541 9.20309 13.7489C9.31873 13.7436 9.43201 13.7143 9.53571 13.6629C9.6394 13.6114 9.73124 13.5389 9.80536 13.45L13.972 8.44998Z"
                                    fill="#22C55E" />
                            </g>
                            <defs>
                                <clipPath id="clixxzaswp0_261_916">
                                    <rect width="20" height="20" fill="white" />
                                </clipPath>
                            </defs>
                        </svg>
                    </div>
                    <div class="feature-text">
                        <p class="feature-title">Event space for private gatherings</p>
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_261_916)">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M9.9987 0.833313C4.9362 0.833313 0.832031 4.93748 0.832031 9.99998C0.832031 15.0625 4.9362 19.1666 9.9987 19.1666C15.0612 19.1666 19.1654 15.0625 19.1654 9.99998C19.1654 4.93748 15.0612 0.833313 9.9987 0.833313ZM13.972 8.44998C14.0452 8.36635 14.1009 8.26893 14.1359 8.16345C14.1708 8.05798 14.1843 7.94657 14.1756 7.8358C14.1669 7.72503 14.1361 7.61712 14.085 7.51842C14.034 7.41972 13.9637 7.33223 13.8783 7.2611C13.793 7.18996 13.6942 7.13661 13.5879 7.1042C13.4817 7.07178 13.37 7.06095 13.2594 7.07234C13.1489 7.08372 13.0418 7.1171 12.9443 7.17051C12.8469 7.22391 12.7611 7.29627 12.692 7.38331L9.1087 11.6825L7.25453 9.82748C7.09736 9.67568 6.88686 9.59169 6.66836 9.59358C6.44987 9.59548 6.24086 9.68312 6.08635 9.83763C5.93184 9.99214 5.8442 10.2011 5.8423 10.4196C5.8404 10.6381 5.9244 10.8486 6.0762 11.0058L8.5762 13.5058C8.65808 13.5876 8.75611 13.6515 8.86404 13.6934C8.97198 13.7352 9.08745 13.7541 9.20309 13.7489C9.31873 13.7436 9.43201 13.7143 9.53571 13.6629C9.6394 13.6114 9.73124 13.5389 9.80536 13.45L13.972 8.44998Z"
                                    fill="#22C55E" />
                            </g>
                            <defs>
                                <clipPath id="clipczcz_261_916">
                                    <rect width="20" height="20" fill="white" />
                                </clipPath>
                            </defs>
                        </svg>
                    </div>
                    <div class="feature-text">
                        <p class="feature-title">Cozy seating areas for relaxation</p>
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M9.9987 0.833313C4.9362 0.833313 0.832031 4.93748 0.832031 9.99998C0.832031 15.0625 4.9362 19.1666 9.9987 19.1666C15.0612 19.1666 19.1654 15.0625 19.1654 9.99998C19.1654 4.93748 15.0612 0.833313 9.9987 0.833313ZM13.972 8.44998C14.0452 8.36635 14.1009 8.26893 14.1359 8.16345C14.1708 8.05798 14.1843 7.94657 14.1756 7.8358C14.1669 7.72502 14.1361 7.61712 14.085 7.51842C14.034 7.41972 13.9637 7.33223 13.8783 7.2611C13.793 7.18996 13.6942 7.13661 13.5879 7.1042C13.4817 7.07178 13.37 7.06095 13.2594 7.07234C13.1489 7.08372 13.0418 7.1171 12.9443 7.17051C12.8469 7.22391 12.7611 7.29627 12.692 7.38331L9.1087 11.6825L7.25453 9.82748C7.09736 9.67568 6.88686 9.59169 6.66836 9.59358C6.44987 9.59548 6.24086 9.68312 6.08635 9.83763C5.93184 9.99214 5.8442 10.2011 5.8423 10.4196C5.8404 10.6381 5.9244 10.8486 6.0762 11.0058L8.5762 13.5058C8.65808 13.5876 8.75611 13.6515 8.86404 13.6934C8.97198 13.7352 9.08745 13.7541 9.20309 13.7489C9.31873 13.7436 9.43201 13.7143 9.53571 13.6629C9.6394 13.6114 9.73124 13.5389 9.80536 13.45L13.972 8.44998Z"
                                fill="#22C55E" />
                        </svg>

                    </div>
                    <div class="feature-text">
                        <p class="feature-title">24/7 Customer Support</p>
                    </div>
                </div>
            </div>
            <div class="features-btn center">
                <a href="#" class="primary-button">Get Started</a>
            </div>
        </div>
        <!-- footer -->
        <div class="footer">
            <h2>Hotelib in touch</h2>
            <div class="social-icons">
                <a href="#">
                    <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M14.832 1.66669H6.4987C4.19751 1.66669 2.33203 3.53217 2.33203 5.83335V14.1667C2.33203 16.4679 4.19751 18.3334 6.4987 18.3334H14.832C17.1332 18.3334 18.9987 16.4679 18.9987 14.1667V5.83335C18.9987 3.53217 17.1332 1.66669 14.832 1.66669Z"
                            stroke="#444344" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" />
                        <path
                            d="M15.2478 5.41669H15.2561M13.9978 9.47502C14.1006 10.1686 13.9822 10.8769 13.6593 11.4992C13.3364 12.1215 12.8254 12.6262 12.1992 12.9414C11.5729 13.2566 10.8632 13.3664 10.171 13.255C9.47874 13.1436 8.83927 12.8168 8.3435 12.321C7.84773 11.8252 7.52091 11.1857 7.40953 10.4935C7.29814 9.80131 7.40786 9.0916 7.72307 8.46533C8.03829 7.83907 8.54296 7.32814 9.16529 7.00522C9.78762 6.68231 10.4959 6.56384 11.1895 6.66669C11.8969 6.77159 12.5518 7.10124 13.0575 7.60694C13.5632 8.11264 13.8929 8.76758 13.9978 9.47502Z"
                            stroke="#444344" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>

                </a>
                <a href="#">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M10.8986 8.92667L6.9103 3.335C6.86404 3.27004 6.80292 3.21708 6.73203 3.18055C6.66114 3.14401 6.58254 3.12497 6.5028 3.125H4.3978C4.30605 3.12512 4.2161 3.15048 4.1378 3.19831C4.0595 3.24613 3.99587 3.31458 3.95388 3.39616C3.91189 3.47773 3.89315 3.56929 3.89972 3.6608C3.90629 3.75232 3.93791 3.84026 3.99113 3.915L9.09696 11.0733M10.8986 8.92667L16.0045 16.085C16.0577 16.1597 16.0893 16.2477 16.0959 16.3392C16.1024 16.4307 16.0837 16.5223 16.0417 16.6038C15.9997 16.6854 15.9361 16.7539 15.8578 16.8017C15.7795 16.8495 15.6895 16.8749 15.5978 16.875H13.4928C13.413 16.875 13.3344 16.856 13.2636 16.8195C13.1927 16.7829 13.1316 16.73 13.0853 16.665L9.09696 11.0733M10.8986 8.92667L15.767 3.125M9.09696 11.0733L4.22863 16.875"
                            stroke="#444344" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>

                </a>
                <a href="#">
                    <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M13.6706 6.66669C14.9967 6.66669 16.2684 7.19347 17.2061 8.13115C18.1438 9.06884 18.6706 10.3406 18.6706 11.6667V17.5H15.3372V11.6667C15.3372 11.2247 15.1616 10.8007 14.8491 10.4882C14.5365 10.1756 14.1126 10 13.6706 10C13.2285 10 12.8046 10.1756 12.4921 10.4882C12.1795 10.8007 12.0039 11.2247 12.0039 11.6667V17.5H8.67057V11.6667C8.67057 10.3406 9.19736 9.06884 10.135 8.13115C11.0727 7.19347 12.3445 6.66669 13.6706 6.66669ZM2.00391 7.50002H5.33724V17.5H2.00391V7.50002Z"
                            stroke="#444344" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" />
                        <path
                            d="M3.67057 5.00002C4.59105 5.00002 5.33724 4.25383 5.33724 3.33335C5.33724 2.41288 4.59105 1.66669 3.67057 1.66669C2.7501 1.66669 2.00391 2.41288 2.00391 3.33335C2.00391 4.25383 2.7501 5.00002 3.67057 5.00002Z"
                            stroke="#444344" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                </a>
            </div>
            <div class="footer-links">
                <a href="#">Terms and Conditions</a>
                <a href="#">Privacy and Policies</a>
                <a href="#">Cookies Settings</a>
            </div>
            <div class="footer-copyright">
                <p>© 2025 Hotelib Hotel. All rights reserved.</p>
            </div>
        </div>
    </div>
</body>

</html>