@use '../../utils' as *;

/*----------------------------------------*/
/*  gallery
/*----------------------------------------*/
.gallery {
  width: 100%;

  .gallery-content {
    display: flex;
    gap: 30px;
    width: 100%;

    @media #{$xxl,$xl,$lg,$md,$xs} {
      gap: 20px;
    }

    @media #{$md,$xs} {
      flex-direction: column;
    }

    .left {
      width: 25%;
      position: relative;
      transition: all 0.3s ease-in-out;

      @media #{$md,$xs} {
        width: 100%;
      }

      .left-img {
        width: 100%;
        height: 780px;
        position: relative;

        @media #{$xxl,$xl} {
          height: 700px;
        }

        @media #{$lg,$md,$xs} {
          height: 500px;
        }

        @media #{$xs} {
          height: 300px;
        }

        @media #{$sm} {
          height: 500px;
        }

        &::before {
          content: '';
          position: absolute;
          top: 50%;
          inset-inline-start: 50%;
          transform: translate(-50%, -50%);
          width: 88%;
          height: 94%;
          background: rgba(0, 0, 0, 0.0);
          border: 1px solid rgba(255, 255, 255, 0.4);
          z-index: 1;

          @include rtl {
            inset-inline-start: auto;
            inset-inline-end: 50%;
          }

          @media #{$md,$xs} {
            width: 93%;
            height: 88%;
          }
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .hotel-view {
        position: absolute;
        top: 50%;
        inset-inline-start: 50%;
        transform: translate(-50%, -50%) scale(0.8);
        z-index: 2;
        width: 77%;
        height: 88%;
        background: rgba(0, 0, 0, 0.30);
        backdrop-filter: blur(5px);
        display: flex;
        justify-content: center;
        align-items: center;
        visibility: hidden;
        opacity: 0;
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

        @include rtl {
          inset-inline-start: auto;
          inset-inline-end: 50%;
        }

        @media #{$md,$xs} {
          width: 87%;
          height: 78%;
        }

        a {
          color: var(--td-white);
          text-align: center;
          font-family: var(--td-heading-font);
          font-size: 30px;
          font-weight: 400;
          line-height: normal;

          @media #{$lg,$md} {
            font-size: 20px;
          }

          @media #{$xs} {
            font-size: 16px;
          }

          @media #{$sm} {
            font-size: 18px;
          }
        }
      }

      &:hover {
        .hotel-view {
          visibility: visible;
          opacity: 1;
          transform: translate(-50%, -50%) scale(1);
        }
      }
    }

    .middle {
      width: 40%;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 30px;
      place-content: center;

      @media #{$xxl,$xl,$lg,$md,$xs} {
        gap: 20px;
      }

      @media #{$md,$xs} {
        width: 100%;
      }

      .image-middle {
        position: relative;
        transition: all 0.3s ease-in-out;

        .square-img {
          width: 100%;
          height: 375px;
          position: relative;

          @media #{$xxl,$xl} {
            height: 340px;
          }

          @media #{$lg,$md,$xs} {
            height: 240px;
          }

          @media #{$xs} {
            height: 150px;
          }

          @media #{$sm} {
            height: 240px;
          }

          &::before {
            content: '';
            position: absolute;
            top: 50%;
            inset-inline-start: 50%;
            transform: translate(-50%, -50%);
            width: 90%;
            height: 90%;
            background: rgba(0, 0, 0, 0.0);
            border: 1px solid rgba(255, 255, 255, 0.4);
            z-index: 1;

            @include rtl {
              inset-inline-start: auto;
              inset-inline-end: 50%;
            }
          }

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .hotel-view {
          position: absolute;
          top: 50%;
          inset-inline-start: 50%;
          transform: translate(-50%, -50%) scale(0.8);
          z-index: 2;
          width: 78%;
          height: 78%;
          background: rgba(0, 0, 0, 0.30);
          backdrop-filter: blur(5px);
          display: flex;
          justify-content: center;
          align-items: center;
          visibility: hidden;
          opacity: 0;
          transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

          @include rtl {
            inset-inline-start: auto;
            inset-inline-end: 50%;
          }

          a {
            color: var(--td-white);
            text-align: center;
            font-family: var(--td-heading-font);
            font-size: 30px;
            font-weight: 400;
            line-height: normal;

            @media #{$lg,$md} {
              font-size: 20px;
            }

            @media #{$xs} {
              font-size: 16px;
            }

            @media #{$sm} {
              font-size: 18px;
            }
          }
        }

        &:hover {
          .hotel-view {
            visibility: visible;
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
          }
        }
      }
    }

    .right {
      width: 35%;
      position: relative;
      transition: all 0.3s ease-in-out;

      @media #{$md,$xs} {
        width: 100%;
      }

      .right-img {
        width: 100%;
        height: 780px;
        position: relative;

        @media #{$xxl,$xl} {
          height: 700px;
        }

        @media #{$lg,$md,$xs} {
          height: 500px;
        }

        @media #{$xs} {
          height: 300px;
        }

        @media #{$sm} {
          height: 500px;
        }

        &::before {
          content: '';
          position: absolute;
          top: 50%;
          inset-inline-start: 50%;
          transform: translate(-50%, -50%);
          width: 92%;
          height: 94%;
          background: rgba(0, 0, 0, 0.0);
          border: 1px solid rgba(255, 255, 255, 0.4);
          z-index: 1;

          @include rtl {
            inset-inline-start: auto;
            inset-inline-end: 50%;
          }

          @media #{$md,$xs} {
            width: 93%;
            height: 88%;
          }
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .hotel-view {
        position: absolute;
        top: 50%;
        inset-inline-start: 50%;
        transform: translate(-50%, -50%) scale(0.8);
        z-index: 2;
        width: 83%;
        height: 87%;
        background: rgba(0, 0, 0, 0.30);
        backdrop-filter: blur(5px);
        display: flex;
        justify-content: center;
        align-items: center;
        visibility: hidden;
        opacity: 0;
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

        @media #{$md,$xs} {
          width: 87%;
          height: 78%;
        }

        @include rtl {
          inset-inline-start: auto;
          inset-inline-end: 50%;
        }

        a {
          color: var(--td-white);
          text-align: center;
          font-family: var(--td-heading-font);
          font-size: 30px;
          font-weight: 400;
          line-height: normal;

          @media #{$lg,$md} {
            font-size: 20px;
          }

          @media #{$xs} {
            font-size: 16px;
          }

          @media #{$sm} {
            font-size: 18px;
          }
        }
      }

      &:hover {
        .hotel-view {
          visibility: visible;
          opacity: 1;
          transform: translate(-50%, -50%) scale(1);
        }
      }
    }
  }
}