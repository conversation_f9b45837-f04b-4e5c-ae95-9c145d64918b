@use '../../utils' as *;

/*----------------------------------------*/
/*  Restaurant styles
/*----------------------------------------*/
.our-restaurant-contents {
    .heading-block {
        margin-bottom: 30px;

        .meta {
            display: flex;
            align-items: center;
            gap: 4px;

            span {
                flex: 0 0 auto;

                img {
                    width: 18px;
                }
            }
        }
    }
}

.restaurant-open-schedule {
    border: 1px solid var(--td-primary);
    background: #FFF4E5;
    display: inline-flex;
    align-items: center;

    @media #{$xxs} {
        display: flex;
        flex-direction: column;
    }

    .time-block {
        padding: 20px 64px;
        position: relative;

        @media #{$xs} {
            padding: 20px 40px;
            width: 100%;
        }

        &:not(:last-child) {
            &::after {
                position: absolute;
                content: "";
                height: calc(100% - 40px);
                inset-inline-end: 0;
                top: 50%;
                transform: translateY(-50%);
                background: #E6DCCE;
                width: 1px;
                @media #{$xxs} {
                    height: 1px;
                    inset-inline-end: 0;
                    top: auto;
                    bottom: 0;
                    width: 100%;
                    transform: inherit;
                }
            }
        }

        .time {
            font-size: rem(24);
            color: #151415;
        }

        .status {
            margin-top: 2px;
            display: block;
        }
    }
}