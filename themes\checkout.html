<!DOCTYPE html>
<html class="no-js" lang="zxx">

<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>Checkout || Welcome to Hotelib Hotel, where luxury meets the skyline.</title>
    <meta name="description" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- Place favicon.ico in the root directory -->
    <link rel="shortcut icon" type="image/x-icon" href="../assets/images/favicon.svg">
    <!-- CSS here -->
    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="../assets/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/flag-icon.css">
    <link rel="stylesheet" href="../assets/css/swiper.min.css">
    <link rel="stylesheet" href="../assets/css/magnific-popup.css">
    <link rel="stylesheet" href="../assets/css/nice-select.css">
    <link rel="stylesheet" href="../assets/css/select2.min.css">
    <link rel="stylesheet" href="../assets/css/flatpickr.min.css">
    <link rel="stylesheet" href="../assets/css/flatpicker.css">
    <link rel="stylesheet" href="../assets/css/slick.css">
    <link rel="stylesheet" href="../assets/css/styles.css">
</head>

<body>
    <!-- Header section start -->
    <header>
        <div class="header-top d-lg-block d-none">
            <div class="container">
                <div class="header-top-content">
                    <div class="left">
                        <p>1234 Elm Street, Suite 567, IL 62701, United States</p>
                    </div>
                    <div class="right">
                        <a href="tel:123456789">+****************</a>
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                        <div class="language-dropdown">
                            <div class="custom-nice-select">
                                <select class="nice-select-sort-1">
                                    <option value="1" selected>EN</option>
                                    <option value="2">BN</option>
                                    <option value="3">IN</option>
                                    <option value="4">FR</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="main-header">
            <div class="container">
                <div class="main-header-content">
                    <div class="left">
                        <a href="index.html" class="logo">
                            <img src="../assets/images/logo/logo.svg" alt="LOGO">
                        </a>
                    </div>
                    <div class="main-menu d-lg-block d-none">
                        <ul>
                            <li>
                                <a href="index.html" class="active">Home</a>
                            </li>
                            <li>
                                <a href="rooms.html">Romes</a>
                            </li>
                            <li>
                                <a href="restaurant.html">Restaurant</a>
                            </li>
                            <li>
                                <a href="offers.html">Offers</a>
                            </li>
                            <li>
                                <a href="gallery.html">Photo Gallery</a>
                            </li>
                            <li>
                                <a href="blogs.html">Blogs</a>
                            </li>
                        </ul>
                    </div>
                    <div class="right">
                        <div class="btn-wrapper">
                            <div class="d-sm-block d-none">
                                <a href="rooms.html" class="primary-button border-btn">Book Now</a>
                            </div>
                            <div class="d-none">
                                <a href="login.html" class="primary-button">Login</a>
                            </div>
                            <div class="d-block">
                                <div class="user-dropdown-full">
                                    <div class="user-dropdown-btn">
                                        <iconify-icon icon="tdesign:user-filled" class="user-icon"></iconify-icon>
                                    </div>
                                    <div class="user-dropdown-content-2">
                                        <div class="header">
                                            <div class="img">
                                                <img src="../assets/images/user/user-r-0.png" alt="">
                                            </div>
                                            <div class="content">
                                                <p>@sakib</p>
                                            </div>
                                        </div>
                                        <div class="user-link">
                                            <ul>
                                                <li>
                                                    <a href="my-account.html">
                                                        <div class="icon">
                                                            <img src="../assets/images/icon/user-icon.svg" alt="">
                                                        </div>
                                                        My Account
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="my-bookings.html">
                                                        <div class="icon">
                                                            <img src="../assets/images/icon/my-bookings-icon.svg"
                                                                alt="">
                                                        </div>
                                                        My Bookings
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="payment-history.html">
                                                        <div class="icon">
                                                            <img src="../assets/images/icon/payment-history-icon.svg"
                                                                alt="">
                                                        </div>
                                                        Payment History
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="#" class="logout">
                                                        <div class="icon">
                                                            <img src="../assets/images/icon/logout-icon.svg" alt="">
                                                        </div>
                                                        Logout
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="toggle-btn d-lg-none d-block">
                                <button class="htlib-toggle-btn htlib-offcanvas-toggle">
                                    <iconify-icon icon="hugeicons:menu-01" class="menu-icon"></iconify-icon>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <!-- header section end -->

    <!-- offcanvas or mobile menu part start -->
    <div class="htlib-offcanvas">
        <div class="htlib-offcanvas-wrapper">
            <div class="htlib-offcanvas-header">
                <a href="index.html" class="htlib-offcanvas-logo">
                    <img src="../assets/images/logo/logo.svg" alt="LOGO">
                </a>
                <div class="htlib-offcanvas-close">
                    <button class="htlib-offcanvas-close-toggle">
                        <iconify-icon icon="hugeicons:cancel-01" class="cancel-icon"></iconify-icon>
                    </button>
                </div>
            </div>
            <div class="htlib-offcanvas-navbars">
                <ul>
                    <li>
                        <a href="index.html">Home</a>
                    </li>
                    <li>
                        <a href="rooms.html">Romes</a>
                    </li>
                    <li>
                        <a href="restaurant.html">Restaurant</a>
                    </li>
                    <li>
                        <a href="offers.html">Offers</a>
                    </li>
                    <li>
                        <a href="gallery.html">Photo Gallery</a>
                    </li>
                    <li>
                        <a href="blogs.html">Blogs</a>
                    </li>
                </ul>
            </div>
            <div class="header-top-content">
                <a href="#" class="location header-top-right-card">
                    <div class="icon">
                        <iconify-icon icon="hugeicons:location-06" class="header-top-icon"></iconify-icon>
                    </div>
                    <div class="text">
                        <h6>1234 Elm Street, Suite 567, IL 62701, United States</h6>
                    </div>
                </a>
                <a href="tel:123456789" class="location header-top-right-card">
                    <div class="icon">
                        <iconify-icon icon="hugeicons:calling-02" class="header-top-icon"></iconify-icon>
                    </div>
                    <div class="text">
                        <h6>+****************</h6>
                    </div>
                </a>
                <a href="mailto:<EMAIL>" class="location header-top-right-card">
                    <div class="icon">
                        <iconify-icon icon="hugeicons:mail-02" class="header-top-icon"></iconify-icon>
                    </div>
                    <div class="text">
                        <h6><EMAIL></h6>
                    </div>
                </a>
            </div>
            <div class="appointment-btn">
                <a href="rooms.html" class="primary-button border-btn">Book Now</a>
            </div>
        </div>
    </div>
    <div class="htlib-offcanvas-overlay"></div>
    <!-- offcanvas or mobile menu part end -->

    <!-- Body main wrapper start -->
    <main>

        <div class="common-page-header" data-background="../assets/images/page-header/page-header-banner.png">
            <div class="page-header-overlay">
                <div class="container">
                    <h5>Checkout</h5>
                </div>
            </div>
        </div>

        <!-- checkout area start -->
        <section class="checkout-area section_space-py">
            <div class="container">
                <!-- checkout progress -->
                <div class="checkout-container">
                    <div class="checkout-progress">
                        <div class="step completed">
                            <div class="step-number">
                                <span class="step-number-text">1</span>
                                <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="20 6 9 17 4 12"></polyline>
                                </svg>
                            </div>
                            <div class="step-label">Select Your Room</div>
                        </div>

                        <div class="step active">
                            <div class="step-number">
                                <span class="step-number-text">2</span>
                                <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="20 6 9 17 4 12"></polyline>
                                </svg>
                            </div>
                            <div class="step-label">Reservation</div>
                        </div>

                        <div class="step">
                            <div class="step-number">
                                <span class="step-number-text">3</span>
                                <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="20 6 9 17 4 12"></polyline>
                                </svg>
                            </div>
                            <div class="step-label">Confirmation</div>
                        </div>

                        <div class="step">
                            <div class="step-number">
                                <span class="step-number-text">4</span>
                                <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="20 6 9 17 4 12"></polyline>
                                </svg>
                            </div>
                            <div class="step-label">Payment</div>
                        </div>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="button-container d-none">
                        <button class="btn btn-previous">Previous</button>
                        <button class="btn btn-next">Next Step</button>
                    </div>
                </div>
                <!-- reservation form -->
                <form class="reservation-form">
                    <div class="row gy-5">
                        <div class="col-xxl-7 col-xl-7 col-lg-7">
                            <!-- Personal Information -->
                            <div class="checkout-personal-info">
                                 <div class="checkout-heading">
                                    <h2 class="title">Your Information</h2>
                                </div>
                                <div class="row gy-3">
                                    <div class="col-lg-6">
                                         <div class="td-form-group">
                                            <label for="fullName" class="input-label">Full Name <span>*</span></label>
                                            <div class="input-field">
                                                <input type="text" class="form-control" id="fullName" placeholder="">
                                            </div>
                                            <p class="feedback-invalid">This field is required</p>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                         <div class="td-form-group">
                                            <label for="fullName" class="input-label">Email <span>*</span></label>
                                            <div class="input-field">
                                                <input type="email" id="email" class="form-control" placeholder="">
                                            </div>
                                            <p class="feedback-invalid">This field is required</p>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                         <div class="td-form-group">
                                            <label for="country" class="input-label">Country <span>*</span></label>
                                            <div class="input-field">
                                            <input type="text" id="country" class="form-control" placeholder="">
                                            </div>
                                            <p class="feedback-invalid">This field is required</p>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                         <div class="td-form-group">
                                            <label for="fullPhoneName" class="input-label">Phone <span>*</span></label>
                                            <div class="input-field">
                                                <input  type="text" id="Phone" class="form-control" placeholder="">
                                            </div>
                                            <p class="feedback-invalid">This field is required</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="payment-method">
                                    <div class="checkout-heading">
                                        <h2 class="title">Payment Method</h2>
                                    </div>
                                    <p>Pay With:</p>

                                    <!-- Payment Options -->
                                    <div class="payment-options">
                                        <div class="payment-option active" data-method="card">
                                            <i class="fa-solid fa-credit-card"></i>
                                            <span>Card Payment</span>
                                        </div>
                                        <div class="payment-option" data-method="bank">
                                            <i class="fa-solid fa-building-columns"></i>
                                            <span>Bank Transfer</span>
                                        </div>
                                    </div>

                                    <!-- Card Payment Form -->
                                    <div class="payment-options-form mt-30 method-form" id="card-form">
                                        <div class="row gy-3">
                                            <div class="col-lg-6">
                                                <div class="td-form-group">
                                                    <label for="cardNumber" class="input-label">Card Number <span>*</span></label>
                                                    <div class="input-field">
                                                        <input type="text" id="cardNumber" class="form-control" placeholder="1234 5678 9012 3456">
                                                    </div>
                                                    <p class="feedback-invalid">This field is required</p>
                                                </div>
                                            </div>
                                            <div class="col-lg-6">
                                                <div class="td-form-group">
                                                    <label for="cardName" class="input-label">Name on Card <span>*</span></label>
                                                    <div class="input-field">
                                                        <input type="text" id="cardName" class="form-control" placeholder="John Doe">
                                                    </div>
                                                    <p class="feedback-invalid">This field is required</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="payment-gateway fst-italic mt-2">
                                            <p>Payment Gateway Charge $10</p>
                                        </div>
                                    </div>

                                    <!-- Bank Transfer Form -->
                                    <div class="payment-options-form mt-30 method-form d-none" id="bank-form">
                                        <div class="row gy-3">
                                            <div class="col-lg-6">
                                                <div class="td-form-group">
                                                    <label for="transId" class="input-label">Trans ID <span>*</span></label>
                                                    <div class="input-field">
                                                        <input type="text" id="transId" class="form-control" placeholder="">
                                                    </div>
                                                    <p class="feedback-invalid">This field is required</p>
                                                </div>
                                            </div>
                                            <div class="col-lg-6">
                                                <div class="td-form-group">
                                                    <label for="fullname2" class="input-label">Full name <span>*</span></label>
                                                    <div class="input-field">
                                                        <input type="text" id="fullname2" class="form-control" placeholder="">
                                                    </div>
                                                    <p class="feedback-invalid">This field is required</p>
                                                </div>
                                            </div>
                                            <div class="col-lg-12">
                                                <div class="td-form-group">
                                                    <label class="input-label" for="image">Screenshot <span>*</span></label>
                                                    <div class="input-field">
                                                        <div id="uploadItems">
                                                            <div class="upload-custom-file">
                                                                <input type="file" name="avatar" class="upload-input" accept=".gif, .jpg, .png, .jpeg, .webp, .pdf, .svg" id="image">
                                                                <label for="image">
                                                                    <img class="upload-icon" src="../assets/images/icon/textarea-upload.svg" alt="upload file">
                                                                    <span><b>Upload Screenshot</b></span>
                                                                </label>
                                                                <button type="button" class="file-upload-close" style="display: none;">
                                                                    <i><iconify-icon icon="tabler:circle-x"></iconify-icon></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <p class="feedback-invalid">This field is required</p>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="payment-gateway fst-italic mt-2">
                                            <p>Payment Gateway Charge $10</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-5 col-xl-5 col-lg-5">
                            <!-- Reservation Summary -->
                            <div class="reservation-summary">
                                <div class="checkout-heading">
                                    <h2 class="title">Review Your Reservation</h2>
                                </div>
                                
                                <div class="booking-details">
                                    <div class="selected-date">
                                        <p>Selected Date: 21 May, 2025 - 28 May, 2025</p>
                                    </div>
                                    
                                    <div class="room-details">
                                        <img class="room-image" src="../assets/images/checkout/booking/booking-img-01.png" alt="City View Deluxe">
                                        
                                        <div class="room-info">
                                            <h3>City View Deluxe</h3>
                                            <div class="pirce-info">
                                                <h6 class="price">$400/</h6>
                                                <span>per night</span>
                                            </div>
                                            
                                            <div class="room-amenities">
                                                <div class="amenity">
                                                    <div class="icon">
                                                        <img src="../assets/images/amenities/amenities-square-fit.svg" alt="amenities">
                                                    </div>
                                                    <span>King Bed</span>
                                                </div>
                                                
                                                <div class="amenity">
                                                   <div class="icon">
                                                        <img src="../assets/images/amenities/amenities-beds.svg" alt="amenities">
                                                   </div>
                                                    <span>2 Beds</span>
                                                </div>
                                                
                                                <div class="amenity">
                                                   <div class="icon">
                                                        <img src="../assets/images/amenities/amenities-bathtub.svg" alt="amenities">
                                                    </div>
                                                    <span>2 Bathrooms</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="price-summary mt-30">
                                    <div class="price-row">
                                        <h6>Amount</h6>
                                        <span>$2,800.00</span>
                                    </div>
                                    
                                    <div class="price-row">
                                        <h6>TAX</h6>
                                        <span>$100.00</span>
                                    </div>
                                    
                                    <div class="price-row">
                                        <h6>Charge</h6>
                                        <span>$100.00</span>
                                    </div>
                                    
                                    <div class="price-row">
                                        <h6>Sub Total</h6>
                                        <span>$3,000.00</span>
                                    </div>
                                    
                                    <div class="price-row">
                                        <h6>Discount</h6>
                                        <span class="negative">(-) $300.00</span>
                                    </div>
                                    
                                    <div class="price-row">
                                        <h6>Days You Selected</h6>
                                        <span>10</span>
                                    </div>
                                    
                                    <div class="price-row total">
                                        <h6>Total</h6>
                                        <span>$2,600.00</span>
                                    </div>
                                </div>
                                
                                <div class="terms">
                                    <div class="animate-custom">
                                        <input class="inp-cbx" id="terms" type="checkbox" style="display: none;">
                                        <label class="cbx" for="terms">
                                            <span>
                                                <svg width="12px" height="9px" viewBox="0 0 12 9">
                                                    <polyline points="1 5 4 8 11 1"></polyline>
                                                </svg>
                                            </span>
                                            <span>I accept the Terms and Conditions and Privacy Policy.</span>
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="action-buttons">
                                    <button type="submit" class="primary-button xl-btn w-100">Confirm Payment</button>
                                    <button type="button" class="primary-button border-btn xl-btn w-100">Confirm and pay later</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </section>
        <!-- checkout area end -->


    </main>
    <!-- Body main wrapper end -->

    <!-- Footer area start -->
    <footer class="footer-1">
        <div class="main-footer section_space-py" data-background="../assets/images/footer/footer.png">
            <div class="container">
                <div class="full-footer">
                    <div class="left">
                        <div class="logo">
                            <a href="index.html">
                                <img src="../assets/images/logo/logo-white.svg" alt="LOGO">
                            </a>
                        </div>
                        <p class="description">
                            Discover how we’ve grown, adapted, and continually elevated the guestexperience.Seamlessly
                            blends
                            modern comfort with city views.
                        </p>
                        <div class="social-buttons">
                            <a href="#">
                                <iconify-icon icon="lucide:instagram" class="social-icon"></iconify-icon>
                            </a>
                            <a href="#">
                                <iconify-icon icon="proicons:x-twitter" class="social-icon"></iconify-icon>
                            </a>
                            <a href="#">
                                <iconify-icon icon="lucide:linkedin" class="social-icon"></iconify-icon>
                            </a>
                        </div>

                    </div>
                    <div class="right">
                        <div class="column-1 footer-link-list">
                            <h5>Quick Links</h5>
                            <ul>
                                <li>
                                    <a href="about.html">About Us</a>
                                </li>
                                <li>
                                    <a href="contact.html">Contact</a>
                                </li>
                                <li>
                                    <a href="privacy-policy.html">Privacy</a>
                                </li>
                                <li>
                                    <a href="blogs.html">Blogs</a>
                                </li>
                                <li>
                                    <a href="restaurant.html">Restaurant</a>
                                </li>
                            </ul>
                        </div>
                        <div class="column-2 footer-link-list">
                            <h5>Exclusive Offers</h5>
                            <ul>
                                <li>
                                    <a href="gallery.html">Gallery</a>
                                </li>
                                <li>
                                    <a href="offers.html">Offers</a>
                                </li>
                                <li>
                                    <a href="offers.html">Special Deals</a>
                                </li>
                            </ul>
                        </div>
                        <div class="column-3 footer-link-list">
                            <h5>Explore Our Hotel</h5>
                            <ul>
                                <li>
                                    <a href="gallery.html">Gallery</a>
                                </li>
                                <li>
                                    <a href="offers.html">Packages</a>
                                </li>
                                <li>
                                    <a href="rooms.html">Rooms & Suites</a>
                                </li>
                                <li>
                                    <a href="facilities.html">Facilities</a>
                                </li>
                            </ul>
                        </div>
                        <div class="column-4 footer-link-list">
                            <h5>Quick Links</h5>
                            <ul>
                                <li>
                                    <a href="#">
                                        <span class="contact-icon-box">
                                            <iconify-icon icon="ep:location" class="contact-icon"></iconify-icon>
                                        </span>
                                        ul. Skrajna 7, Szczecin 70-886, Szczecin
                                    </a>
                                </li>
                                <li>
                                    <a href="mailto:<EMAIL>">
                                        <span class="contact-icon-box">
                                            <iconify-icon icon="famicons:mail-outline"
                                                class="contact-icon"></iconify-icon>
                                        </span>
                                        <EMAIL>
                                    </a>
                                </li>
                                <li>
                                    <a href="callto:33700555927">
                                        <span class="contact-icon-box">
                                            <iconify-icon icon="hugeicons:call" class="contact-icon"></iconify-icon>
                                        </span>
                                        (+33)7 00 55 59 27
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bottom-footer">
            <div class="container">
                <div class="bottom-footer-content">
                    <div class="left">
                        <p>© 2025 hotelib Hotel. All rights reserved.</p>
                    </div>
                    <div class="right">
                        <a href="terms-and-condition.html">Terms and Conditions</a>
                        <a href="privacy-policy.html">Privacy Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    <!-- Footer area end -->

    <!-- JS here -->
    <script src="../assets/js/jquery-3.7.1.min.js"></script>
    <script src="../assets/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/jquery.nice-select.min.js"></script>
    <script src="../assets/js/magnific-popup.min.js"></script>
    <script src="../assets/js/swiper.min.js"></script>
    <script src="../assets/js/jarallax.min.js"></script>
    <script src="../assets/js/iconify.min.js"></script>
    <script src="../assets/js/moment.min.js"></script>
    <script src="../assets/js/select2.js"></script>
    <script src="../assets/js/flatpickr.js"></script>
    <script src="../assets/js/slick.min.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const steps = document.querySelectorAll('.step');
            const btnNext = document.querySelector('.btn-next');
            const btnPrevious = document.querySelector('.btn-previous');


            // Find the currently active step
            function getCurrentStep() {
                for (let i = 0; i < steps.length; i++) {
                    if (steps[i].classList.contains('active')) {
                        return i;
                    }
                }
                return 0;
            }

            // Update buttons based on current step
            function updateButtons(currentStep) {
                if (currentStep === 0) {
                    btnPrevious.style.visibility = 'hidden';
                } else {
                    btnPrevious.style.visibility = 'visible';
                }

                if (currentStep === steps.length - 1) {
                    btnNext.textContent = 'Complete';
                } else {
                    btnNext.textContent = 'Next Step';
                }
            }

            // Go to next step
            btnNext.addEventListener('click', function () {
                const currentStep = getCurrentStep();

                if (currentStep < steps.length - 1) {
                    // Mark current step as completed
                    steps[currentStep].classList.remove('active');
                    steps[currentStep].classList.add('completed');

                    // Activate next step
                    steps[currentStep + 1].classList.add('active');

                    updateButtons(currentStep + 1);
                } else {
                    // Handle completion
                    alert('Booking completed!');
                }
            });

            // Go to previous step
            btnPrevious.addEventListener('click', function () {
                const currentStep = getCurrentStep();

                if (currentStep > 0) {
                    // Deactivate current step
                    steps[currentStep].classList.remove('active');

                    // Activate previous step
                    steps[currentStep - 1].classList.remove('completed');
                    steps[currentStep - 1].classList.add('active');

                    updateButtons(currentStep - 1);
                }
            });

            // Initialize
            const initialStep = getCurrentStep();
            updateButtons(initialStep);
        });
    </script>
    <script>
        const options = document.querySelectorAll('.payment-option');
        const cardForm = document.getElementById('card-form');
        const bankForm = document.getElementById('bank-form');

        options.forEach(option => {
            option.addEventListener('click', () => {
                options.forEach(opt => opt.classList.remove('active'));
                option.classList.add('active');

                const method = option.getAttribute('data-method');

                if (method === 'card') {
                    cardForm.classList.remove('d-none');
                    bankForm.classList.add('d-none');
                } else if (method === 'bank') {
                    bankForm.classList.remove('d-none');
                    cardForm.classList.add('d-none');
                }
            });
        });
    </script>
</body>

</html>