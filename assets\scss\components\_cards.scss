@use '../utils' as *;

/*----------------------------------------*/
/*  Cards
/*----------------------------------------*/
.room-card {
  height: 100%;
  display: flex;
  flex-direction: column;

  .room-card-full {
    height: 100%;
    display: flex;
    flex-direction: column;

    .room-card-img {
      display: block;
      width: 100%;
      height: 230px;

      @media #{$lg,$md,$xs} {
        height: 200px;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .room-card-content {
      border: 1px solid rgba(21, 20, 21, 0.16);
      border-top: none;
      padding: 16px 20px 20px 20px;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .rooms-amenities {
        display: flex;
        align-items: center;
        gap: 12px 16px;
        margin-bottom: 10px;
        flex-wrap: wrap;

        .single-amenities {
          display: flex;
          align-items: center;
          gap: 4px;

          .icon {
            width: 18px;
            height: 18px;
            display: flex;
            justify-content: center;
            align-items: center;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          p {
            font-size: 14px;
            color: var(--td-text-primary);
            line-height: normal;
            margin-bottom: 0;
            font-weight: 500;
          }
        }
      }

      .room-title {
        display: block;
        color: var(--td-heading);
        font-family: var(--td-heading-font);
        font-size: 24px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        margin: 4px 0px 10px 0px;
        transition: all 0.3s ease-in-out;

        @media #{$xl} {
          font-size: 26px;
        }

        @media #{$lg,$md} {
          font-size: 24px;
        }

        @media #{$xs} {
          font-size: 20px;
        }

        &:hover {
          color: var(--td-primary);
        }
      }

      .room-description {
        color: rgba($text-primary, $alpha: 0.7);
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: lh(26, 16);

        @media #{$xs} {
          font-size: 14px;
        }
      }

      .separator {
        width: 78px;
        height: 2px;
        opacity: 0.2;
        background: linear-gradient(90deg, #151415 0%, #151415 49.52%, #151415 100%);
        margin-top: 20px;
        margin-bottom: 10px;
      }

      .price-and-action {
        display: flex;
        align-items: center;
        justify-content: space-between;

        @media #{$xs} {
          flex-direction: column;
          align-items: start;
          justify-content: start;
        }

        @media #{$sm} {
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
        }

        .price {
          @media #{$xs} {
            margin-bottom: 10px;
          }

          @media #{$sm} {
            margin-bottom: 0px;
          }

          h4 {
            color: var(--td-primary);
            font-family: var(--td-body-font);
            font-size: 24px;
            font-weight: 400;
            line-height: normal;

            @media #{$lg,$md} {
              font-size: 20px;
            }

            @media #{$xs} {
              font-size: 18px;
            }

            span {
              color: var(--td-text-primary);
              font-family: var(--td-body-font);
              font-size: 16px;
              font-weight: 600;
              line-height: normal;

              @media #{$xs} {
                font-size: 14px;
                font-weight: 400;
              }
            }

            del.old-price {
              color: #A7A7A7;
              font-size: 20px;
              margin-inline-end: 3px;
            }
          }
        }

      }
    }
  }
}

.amenities-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(170, 132, 83, 0.16);
  padding: 30px 20px;
  transition: all 0.3s ease-in-out;
  height: 100%;
  min-height: 195px;

  @media #{$xs} {
    padding: 20px 10px;
    min-height: 165px;
  }

  @media #{$sm} {
    padding: 30px 20px;
    min-height: 195px;
  }

  .amenities-card-img {
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 24px;

    @media #{$xs} {
      width: 30px;
      height: 30px;
    }

    @media #{$sm} {
      width: 40px;
      height: 40px;
    }
  }

  .amenities-card-content {
    .amenities-title {
      color: var(--td-heading);
      text-align: center;
      font-family: var(--td-heading-font);
      font-size: 16px;
      font-weight: 400;
      line-height: normal;
    }
  }

  &:hover {
    background: rgba(170, 132, 83, 0.10);
  }
}

.package-card {
  height: 100%;
  display: flex;
  flex-direction: column;

  .package-card-img {
    display: block;
    width: 100%;
    height: 477px;

    @media #{$xxl} {
      height: 350px;
    }

    @media #{$xl,$lg,$md,$xs} {
      height: 300px;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .package-card-content {
    border: 1px solid rgba(21, 20, 21, 0.16);
    border-top: none;
    padding: 16px 20px 20px 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .package-title {
      display: block;
      font-family: var(--td-heading-font);
      color: var(--td-heading);
      font-size: 24px;
      font-weight: 400;
      line-height: normal;
      margin-bottom: 14px;

      @media #{$xxl,$xl,$lg,$md,$xs} {
        font-size: 28px;
      }

      @media #{$xl,$lg} {
        font-size: 26px;
      }

      @media #{$md} {
        font-size: 24px;
      }

      @media #{$xs} {
        font-size: 22px;
      }

      @media #{$sm} {
        font-size: 20px;
      }
    }

    .package-price {
      font-family: var(--td-body-font);
      color: #444344;
      font-size: 24px;
      font-weight: 400;
      line-height: normal;

      @media #{$xxl,$xl,$lg,$md,$xs} {
        font-size: 28px;
      }

      @media #{$xl,$lg} {
        font-size: 26px;
      }

      @media #{$md} {
        font-size: 24px;
      }

      @media #{$xs} {
        font-size: 22px;
      }

      @media #{$sm} {
        font-size: 20px;
      }

      .per {
        color: var(--td-heading);
        font-family: var(--td-body-font);
        font-size: 18px;
        font-weight: 400;
        line-height: normal;

        @media #{$xs} {
          font-size: 14px;
        }

        @media #{$sm} {
          font-size: 16px;
        }
      }

      .per-night {
        color: #A7A7A7;
        font-family: var(--td-body-font);
        font-size: 18px;
        font-weight: 400;
        line-height: normal;
        text-decoration-line: line-through;

        @media #{$xs} {
          font-size: 14px;
        }

        @media #{$sm} {
          font-size: 16px;
        }
      }
    }

    .action-btn {
      margin-top: 23px;
    }
  }
}

.stats-extra-padding {
  padding-block-start: clamp(1.625rem, 6.5vw + 1rem, 5rem);
}

.stats {
  border: 1px solid rgba(170, 132, 83, 0.20);
  border-inline-start: none;
  border-inline-end: none;
}

.stats-card {
  h2 {
    color: var(--td-primary);
    text-align: center;
    font-size: 60px;
    font-weight: 400;
    line-height: normal;
    margin-bottom: 20px;

    @media #{$xl} {
      margin-bottom: 18px;
      font-size: 50px;
    }

    @media #{$lg} {
      margin-bottom: 16px;
      font-size: 45px;
    }

    @media #{$md} {
      margin-bottom: 14px;
      font-size: 40px;
    }

    @media #{$xs} {
      margin-bottom: 8px;
      font-size: 24px;
    }

    @media #{$sm} {
      margin-bottom: 10px;
      font-size: 30px;
    }
  }

  p {
    color: #444344;
    text-align: center;
    font-size: 24px;
    font-weight: 400;
    line-height: normal;

    @media #{$md} {
      font-size: 20px;
    }

    @media #{$xs} {
      font-size: 16px;
    }

    @media #{$sm} {
      font-size: 18px;
    }
  }
}

.blog-card {
  height: 100%;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease-in-out;

  .blog-card-img {
    width: 100%;
    height: 452px;
    overflow: hidden;
    transition: all 0.3s ease-in-out;

    @media #{$md} {
      height: 400px;
    }

    @media #{$xs} {
      height: 300px;
    }

    @media #{$sm} {
      height: 400px;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    inset-inline-start: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, rgba(24, 59, 86, 0.00) 0%, rgba(22, 49, 70, 0.45) 56.25%, #152532 88%);
    z-index: 1;
  }

  .blog-card-content {
    position: absolute;
    bottom: 0;
    inset-inline-start: 0;
    width: 100%;
    padding: 26px;
    z-index: 2;
    transition: all 0.3s ease-in-out;

    @media #{$lg,$md,$xs} {
      padding: 18px;
    }

    .blog-title {
      display: inline-block;
      font-family: var(--td-heading-font);
      color: var(--td-white);
      font-size: 24px;
      font-weight: 400;
      line-height: normal;
      margin-bottom: 10px;
      text-decoration-line: none;
      text-decoration-color: transparent;
      text-decoration-thickness: 1px;
      text-underline-offset: 3px;
      transition:
        text-decoration-color 0.3s ease-in-out,
        text-decoration-thickness 0.3s ease-in-out;

      &:hover {
        text-decoration-line: underline;
        text-decoration-color: var(--td-white);
        text-decoration-thickness: 1px;
      }

      @media #{$md} {
        font-size: 20px;
      }

      @media #{$xs} {
        font-size: 18px;
      }
    }

    p {
      color: var(--td-text-secondary);
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: lh(22, 16);
      transition: all 0.3s ease-in-out;
    }

    .date-action {
      position: absolute;
      bottom: 26px;
      inset-inline-start: 26px;
      width: calc(100% - 52px);
      display: flex;
      justify-content: space-between;
      align-items: center;
      opacity: 0;
      transform: translateY(20px);
      transition: all 0.3s ease-in-out;

      @media #{$lg,$md,$xs} {
        bottom: 18px;
        inset-inline-start: 18px;
        width: calc(100% - 36px);
      }

      .date {
        color: var(--td-text-secondary);
        text-align: right;
        font-family: var(--td-body-font);
        font-size: 18px;
        font-weight: 400;
        line-height: normal;

        @media #{$lg,$md,$xs} {
          font-size: 16px;
        }
      }
    }
  }

  &:hover {
    .blog-card-img {
      transform: scale(1.1);
    }

    .blog-card-content {
      padding-bottom: 60px;

      @media #{$lg,$md,$xs} {
        padding-bottom: 50px;
      }

      h3,
      p {
        transform: translateY(-5px);
      }

      .date-action {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }
}

.product-gallery {
  position: relative;

  .main-slider {
    .slick-slide {
      &.slick-current {
        &.slick-active {
          position: relative;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            inset-inline-start: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(180deg, rgba(0, 0, 0, 0.00) 0%, rgba(0, 0, 0, 0.50) 100%);
            z-index: 1;
          }
        }
      }
    }
  }

  .slider-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 0;
    position: relative;
  }

  .main-slider {
    margin-bottom: 0;

    img {
      width: 100%;
      height: auto;
      display: block;
    }
  }

  .nav-slider {
    position: absolute;
    bottom: 20px;
    inset-inline-start: 20px;
    width: 60%;
    max-width: 380px;
    z-index: 5;

    @media #{$xs} {
      bottom: 10px;
      inset-inline-start: 10px;
    }

    @media #{$sm} {
      bottom: 20px;
      inset-inline-start: 20px;
    }

    .slick-slide {
      cursor: pointer;
      opacity: 1;
      padding: 0 5px;

      img {
        border: 2px solid #A9977B;
        height: 80px;

        @media #{$xs} {
          height: 40px;
        }

        @media #{$sm} {
          height: 80px;
        }
      }
    }

    .slick-slide.slick-current {
      opacity: 1;

      img {
        border-color: #F0992A;
      }
    }
  }

  .slick-arrow {
    display: none;
  }
}

.latest-news-card {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 30px;

  &:last-child {
    margin-bottom: 0;
  }

  .latest-news-card-img {
    width: 96px;
    height: 96px;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .latest-news-card-content {
    .latest-news-title {
      display: block;
      color: var(--td-heading);
      font-size: 18px;
      font-weight: 400;
      line-height: normal;
      margin-bottom: 16px;
    }

    p {
      color: var(--td-text-primary);
      font-size: 16px;
      font-weight: 400;
      line-height: normal;
    }
  }
}