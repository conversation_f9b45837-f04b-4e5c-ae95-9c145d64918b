<!DOCTYPE html>
<html class="no-js" lang="zxx">

<head>
  <meta charset="utf-8">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <title>Sign In || Welcome to Hotelib Hotel, where luxury meets the skyline.</title>
  <meta name="description" content="">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <!-- Place favicon.ico in the root directory -->
  <link rel="shortcut icon" type="image/x-icon" href="../assets/images/favicon.svg">
  <!-- CSS here -->
  <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
  <link rel="stylesheet" href="../assets/css/all.min.css">
  <link rel="stylesheet" href="../assets/css/flag-icon.css">
  <link rel="stylesheet" href="../assets/css/swiper.min.css">
  <link rel="stylesheet" href="../assets/css/magnific-popup.css">
  <link rel="stylesheet" href="../assets/css/nice-select.css">
  <link rel="stylesheet" href="../assets/css/select2.min.css">
  <link rel="stylesheet" href="../assets/css/flatpickr.min.css">
  <link rel="stylesheet" href="../assets/css/flatpicker-range.css">
  <link rel="stylesheet" href="../assets/css/slick.css">
  <link rel="stylesheet" href="../assets/css/styles.css">
</head>

<body>

  <!-- Body main wrapper start -->
  <main>

    <!-- auth area start -->
    <section class="auth-section">
        <div class="auth-main-grid">
            <div class="auth-left">
                <div class="auth-thumb">
                    <img src="../assets/images/auth/forgot-thum.png" alt="Forget Thumb">
                </div>
            </div>
            <div class="auth-right">
                <div class="inner">
                    <div class="auth-form-box">
                        <div class="auth-top-contents">
                            <div class="auth-logo">
                                <a href="../themes/index.html">
                                    <img src="../assets/images/logo/logo.svg" alt="Auth Logo">
                                </a>
                            </div>
                            <div class="intro-contents">
                                <h3 class="title">Forgot Password!</h3>
                                <p class="description">Enter your hotlive Eamil Address</p>
                            </div>
                        </div>
                        <div class="auth-from-box">
                            <form id="sign-up-form" action="#">
                                <div class="row gy-3">
                                    <div class="col-xxl-12">
                                        <div class="td-form-group">
                                            <label class="input-label">Email address <span>*</span></label>
                                            <div class="input-field">
                                                <input type="email" class="form-control" placeholder="Email" required>
                                            </div>
                                            <p class="feedback-invalid">This field is required</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="auth-from-btn-wrap mt-20">
                                    <button class="primary-button xl-btn w-100" type="submit">Sign In</button>
                                </div>
                                <div class="auth-bottom-contents">
                                    <div class="have-auth-account">
                                        <p class="description">Don’t receive a verification code? <a class="underline-btn"  href="sign-up.html"> Re-send</a></p>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- auth area end -->

  </main>
  <!-- Body main wrapper end -->

  <!-- JS here -->
  <script src="../assets/js/jquery-3.7.1.min.js"></script>
  <script src="../assets/js/bootstrap.bundle.min.js"></script>
  <script src="../assets/js/jquery.nice-select.min.js"></script>
  <script src="../assets/js/magnific-popup.min.js"></script>
  <script src="../assets/js/swiper.min.js"></script>
  <script src="../assets/js/jarallax.min.js"></script>
  <script src="../assets/js/iconify.min.js"></script>
  <script src="../assets/js/moment.min.js"></script>
  <script src="../assets/js/select2.js"></script>
  <script src="../assets/js/flatpickr.js"></script>
  <script src="../assets/js/slick.min.js"></script>
  <script src="../assets/js/main.js"></script>
    <script>
        // Function to toggle password visibility
        function togglePasswordVisibility(event) {
            const eyeIconSpan = event.currentTarget;
            const passwordInput = eyeIconSpan.closest('.input-field').querySelector('input');  
            // For the image version
            const eyeIconImg = eyeIconSpan.querySelector('img');  
            const eyeIconI = eyeIconSpan.querySelector('i');  

            // Toggle logic for the image version
            if (passwordInput.type === "password") {
                passwordInput.type = "text";

                if (eyeIconImg) {
                    eyeIconImg.src = "../assets/images/icon/eye-open.svg"; 
                }
                if (eyeIconI) {
                    eyeIconI.classList.replace('fa-eye', 'fa-eye-slash'); 
                }
            } else {
                passwordInput.type = "password";
                if (eyeIconImg) {
                    eyeIconImg.src = "../assets/images/icon/eye.svg";
                }
                if (eyeIconI) {
                    eyeIconI.classList.replace('fa-eye-slash', 'fa-eye');  
                }
            }
        }

        // Attach event listeners to all eye icon spans
        document.querySelectorAll('.eyeicon').forEach(function (eyeIconSpan) {
            eyeIconSpan.addEventListener('click', togglePasswordVisibility);
        });
    </script>
</body>

</html>