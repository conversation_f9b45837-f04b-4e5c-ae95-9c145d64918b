@use '../../utils' as *;

/*----------------------------------------*/
/*  Checkout styles
/*----------------------------------------*/
.checkout-area {
    background: var(--td-bg-1);
}

.checkout-heading {
    .title {
        font-size: 20px;
        margin-bottom: 20px;
        font-weight: 500;
    }
}

// Checkout progress
.checkout-container {
    max-width: 1000px;
    margin: auto;
    margin-bottom: 60px;
}

.checkout-progress {
    display: flex;
    justify-content: space-between;
    position: relative;
    margin-bottom: 30px;

    &::before {
        content: '';
        position: absolute;
        top: 25px;
        inset-inline-start: 12.5%;
        inset-inline-end: 12.5%;
        height: 1px;
        background: rgba($black, $alpha: 0.2);
        z-index: 1;
    }

    .step {
        position: relative;
        z-index: 2;
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 25%;
    }

    .step-number {
        width: 46px;
        height: 46px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fff4e5;
        border: 1px solid #dddddd;
        margin-bottom: 10px;
        font-weight: bold;
        color: #777777;
        font-size: 20px;
        font-weight: 500;

        @media #{$xs} {
            width: 36px;
            height: 36px;
        }
    }

    .step-label {
        text-align: center;
        font-size: 20px;
        color: #777777;
        font-family: var(--td-heading-font);
        font-weight: 400;

        @media #{$md} {
            font-size: 18px;
        }

        @media #{$xs} {
            font-size: 16px;
        }

        @media #{$xxs} {
            font-size: 13px;
        }
    }

    .step.completed {
        .step-number {
            background-color: #a0855c;
            border-color: #a0855c;
            color: var(--td-white);
            position: relative;
        }

        .step-label {
            color: #a0855c;
        }

        .checkmark {
            display: block;
        }

        .step-number-text {
            display: none;
        }
    }

    .step.active {
        .step-number {
            border: 2px solid #a0855c;
            color: #a0855c;
        }

        .step-label {
            color: #333333;
        }
    }

    .checkmark {
        display: none;
        width: 20px;
        height: 20px;
    }
}

// Checkout personal info
.checkout-personal-info {

    .payment-method {
        margin-top: 30px;
    }

    .payment-options {
        display: flex;
        gap: 14px;
        margin-top: 15px;
        flex-wrap: wrap;
    }

    .payment-option {
        padding: 12px;
        border-radius: 3px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        cursor: pointer;
        transition: all 0.3s;
        border: 1px solid rgba(21, 20, 21, 0.16);
        font-weight: 500;
        color: rgba($heading, $alpha: 0.6);

        &.active {
            border: 1px solid var(--td-primary);
            background-color: transparent;
            color: rgba($heading, $alpha: 1);

            i {
                color: var(--td-primary);
            }
        }

        i {
            font-size: 18px;
            color: #666;
        }
    }
}

// Reservation summary
.reservation-summary {
    padding-inline-start: 20px;

    @media #{$xs,$sm,$md} {
        padding-inline-start: 0;
    }

    .booking-details {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .selected-date {
        border-bottom: 1px solid #eee;
        border: 1px solid rgba(21, 20, 21, 0.16);
        background: #FFF4E5;
        padding: 10px 16px;
    }

    .room-details {
        display: flex;
        gap: 24px;
        align-items: center;
        border: 1px solid rgba(21, 20, 21, 0.16);

        @media #{$xs} {
            flex-direction: column;
            align-items: start;
        }

        @media #{$sm} {
            flex-direction: row;
            align-items: center;
        }
    }

    .room-image {
        width: 146px;
        height: 146px;
        object-fit: cover;

        @media #{$xs} {
            width: 100%;
            height: 250px;
        }

        @media #{$sm} {
            width: 146px;
        }
    }

    .room-info {
        @media #{$xs} {
            padding-left: 16px;
            padding-right: 16px;
            padding-bottom: 16px;
        }

        @media #{$sm} {
            padding-left: 0px;
            padding-right: 0px;
            padding-bottom: 0px;
        }
        h3 {
            font-size: 24px;
            font-weight: 500;
            margin-bottom: 5px;

            @media #{$xs} {
                font-size: 20px;
            }
        }
    }

    .pirce-info {
        display: flex;
        align-items: center;

        font-weight: 500;

        .price {
            color: #b89468;
            font-size: 20px;
        }
    }

    .room-amenities {
        display: flex;
        gap: 12px 16px;
        margin-top: 10px;
        flex-wrap: wrap;
    }

    .amenity {
        display: flex;
        align-items: center;
        gap: 5px;
        font-size: 12px;
        color: #666;

        .icon {
            flex: 0 0 auto;

            img {
                width: 18px;
            }
        }
    }

    .price-summary {
        border-radius: 3px;
        padding: 30px 30px;
        border: 1px solid rgba(21, 20, 21, 0.16);
    }

    .price-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;

        h6 {
            font-size: 14px;
        }

        span {
            font-size: 14px;
            font-weight: 500;
        }

        &.total {
            margin-top: 10px;
            border-top: 1px solid rgba($black, $alpha: 0.2);
            padding-top: 15px;
            font-size: 16px;
            font-weight: bold;
        }
    }

    .negative {
        color: #d12e5c;
    }

    .terms {
        margin-top: 30px;
        display: flex;
        align-items: flex-start;
        gap: 10px;
    }

    .action-buttons {
        display: flex;
        gap: 15px;
        margin-top: 20px;

        @media #{$xs,$lg} {
            flex-wrap: wrap;
        }
    }
}

// Reservation status
.reservation-status-header {
    text-align: center;

    .icon {
        width: 160px;
        height: 160px;
        border-radius: 50%;
        margin: 0 auto 20px;
        display: flex;
        align-items: center;
        justify-content: center;

        @media #{$xs} {
            width: 120px;
            height: 120px;
        }
    }

    .reservation-title {
        font-size: 28px;
        margin-bottom: 15px;

        @media #{$xs} {
            font-size: 24px;
        }
    }

    .reservation-message {
        color: #666;
        max-width: 600px;
        margin: 0 auto;
        font-size: 14px;
        line-height: 1.6;
    }

}

.reservation-booking-details {
    flex: 1;
    padding: 24px;
    border: 1px solid rgba($heading, $alpha: 0.16);

    .details-section {
        margin-bottom: 15px;
    }

    .details-row {
        display: flex;
        justify-content: space-between;
        font-size: 14px;

        &:not(:first-child) {
            padding-top: 18px;
        }

        &:not(:last-child) {
            padding-bottom: 18px;
            border-bottom: 1px solid rgba(21, 20, 21, 0.16);
        }
    }

    .details-label {
        font-size: 14px;
    }

    .details-value {
        font-weight: 500;
        text-align: right;
        color: #444344;
    }

    .price-total {
        font-size: 18px;
        font-weight: 600;
        color: var(--text-color);
    }

    .discount {
        color: #e74c3c;
    }

    .status-badge {
        display: inline-block;
        padding: 3px 10px;
        font-size: 12px;
        font-weight: 600;
        color: var(--td-white);
    }

    .status-booked {
        background-color: var(--td-primary);
    }

    .status-paid {
        background-color: var(--td-primary);
    }
}

.reservation-room-details {
    border: 1px solid rgba($heading, $alpha: 0.16);
    padding: 16px;

    .details-title {
        font-size: 24px;
        margin-bottom: 10px;
        padding-bottom: 5px;
    }

    .room-image {
        width: 100%;
        height: 240px;
        object-fit: cover;
        border-radius: 0;
        margin-bottom: 20px;
    }

    .room-title {
        font-size: 20px;
        margin-bottom: 10px;
        color: var(--text-color);
    }

    .room-info-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;
        font-size: 14px;
    }

    .room-info-label {
        font-weight: 500;
    }
}

.reservation-footer-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 20px;
    flex-wrap: wrap;

    @media #{$xxs} {
        flex-direction: column;
    }
}