@use '../../utils' as *;

/*----------------------------------------*/
/*  5.2 footer-2
/*----------------------------------------*/
.footer-2 {
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
    overflow: hidden;

    .all-footer-item {
        position: relative;
        z-index: 2;

        .subscribe-box {
            .subscribe-box-content {
                display: flex;
                justify-content: center;
                align-items: center;

                .full {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    gap: 16px;
                    width: 970px;
                    background-color: var(--td-primary);
                    padding: 60px 60px;
                    border-radius: 16px;
                    margin-top: 100px;
                    margin-bottom: 24px;

                    @media #{$xl} {
                        padding: 40px 60px;
                        margin-top: 60px;
                    }

                    @media #{$lg,$md} {
                        padding: 30px 40px;
                        margin-top: 60px;
                        width: 100%;
                    }
                    @media #{$xs} {
                        padding: 20px 30px;
                        margin-top: 30px;
                        width: 100%;
                    }

                    @media #{$xs} {
                        flex-direction: column;
                        align-items: start;
                        justify-content: start;
                    }

                    .left {
                        width: 40%;

                        @media #{$md} {
                            width: 45%;
                        }

                        @media #{$xs} {
                            width: 100%;
                        }

                        h2 {
                            color: var(--td-white);
                            font-size: rem(32);
                            font-weight: 600;
                            line-height: lh(32, 32);

                            @media #{$md} {
                                font-size: rem(26);
                            }

                            @media #{$xs} {
                                font-size: rem(20);
                            }

                            @media #{$sm} {
                                font-size: rem(26);
                            }
                        }
                    }

                    .right {
                        width: 60%;

                        @media #{$md} {
                            width: 55%;
                        }

                        @media #{$xs} {
                            width: 100%;
                        }

                        .subscribe-input {
                            position: relative;

                            input[type=text],
                            input[type=email] {
                                outline: none;
                                height: 68px;
                                width: 100%;
                                padding: 8px 140px 8px 15px;
                                border-radius: 16px;
                                color: #6B7280;
                                background: var(--td-white);
                                font-size: 16px;

                                @media #{$xs} {
                                    height: 48px;
                                }

                                &::placeholder {
                                    color: #6B7280;
                                }
                            }

                            .subscribe-btn {
                                position: absolute;
                                top: 50%;
                                transform: translateY(-50%);
                                inset-inline-end: 8px;
                            }
                        }
                    }
                }
            }
        }

        .footer-links {
            .left {
                padding-inline-end: 90px;

                @media #{$md,$xs} {
                    padding-inline-end: 0px;
                    padding-bottom: 20px;
                }

                .logo {
                    display: inline-block;
                    height: 32px;
                    margin-bottom: 20px;

                    img {
                        height: 100%;
                    }
                }

                p {
                    color: #E4E6EA;
                    font-size: rem(14);
                    font-weight: 500;
                    line-height: lh(21, 14);
                }

                .social {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    margin-top: 16px;

                    a {
                        display: inline-flex;
                        justify-content: center;
                        align-items: center;
                        width: 36px;
                        height: 36px;
                        flex-shrink: 0;
                        border-radius: 8px;
                        border: 1px solid var(--td-white);
                        transition: all 0.3s ease-in-out;

                        span{
                            display: flex;

                            .social-icon{
                                font-size: 26px;
                                color: var(--td-white);
                            }
                        }

                        &:hover {
                            background-color: var(--td-white);

                            span{
                                .social-icon{
                                    color: var(--td-primary);
                                }
                            }
                        }
                    }
                }
            }

            .right {
                display: grid;
                grid-template-columns: repeat(5, 1fr);
                gap: 24px;

                @media #{$xs} {
                    grid-template-columns: repeat(2, 1fr);
                    gap: 30px;
                }
        
                @media #{$sm} {
                    grid-template-columns: repeat(3, 1fr);
                    gap: 30px;
                }

                .link-box {
                    h5 {
                        color: var(--td-white);
                        font-size: rem(20);
                        font-weight: 600;
                        line-height: lh(18, 18);
                        margin-bottom: 20px;

                        @media #{$xs} {
                            margin-bottom: 12px;
                        }
                    }

                    ul {
                        list-style-type: none;

                        li {
                            margin-bottom: 16px;

                            @media #{$xs} {
                                margin-bottom: 8px;
                            }

                            &:last-child {
                                margin-bottom: 0;
                            }

                            a {
                                color: #E4E6EA;
                                font-size: rem(14);
                                font-weight: 500;
                                line-height: lh(14, 14);
                                transition: all 0.3s ease-in-out;

                                &:hover {
                                    color: var(--td-white);
                                }
                            }
                        }
                    }
                }
            }
        }

        hr {
            border-color: rgba(255, 255, 255, 0.30);
            margin: 24px 0;
        }

        .footer-copyright {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 10px;
            padding-bottom: 24px;

            @media #{$xs} {
                flex-direction: column;
                justify-content: start;
                align-items: start;
            }

            .middle {
                p {
                    color: #E4E6EA;
                    font-size: rem(14);
                    font-weight: 500;
                    line-height: lh(21, 14);
                }
            }

            .right {
                ul {
                    display: flex;
                    align-items: center;
                    list-style-type: none;
                    gap: 16px;

                    li {
                        a {
                            color: var(--gray-200, #E4E6EA);
                            font-size: rem(14);
                            font-weight: 500;
                            line-height: lh(21, 14);
                        }
                    }
                }
            }
        }
    }

    .circle-1 {
        position: absolute;
        inset-inline-start: -90px;
        top: 117px;
        z-index: 1;

        img {
            @media #{$xl,$lg,$md,$xs} {
                display: none;
            }
        }
    }

    .circle-2 {
        position: absolute;
        inset-inline-end: -21%;
        bottom: -62%;
        z-index: 1;

        img {
            @media #{$xl,$lg,$md,$xs} {
                display: none;
            }
        }
    }
}