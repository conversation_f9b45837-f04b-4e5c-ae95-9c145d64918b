@use "../utils" as *;

/*----------------------------------------*/
/* Alert Styles
/*----------------------------------------*/

.td-alert-box {
    background: var(--td-black);
    padding: rem(12) rem(20) rem(12) rem(16);
    z-index: 1;
    position: relative;
    transition: .3s;
    @include flexbox();
    column-gap: rem(12);
    align-items: center;
    border: rem(1) solid transparent;
    width: 350px;

    @media #{$xxs} {
        padding: rem(10) rem(12) rem(10);
        width: 300px;
    }

    .alert-content {
        @include flexbox();
        align-items: center;
        column-gap: rem(16);
        flex-grow: 1;

        @media #{$xs} {
            column-gap: rem(12);
        }

        .alert-title {
            font-size: 18px;
            font-weight: 500;
            font-family: var(--td-body-font);

            @media #{$xs} {
                font-size: rem(14);
            }
        }

        .alert-message {
            font-size: 14px;
            position: relative;
            margin-top: 2px;
        }
    }

    .alert-icon {
        flex: 0 0 auto;

        svg {
            width: rem(50);
            height: rem(50);

            @media #{$xs} {
                width: rem(36);
                height: rem(36);
            }
        }
    }

    .close-btn {
        padding: 5px;
        position: absolute;
        inset-inline-end: 8px;
        top: 8px;
    }

    &.hidden {
        opacity: 0;
        transform: translateY(-50%, rem(20));
        pointer-events: none;
    }

    &.has-success {
        border-inline-start: 4px solid #0C9;
        background: #E6FAF5;
    }

    &.has-warning {
        border-inline-start: 4px solid #F2C94C;
        background: #FDF8E8;
    }

    &.has-info {
        border-inline-start: 4px solid #5458F7;
        background: #EEEEFE;
    }

    &.has-danger {
        border-inline-start: 4px solid #E93A2D;
        background: #FFE1DF;
    }
}

.alert-show-status {
    position: fixed;
    top: rem(16);
    inset-inline-end: rem(16);
    z-index: 999;
}