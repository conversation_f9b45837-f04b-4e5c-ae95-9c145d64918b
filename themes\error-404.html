<!doctype html>
<html class="no-js" lang="zxx">

<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>Home || MiTrade HTML5 Template</title>
    <meta name="description" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- Place favicon.ico in the root directory -->
    <link rel="shortcut icon" type="image/x-icon" href="../assets/images/favicon.svg">
    <!-- CSS here -->
    <style>
        /* Google Fonts */
        @import url('https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,100..900;1,100..900&family=Marcellus&display=swap');

        :root {
            --primary: #AA8453;
            --text-color: #444344;
            --heading: #151415;
            --white: #fff;
            --heading-font: "Marcellus", serif;
            --body-font: "Jost", sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--body-font);
            background-image: url(../assets/images/error/error-bg.png);
            color: var(--text-color);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
            background-repeat: no-repeat;
            background-size: cover;
        }

        body::after {
            position: absolute;
            content: "";
            height: calc(100% - 70px);
            width: calc(100% - 80px);
            border: 1px solid #AA8453;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
        }

        .error-container {
            max-width: 800px;
            padding: 20px;
            z-index: 1;
            position: relative;
        }

        .error-code {
            font-size: 200px;
            font-weight: 700;
            line-height: 1;
            margin-bottom: 20px;
            position: relative;
        }

        .error-message {
            font-size: 42px;
            font-weight: 600;
            margin-bottom: 30px;
            position: relative;
            z-index: 2;
        }

        .error-description {
            font-size: 18px;
            margin-bottom: 40px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
        }

        .back-button {
            height: 50px;
            padding: 10px 24px;
            background: var(--primary);
            border: 1px solid var(--primary);
            color: var(--white);
            font-size: 16px;
            font-weight: 400;
            line-height: normal;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease-in-out;
            font-family: var(--heading-font);
            text-decoration: none;
        }

        .back-button:hover {
            background: transparent;
            border: 1px solid var(--primary);
            color: var(--heading);

        }

        .back-button svg {
            margin-left: 4px;
            vertical-align: middle;
        }


        @media (max-width: 768px) {
            .error-code {
                font-size: 120px;
            }

            .error-message {
                font-size: 32px;
            }

            .error-description {
                font-size: 16px;
                padding: 0 20px;
            }
        }

        @media (max-width: 480px) {
            body::after {
                display: none;
            }

            .error-code {
                font-size: 100px;
            }

            .error-message {
                font-size: 28px;
            }
        }
    </style>
</head>

<body>

    <!-- Body main wrapper start -->
    <main>
        <div class="error-area">
            <div class="error-container" id="error-container">
                <div class="error-code" id="error-code">404</div>
                <h1 class="error-message" id="error-title">Page not found</h1>
                <p class="error-description" id="error-description">
                    We couldn't find the page you were looking for. It might have been removed, renamed, or doesn't
                    exist.
                </p>
                <a href="/" class="back-button">
                    Back to homepage
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M5 12h14"></path>
                        <path d="m12 5 7 7-7 7"></path>
                    </svg>
                </a>
            </div>
        </div>
    </main>
    <!-- Body main wrapper end -->
</body>

</html>