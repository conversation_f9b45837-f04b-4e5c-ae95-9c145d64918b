@use '../utils' as *;

/*----------------------------------------*/
/*  filter
/*----------------------------------------*/
.common-filter-box {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20px;
  padding: 30px;
  background: var(--td-white);
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.10);
  position: relative;

  @media #{$lg,$md} {
    grid-template-columns: repeat(3, 1fr);
  }

  @media #{$xs} {
    grid-template-columns: repeat(1, 1fr);
  }

  @media #{$sm} {
    grid-template-columns: repeat(2, 1fr);
  }

  .common-date-input-box {
    position: relative;

    .icon {
      position: absolute;
      inset-inline-end: 12px;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      .dropdown-icon {
        font-size: 20px;
        color: var(--td-heading);
      }
    }

    label {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      inset-inline-start: 12px;
      font-size: 14px;
      font-weight: 400;
      line-height: 100%;
      font-family: var(--td-heading-font);
      color: var(--td-heading);
      cursor: text;
    }

    input[type=text],
    input[type=search],
    input[type=email],
    input[type=tel],
    input[type=number],
    input[type=password],
    textarea {
      outline: none;
      height: 54px;
      width: 100%;
      padding: 0 12px;
      border-radius: 0px;
      border: 1px solid rgba(21, 20, 21, 0.16);
      color: var(--td-heading);
      background: var(--td-white);
      font-size: 14px;
      font-weight: 400;
      line-height: 100%;
      font-family: var(--td-body-font);
      text-align: end;
      padding-inline-end: 35px;

      &::placeholder {
        color: var(--td-heading);
        font-size: 14px;
        font-weight: 400;
        line-height: 100%;
        font-family: var(--td-body-font);
        text-align: end;
      }

      &:focus {
        border: 1px solid var(--td-primary);
      }
    }
  }

  .action-btn {
    @media #{$lg,$md} {
      grid-column: span 2;
    }

    @media #{$xs} {
      grid-column: span 1;
    }

    @media #{$sm} {
      grid-column: span 2;
    }
  }

  &-2 {
    background: transparent;
    box-shadow: none;
    border: 1px solid rgba(21, 20, 21, 0.16);
  }
}