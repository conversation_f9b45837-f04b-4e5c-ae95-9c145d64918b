@use '../../utils' as *;

/*----------------------------------------*/
/*  Promo styles
/*----------------------------------------*/
.popup-overlay {
    position: fixed;
    top: 0;
    inset-inline-start: 0;
    width: 100%;
    height: 100%;
    background-color: rgba($heading, $alpha: 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    transition: all 0.3s;
    visibility: visible;
    opacity: 1;

    &.hidden {
        visibility: hidden;
        opacity: 0;
    }
}

.promo-popup-main {
    position: relative;
    display: flex;
    width: 900px;
    max-width: 90%;
    background-color: var(--td-white);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    z-index: 1001;

    .promo-contents {
        flex: 1;
        padding: 40px 40px;
        background-color: var(--td-white);
        display: flex;
        flex-direction: column;
        justify-content: center;

        @media #{$xs} {
            padding: 30px 30px;
        }

        .logo {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }

        .heading {
            font-weight: 500;
            margin-bottom: 15px;
            font-family: var(--td-body-font);
            font-size: 20px;
        }

        .discount {
            font-weight: 400;
            color: #b79668;
            margin-bottom: 20px;
            font-size: 50px;
            font-family: var(--td-body-font);

            @media #{$xs} {
                font-size: 36px;
            }
        }

        .description {
            margin-bottom: 25px;
            line-height: 1.5;

            @media #{$xs} {
                margin-bottom: 16px;
            }
        }

        .subscription-form {
            margin-top: 10px;
            position: relative;
        }

        .email-input {
            border: 1px solid rgba(21, 20, 21, 0.16);
            height: 52px;
            padding-inline-end: 150px;

            @include td-placeholder {
                color: var(--td-text-primary) !important;
            }

            &:focus {
                border-color: var(--td-primary);
                color: var(--td-text-primary) !important;
            }

            @media #{$xxs} {
                padding-inline-end: 16px;
            }
        }

        .submit-button {
            height: 36px;
            position: absolute;
            inset-inline-end: 10px;
            top: 50%;
            transform: translateY(-50%);
            padding: 0 16px;
            border: 0;

            @media #{$xxs} {
                position: inherit;
                top: inherit;
                transform: inherit;
                margin-top: 16px;
                width: 100%;
            }

            &:hover {
                background-color: #a68457;

                &:hover {
                    color: var(--td-white);
                    background-color: var(--td-heading);
                }
            }
        }
    }

    .promp-image {
        flex: 1;
        position: relative;

        @media #{$xs} {
            display: none;
        }

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }

    .close-btn {
        position: absolute;
        top: -15px;
        inset-inline-end: -15px;
        width: 30px;
        height: 30px;
        background-color: rgba(255, 255, 255, 0.8);
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        z-index: 10;
        transition: background-color 0.3s;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

        svg {
            transform: rotate(180deg);
            transition: all 0.3s;
        }

        &:hover {
            background-color: rgba(255, 255, 255, 1);

            svg {
                transform: rotate(60deg);
            }
        }
    }

    .image-overlay {
        position: absolute;
        top: 50%;
        inset-inline-start: 50%;
        pointer-events: none;
        width: calc(100% - 30px);
        height: calc(100% - 30px);
        border: 1px solid #FFE4C0;
        transform: translate(-50%, -50%);

        @include rtl {
            inset-inline-start: auto;
            inset-inline-end: 50%;
        }
    }
}