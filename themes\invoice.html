<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Download Invoice</title>
    <style>
        /* google font */
        @import url('https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,100..900;1,100..900&display=swap');

        :root {
            --td-white: hsl(0, 0%, 100%);
            --td-black: hsl(0, 0%, 0%);
            --td-heading: #151415;
            --td-primary: #AA8453;
            --td-text-primary: #444344;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Jost", sans-serif;
            background-color: #151415;
            color: var(--td-text-primary);
            line-height: 1.6;
            padding: 20px;
            font-size: 14px;
        }

        a {
            text-decoration: none;
        }

        a,
        p {
            color: #4b4b4b;
        }

        .invoice-container {
            max-width: 800px;
            margin: auto;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            background: var(--td-white);
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #FFF4E5;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            gap: 12px;
            flex-wrap: wrap;
            padding: 20px;
        }

        .logo img {
            height: 4%;
        }

        .payable-info {
            text-align: end;
            font-size: 16px;
        }

        .payable-info p {
            margin-bottom: 5px;
        }

        .invoice-details-box {
            padding: 20px;
            color: #202228;
        }

        .invoice-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 50px;
            gap: 12px;
            flex-wrap: wrap;
        }

        .invoice-details h3 {
            margin-bottom: 8px;
        }

        .invoice-table-box {
            position: relative;
            margin-bottom: 35px;
        }

        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .invoice-table thead {
            background-color: #FFF4E5;
        }

        .invoice-table th,
        .invoice-table td {
            padding: 10px 10px;
            text-align: left;
        }

        .invoice-table tbody {
            border-bottom: 1px solid rgba(215, 218, 224, 0.5);
        }

        .footer-top {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            margin-bottom: 30px;
            gap: 16px;
        }

        .footer-bottom {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            gap: 16px;
        }

        .footer-bottom p {
            font-size: 18px;
        }

        .bank-info p {
            color: var(--td-white);
            text-align: left;
            font-weight: 500;
        }

        .payment-details strong {
            margin-bottom: 5px;
            display: block;
        }

        .button-container {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            margin-top: 50px;
            flex-wrap: wrap;
        }

        .primary-button {
            height: 40px;
            padding: 10px 24px;
            background: var(--td-primary);
            border: 1px solid var(--td-primary);
            color: var(--td-white);
            font-size: 0.875rem;
            font-weight: 400;
            line-height: normal;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease-in-out;
            cursor: pointer;
        }

        .primary-button:hover {
            background: transparent;
            border: 1px solid var(--td-primary);
            color: var(--td-heading);
        }

        .primary-button.white-hover:hover {
            background: transparent;
            border: 1px solid var(--td-white);
            color: var(--td-white);
        }

        .primary-button.primary-hover:hover {
            background: transparent;
            border: 1px solid var(--td-primary);
            color: var(--td-primary);
        }

        .primary-button.border-btn {
            background: transparent;
            border: 1px solid var(--td-primary);
            color: var(--td-heading);
        }

        .primary-button.border-btn:hover {
            background: var(--td-primary);
            border: 1px solid var(--td-primary);
            color: var(--td-white);
        }

        .primary-button.xl-btn {
            height: 50px;
            padding: 10px 26px;
            font-size: 1rem;
        }

        /* Adjustments for Small Screens */
        @media (max-width: 480px) {
            .invoice-table-box {
                overflow-x: scroll;
                padding-left: 0;
            }
        }

        /* Print Styles */
        @media print {
            body {
                background: var(--td-white) !important;
                color: var(--td-white) !important;
            }

            .invoice-container {
                -webkit-print-color-adjust: exact;
            }

            .print-button,
            .download-button {
                display: none !important;
            }
        }

        .app-badge.badge-success {
            background: #22AC3B;
            border: 1px solid rgba(17, 188, 13, 0.15);
            color: var(--td-white);
        }

        .app-badge.badge-danger {
            background: #ed4030;
            color: var(--td-white);
            border: 1px solid rgba(237, 64, 48, 0.1);
        }

        .app-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 7px 15px;
            line-height: 1;
            font-size: 14px;
        }
    </style>
</head>

<body>
    <div class="invoice-container">
        <header class="invoice-header">
            <div class="logo">
                <a href="#">
                    <img src="../assets/images/logo/logo.svg" alt="Invoice Logo">
                </a>
            </div>
            <div class="payable-info">
                <p>Invoice ID: 542516</p>
                <p>Reservation Date: 13 May 2025</p>
            </div>
        </header>
        <div class="invoice-details-box">
            <div class="invoice-details">
                <div class="billed-to">
                    <h3>shakibadnan</h3>
                    <p><EMAIL></p>
                    <p><a href="tel:12345679564">123 4567 9564</a></p>
                    <span class="app-badge badge-success" style="margin-top: 10px;">
                        Paid
                    </span>
                </div>
                <div class="from">
                    <h4 style="margin-bottom: 5px;">Total Amount: <br> 1462.00000000 USD</h4>
                    <p>Amount: 2,520.00 USD</p>
                    <p>Tax: 120.00 USD</p>
                    <p>Discount: (-) 100.00 USD</p>
                </div>
            </div>
            <div class="invoice-table-box">
                <table class="invoice-table">
                    <thead>
                        <tr>
                            <th>Item Name</th>
                            <th>Reservation</th>
                            <th>Total Days</th>
                            <th>Unit Price</th>
                            <th>Tax</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>City View Deluxe</td>
                            <td>21 May 2025 - 30 May 2025</td>
                            <td>10</td>
                            <td>2,500.00 USD</td>
                            <td>120.00 USD</td>
                            <td>2,620.00 USD</td>
                        </tr>
                    </tbody>
                    <tbody>
                        <tr>
                            <td colspan="5">Subtotal</td>
                            <td>2,620.00 USD</td>
                        </tr>
                        <tr>
                            <td colspan="5">Total Discount</td>
                            <td>(-) 100.00 USD</td>
                        </tr>
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="5"><strong>Total</strong></td>
                            <td>
                                <strong style="color: #000000;">2,520.00 USD</strong>
                            </td>
                        </tr>
                    </tfoot>
                </table>
            </div>
            <div class="footer">
                <div class="footer-bottom">
                    <p>Thanks for Booking</p>
                </div>
            </div>
            <div class="button-container">
                <button class="primary-button">
                    Back
                </button>
                <button class="primary-button border-btn">
                    Download PDF
                </button>
                <button class="primary-button" onclick="window.print()">
                    Print Invoice
                </button>
            </div>
        </div>
    </div>
</body>

</html>