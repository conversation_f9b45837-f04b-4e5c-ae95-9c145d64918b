@use '../../utils' as *;

/*----------------------------------------*/
/*  3.1 Header-1
/*----------------------------------------*/
.header-top {
    background-color: var(--td-secondary);
    padding: 10px 0;

    .header-top-content {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .left {
            p {
                color: var(--td-white);
                font-size: rem(14);
                font-weight: 400;
                line-height: normal;
            }
        }

        .right {
            display: flex;
            align-items: center;
            gap: 26px;

            a {
                color: var(--td-white);
                font-size: rem(14);
                font-weight: 400;
                line-height: normal;
            }

            .language-dropdown {
                .custom-nice-select {
                    .nice-select {
                        -webkit-tap-highlight-color: transparent;
                        background-color: transparent;
                        color: var(--td-white);
                        border-radius: 0px;
                        border: solid 1px #e8e8e800;
                        font-size: 14px;
                        font-weight: normal;
                        height: 20px;
                        line-height: 19px;
                        padding-inline-start: 0px;
                        padding-inline-end: 18px;
                    }

                    .nice-select:after {
                        border-color: 1px solid var(--td-white);
                        height: 7px;
                        margin-top: -5px;
                        inset-inline-end: 2px;
                        width: 7px;

                        @include rtl {
                            inset-inline-start: 100%;
                            width: 7px;
                            inset-inline-end: auto;
                            margin-inline-start: -9px;
                        }
                    }

                    .nice-select .list {
                        background-color: var(--td-white);
                        border-radius: 0px;
                        border: 1px solid #E5E5E5;
                        box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.10);
                        inset-inline-start: auto;
                        inset-inline-end: 0;
                        z-index: 90;
                    }

                    .nice-select .option {
                        cursor: pointer;
                        font-weight: 400;
                        line-height: 26px;
                        list-style: none;
                        min-height: 26px;
                        outline: none;
                        padding-inline-start: 14px;
                        padding-inline-end: 14px;
                        text-align: left;
                        -webkit-transition: all 0.2s;
                        transition: all 0.2s;
                        color: var(--td-heading);
                    }

                    .nice-select .option:hover,
                    .nice-select .option.focus,
                    .nice-select .option.selected.focus {
                        background-color: #f6f6f6;
                    }

                    .nice-select .option.selected {
                        font-weight: normal;
                    }
                }
            }
        }
    }
}

.main-header {
    background: var(--td-bg-1);
    backdrop-filter: blur(18px);
    height: 80px;
    padding: 0 20px;
    position: relative;
    z-index: 80;
    display: flex;
    align-items: center;

    @media #{$md} {
        height: 70px;
        padding: 0 15px;
    }

    @media #{$xs} {
        height: 60px;
    }

    @media #{$xs,$sm,$md} {
        padding: 0px 0;
    }

    .main-header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .left {
            .logo {
                display: inline-block;
                height: 40px;

                @media #{$md} {
                    height: 36px;
                }

                @media #{$xs} {
                    height: 30px;
                }

                img {
                    height: 100%;
                }
            }
        }

        .main-menu {
            ul {
                display: flex;
                align-items: center;
                gap: 30px;
                list-style-type: none;

                @media #{$lg,$md,$xs} {
                    gap: 22px;
                }

                li {
                    a {
                        color: var(--td-heading);
                        font-size: rem(16);
                        font-weight: 400;
                        line-height: normal;
                        transition: all 0.3s ease-in-out;

                        &:hover,
                        &.active {
                            color: var(--td-primary);
                        }
                    }

                    &.active {
                        a {
                            color: var(--td-primary);
                        }
                    }
                }
            }
        }

        .right {
            .btn-wrapper {
                display: flex;
                align-items: center;
                gap: 13px;

                .toggle-btn {
                    .htlib-toggle-btn {
                        display: flex;
                        align-items: center;

                        .menu-icon {
                            color: var(--td-heading);
                            font-size: 24px;
                        }
                    }
                }
            }
        }
    }
}

.language-dropdown-header-bar {
    .nice-select {
        background-color: transparent;
        border: solid 1px var(--td-primary);
        border-radius: 0px;
        height: 40px;
        line-height: 38px;

        @media #{$md,$xs} {
            height: 34px;
            line-height: 32px;
        }
    }

    .nice-select:after {
        border-bottom: 1px solid var(--td-heading);
        border-right: 1px solid var(--td-heading);
        height: 6px;
        width: 6px;
    }

    .nice-select .option:hover,
    .nice-select .option.focus,
    .nice-select .option.selected.focus {
        background-color: rgba(170, 132, 83, 0.2);
    }

    .nice-select .list {
        border-radius: 0px;
    }

    .nice-select .option.selected {
        font-weight: normal;
    }
}