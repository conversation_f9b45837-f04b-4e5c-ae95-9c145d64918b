@use '../utils' as *;

/*----------------------------------------*/
/*  pagination
/*----------------------------------------*/
.common-pagination {
  display: flex;
  justify-content: center;
  align-items: center;

  ul {
    display: flex;
    align-items: center;
    gap: 10px;
    list-style-type: none;

    li {
      a {
        font-family: var(--td-heading-font);
        display: flex;
        width: 40px;
        height: 40px;
        padding: 10px;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border: 1px solid rgba(170, 132, 83, 0.30);
        color: var(--td-heading);
        text-align: center;
        font-size: 20px;
        font-weight: 400;
        line-height: normal;

        &:hover,
        &.active {
          border: 1px solid var(--td-primary);
          background: #FFF4E5;
        }

        &.navigation {
          &.disabled {
            .arrow {
              opacity: 0.2;
              cursor: not-allowed;
            }
          }

          &:hover,
          &.active {
            border-color: var(--td-primary);
            background: var(--td-primary);

            .arrow {
              color: var(--td-white);
              opacity: 1;
            }
          }
        }
      }
    }
  }
}