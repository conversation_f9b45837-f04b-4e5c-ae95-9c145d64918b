@use '../utils' as *;

/*----------------------------------------*/
/*  2.6 headings
/*----------------------------------------*/
.common-title-head {
    background: rgba(170, 132, 83, 0.30);
    backdrop-filter: blur(1px);
    display: inline-flex;
    height: 36px;
    padding: 10px 16px;
    align-items: center;
    gap: 6px;
    position: relative;
    margin-bottom: 10px;

    @media #{$xs} {
        padding: 4px 8px;
    }

    @media #{$sm} {
        padding: 8px 12px;
    }

    p {
        color: var(--td-white);
        text-align: center;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        text-transform: uppercase;
        display: flex;
        align-items: center;
        gap: 6px;

        @media #{$xs} {
            font-size: 10px;
        }

        @media #{$sm} {
            font-size: 12px;
        }

        &::before {
            content: "";
            display: inline-block;
            width: 16px;
            height: 16px;
            background-image: url('../images/icon/text-left-img.svg');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        &::after {
            content: "";
            display: inline-block;
            width: 16px;
            height: 16px;
            background-image: url('../images/icon/text-right-img.svg');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
    }

    &-2 {
        background: rgba(170, 132, 83, 0.16);
        backdrop-filter: blur(1px);

        p {
            color: var(--td-primary);

            &::before {
                background-image: url('../images/icon/text-left-img-primary.svg');

            }

            &::after {
                background-image: url('../images/icon/text-right-img-primary.svg');
            }
        }
    }
}

.common-section-title {
    display: flex;
    justify-content: space-between;
    align-items: end;

    @media #{$xs} {
        flex-direction: column;
        align-items: start;
        justify-content: start;
    }

    .left {
        width: 45%;

        @media #{$lg,$md} {
            width: 60%;
        }

        @media #{$xs} {
            width: 100%;
        }

        .section-title {
            color: var(--td-heading);
            font-size: 60px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            margin-bottom: 16px;

            @media #{$xl} {
                font-size: 55px;
            }

            @media #{$lg} {
                font-size: 45px;
            }

            @media #{$md} {
                font-size: 35px;
            }

            @media #{$xs} {
                font-size: 26px;
                margin-bottom: 10px;
            }

            @media #{$sm} {
                font-size: 30px;
                margin-bottom: 10px;
            }

            .highlight-text {
                display: inline-block;
                position: relative;

                .highlight-text-img {
                    position: absolute;
                    top: 50%;
                    transform: translateY(-50%);
                    inset-inline-end: -60px;
                    width: 275px;
                    height: 85px;

                    @media #{$lg} {
                        width: 216px;
                        height: 68px;
                    }

                    @media #{$md} {
                        width: 185px;
                        height: 55px;
                    }

                    @media #{$xs} {
                        width: 100px;
                        height: 55px;
                        inset-inline-end: -15px;
                        top: 10%;
                        transform: translateY(-10%);
                    }

                    @media #{$sm} {
                        width: 125px;
                        height: 55px;
                        inset-inline-end: -20px;
                    }

                    &-2 {
                        width: 206px;
                        height: 63px;
                        inset-inline-end: -40px;
                        top: 0%;
                        transform: translateY(-0%);

                        @media #{$lg} {
                            width: 160px;
                            height: 68px;
                        }

                        @media #{$md} {
                            width: 135px;
                            height: 55px;
                        }

                        @media #{$xs} {
                            width: 100px;
                            height: 55px;
                            inset-inline-end: -20px;
                        }
                    }

                    &-3 {
                        width: 180px;
                        height: 70px;
                        inset-inline-end: -30px;
                        top: 0%;
                        transform: translateY(-0%);

                        @media #{$lg} {
                            width: 150px;
                            height: 68px;
                        }

                        @media #{$md} {
                            width: 115px;
                            height: 55px;
                        }

                        @media #{$xs} {
                            width: 90px;
                            height: 55px;
                            inset-inline-end: -20px;
                        }
                    }
                }
            }
        }

        .section-description {
            color: var(--td-text-primary);
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: lh(26, 16);

            @media #{$md,$xs} {
                font-size: 14px;
            }
        }
    }

    .right {
        @media #{$xs} {
            margin-top: 10px;
        }
    }

    &-2 {
        .left {
            width: 100%;

            .section-title {
                width: 50%;

                @media #{$xxl,$xl,$lg,$md,$xs} {
                    width: 60%;
                }

                @media #{$xs} {
                    width: 100%;
                }

                @media #{$sm} {
                    width: 60%;
                }
            }

            .left-action-btn {
                margin-top: 40px;

                @media #{$xs} {
                    margin-top: 20px;
                }

                @media #{$sm} {
                    margin-top: 30px;
                }
            }
        }
    }

    &-3 {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .left {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 60%;

            @media #{$lg,$md} {
                width: 80%;
            }

            @media #{$xs} {
                width: 100%;
            }

            .section-title {
                text-align: center;

                .highlight-text {
                    .highlight-text-img {
                        inset-inline-end: -20px;
                    }
                }
            }

            .section-description {
                text-align: center;
            }
        }
    }

    &-4 {
        .left {
            width: 42%;

            @media #{$lg,$md} {
                width: 80%;
            }

            @media #{$xs} {
                width: 100%;
            }

            .section-title {
                text-align: center;

                .highlight-text {
                    .highlight-text-img {
                        inset-inline-end: -20px;

                        @media #{$md} {
                            inset-inline-end: -0px;
                        }

                        @media #{$xs} {
                            inset-inline-end: -0px;
                        }
                    }
                }
            }
        }
    }
}

.hero-title {
    color: var(--td-white);
    text-align: center;
    font-size: 80px;
    font-weight: 400;
    line-height: lh(96, 80);
    margin-bottom: 16px;

    @media #{$xl} {
        font-size: 70px;
    }

    @media #{$lg} {
        font-size: 60px;
    }

    @media #{$md} {
        font-size: 50px;
    }

    @media #{$xs} {
        font-size: 30px;
        margin-bottom: 10px;
    }

    @media #{$sm} {
        font-size: 40px;
        margin-bottom: 10px;
    }
}

.hero-subtitle {
    color: #FFF4E5;
    text-align: center;
    font-size: 20px;
    font-weight: 300;
    line-height: lh(30, 20);
    padding: 0 30px;
    margin-bottom: 40px;

    @media #{$md} {
        font-size: 18px;
    }

    @media #{$xs} {
        font-size: 14px;
        padding: 0 0px;
        margin-bottom: 20px;
    }

    @media #{$sm} {
        font-size: 16px;
        padding: 0 0px;
        margin-bottom: 30px;
    }
}

.common-page-header {
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
    padding: 32px 0;

    @media #{$md} {
        padding: 28px 0;
    }

    @media #{$xs} {
        padding: 24px 0;
    }

    @media #{$sm} {
        padding: 26px 0;
    }

    .page-header-overlay {
        background: rgba(170, 132, 83, 0.40);
        backdrop-filter: blur(4.5px);
        padding: 50px 0;

        @media #{$lg} {
            padding: 45px 0;
        }

        @media #{$md} {
            padding: 40px 0;
        }

        @media #{$xs} {
            padding: 20px 0;
        }

        @media #{$sm} {
            padding: 30px 0;
        }

        h5 {
            color: var(--td-white);
            text-align: center;
            font-size: 30px;
            font-weight: 400;
            line-height: normal;

            @media #{$lg} {
                font-size: 28px;
            }

            @media #{$md} {
                font-size: 26px;
            }

            @media #{$xs} {
                font-size: 20px;
            }

            @media #{$sm} {
                font-size: 24px;
            }
        }
    }
}