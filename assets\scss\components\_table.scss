@use '../utils' as *;

/*----------------------------------------*/
/*  Dashboard table styles 
/*----------------------------------------*/

.custom-table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--td-white);
    border: 1px solid rgba($heading, $alpha: 0.2);

    &.booking-table {
        min-width: 1000px;
    }

    thead {
        tr {
            th {
                background: #FFF4E5;
                padding: 12px 1rem;
                font-size: 16px;
                font-weight: 500;
                font-family: var(--td-heading-font);

            }
        }
    }

    th {
        padding: 15px rem(15);
        text-align: left;
        border-bottom: 1px solid rgba($heading, $alpha: 0.16);
    }

    td {
        padding: 15px rem(16);
        text-align: left;
        border-bottom: 1px solid rgba($heading, $alpha: 0.16);
        font-size: 14px;
        font-weight: 500;
        color: #444344;
    }

    tr {
        &:hover {
            // background-color: #f9f9f9;
        }
    }
}

.action-buttons-wrap {
    display: flex;
    align-items: center;
    gap: 12px;
}

.table-action-btn {
    display: flex;
    width: 34px;
    height: 34px;
    padding: 0px 11px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    background: #AA8453;

    .btn-icon {
        font-size: 18px;
        display: flex;
        align-items: center;
        color: var(--td-white);
    }

    &.btn-outline {
        background-color: transparent;
        border: 1px solid var(--td-primary);

        .btn-icon {
            color: var(--td-black);
        }
    }
}