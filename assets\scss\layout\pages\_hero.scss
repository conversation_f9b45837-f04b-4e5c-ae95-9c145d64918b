@use '../../utils' as *;

/*----------------------------------------*/
/*  hero
/*----------------------------------------*/
.hero {
  position: relative;
  width: 100%;

  .hero-main {
    .hero-video {
      z-index: 1 !important;
    }
    .hero-video,
    .hero-thumb {
      position: absolute;
      top: 0;
      inset-inline-start: 0;
      width: 100%;
      height: 100%;
      z-index: 0;
    }

    video {
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center;
    }

    .video-overlay {
      position: absolute;
      top: 0;
      inset-inline-start: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(0deg, rgba(0, 0, 0, 0.50) 0%, rgba(0, 0, 0, 0.50) 100%);
      z-index: 1;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .container {
    position: relative;
    z-index: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 120px 20px 188px 20px;

    @media #{$xl} {
      padding: 100px 20px 178px 20px;
    }

    @media #{$lg} {
      padding: 90px 20px 168px 20px;
    }

    @media #{$md} {
      padding: 80px 20px 158px 20px;
    }

    @media #{$xs} {
      padding: 60px 20px 138px 20px;
    }

    @media #{$sm} {
      padding: 70px 20px 148px 20px;
    }

    .hero-content {
      max-width: 850px;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
    }

    .hero-filter {
      position: absolute;
      bottom: 0;
      inset-inline-start: 0;
      width: 100%;
      z-index: 5;
      transform: translateY(55px);

      @media #{$lg,$md,$xs} {
        transform: translateY(95px);
      }

      @media #{$xs} {
        transform: translateY(320px);
      }

      @media #{$sm} {
        transform: translateY(155px);
      }
    }
  }
}

.no-video .hero-main {
  video {
    display: none;
  }

  img {
    display: block;
  }
}

.extra-mt {
  margin-top: 55px;

  @media #{$lg,$md,$xs} {
    margin-top: 95px;
  }

  @media #{$xs} {
    margin-top: 320px;
  }

  @media #{$sm} {
    margin-top: 155px;
  }
}