@use '../utils' as *;

/*----------------------------------------*/
/*  Custom dropdown
/*----------------------------------------*/
.custom-dropdown-box {
  position: relative;

  .custom-dropdown-btn {
    .input {
      height: 54px;
      width: 100%;
      padding: 0 12px;
      border-radius: 0px;
      border: 1px solid rgba(21, 20, 21, 0.16);
      color: var(--td-heading);
      background: var(--td-white);
      display: flex;
      justify-content: space-between;
      align-items: center;

      .left {
        p {
          font-family: var(--td-heading-font);
          font-size: 14px;
          font-weight: 400;
          color: var(--td-heading);

          span {
            color: #FF6A00;
            font-family: var(--td-body-font);
            font-size: 14px;
            font-weight: 500;
            line-height: lh(26, 14);
          }
        }
      }

      .right {
        display: flex;
        align-items: center;
        gap: 10px;
      }

      p {
        font-size: 14px;
      }

      &.open {
        border: 1px solid var(--td-primary);
      }
    }

    .icon {
      display: inline-flex;
      transition: all 0.3s ease-in-out;

      .dropdown-icon {
        font-size: 20px;
        color: var(--td-heading);
      }

      &.open {
        transform: rotate(180deg);
      }
    }
  }

  .custom-dropdown-content {
    position: absolute;
    top: 100%;
    margin-top: 5px;
    inset-inline-start: 0;
    width: 100%;
    padding: 18px;
    border: 1px solid #E5E5E5;
    background: var(--td-white);
    box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.10);
    visibility: hidden;
    opacity: 0;
    transform: translateY(-5px);
    transition: all 0.3s ease-in-out;
    z-index: 2;

    &.open {
      visibility: visible;
      opacity: 1;
      transform: translateY(0px);
    }

  }
}

.counter-card-box {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.counter-card {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .counter-text {

    h5 {
      font-family: var(--td-heading-font);
      color: var(--td-heading);
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }
}

.counter {
  display: flex;
  align-items: center;
  gap: 8px;

  .counter-btn {
    display: flex;
    justify-content: center;
    align-items: center;

    .count-iocn {
      font-size: 18px;
      color: var(--td-heading);
    }
  }

  .count {
    width: 30px;
    height: 30px;
    font-family: var(--td-heading-font);
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--td-heading);
    text-align: center;
    font-size: rem(18);
    font-weight: 400;
    line-height: lh(20, 14);
  }
}

.common-counter {
  display: flex;
  padding: 7.5px 15px;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  border-radius: 40px;
  border: 1px solid rgba(8, 8, 8, 0.10);
  height: 35px;

  .title {
    h5 {
      color: var(--td-heading);
      font-size: rem(14);
      font-weight: 500;
      line-height: lh(20, 14);
    }
  }
}

.user-dropdown-full {
  position: relative;

  .user-dropdown-btn {
    display: flex;
    width: 40px;
    height: 40px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    background-color: var(--td-primary);
    cursor: pointer;


    @media #{$xs} {
      width: 34px;
      height: 34px;
    }

    .user-icon {
      font-size: 22px;
      color: var(--td-white);

      @media #{$xs} {
        font-size: 18px;
      }
    }
  }

  .user-dropdown-content-2 {
    position: absolute;
    top: 100%;
    margin-top: 10px;
    inset-inline-end: 0;
    width: 235px;
    background: var(--td-white);
    visibility: hidden;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease-in-out;
    z-index: 10;

    .header {
      padding: 10px 18px;
      display: flex;
      align-items: center;
      gap: 12px;
      border-bottom: 1px solid rgba(21, 20, 21, 0.16);

      .img {
        width: 31px;
        height: 31px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .content {
        p {
          color: var(--td-heading);
          text-align: center;
          font-size: 18px;
          font-weight: 500;
          line-height: lh(26, 16);
        }
      }
    }

    .user-link {
      ul {
        list-style-type: none;

        li {

          a {
            display: flex;
            align-items: center;
            gap: 6px;
            color: var(--td-text-primary);
            font-size: 16px;
            font-weight: 500;
            line-height: normal;
            padding: 10px 18px;
            transition: all 0.3s ease-in-out;

            .icon {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 20px;
              height: 20px;
              flex-shrink: 0;

              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }

            &:hover {
              background-color: #F2F2F2;
            }

            &.logout {
              background: rgba(233, 58, 45, 0.10);
              color: #E93A2D;

              &:hover {
                background: rgba(233, 58, 45, 0.20);
              }
            }
          }
        }
      }
    }

    &.active {
      visibility: visible;
      opacity: 1;
      transform: translateY(0px);
    }
  }
}