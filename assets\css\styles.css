@charset "UTF-8";
/*-----------------------------------------------------------------------------------

  Project Name:  Hotelib - Welcome to Hotelib Hotel, where luxury meets the skyline.
  Author: Tdevs
  Support: 
  Description:  We are dedicated to being your partner in talent  acquisition. 
  Version: 1.0

-----------------------------------------------------------------------------------


/*----------------------------------------*/
/*   1.1 Globals
/*----------------------------------------*/
@import url("https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,100..900;1,100..900&family=Marcellus&display=swap");
.mt-1 {
  margin-top: 1px;
}

.mb-1 {
  margin-bottom: 1px;
}

.ml-1 {
  margin-inset-inline-start: 1px;
}

.mr-1 {
  margin-inset-inline-end: 1px;
}

.mt-2 {
  margin-top: 2px;
}

.mb-2 {
  margin-bottom: 2px;
}

.ml-2 {
  margin-inset-inline-start: 2px;
}

.mr-2 {
  margin-inset-inline-end: 2px;
}

.mt-3 {
  margin-top: 3px;
}

.mb-3 {
  margin-bottom: 3px;
}

.ml-3 {
  margin-inset-inline-start: 3px;
}

.mr-3 {
  margin-inset-inline-end: 3px;
}

.mt-4 {
  margin-top: 4px;
}

.mb-4 {
  margin-bottom: 4px;
}

.ml-4 {
  margin-inset-inline-start: 4px;
}

.mr-4 {
  margin-inset-inline-end: 4px;
}

.mt-5 {
  margin-top: 5px;
}

.mb-5 {
  margin-bottom: 5px;
}

.ml-5 {
  margin-inset-inline-start: 5px;
}

.mr-5 {
  margin-inset-inline-end: 5px;
}

.mt-6 {
  margin-top: 6px;
}

.mb-6 {
  margin-bottom: 6px;
}

.ml-6 {
  margin-inset-inline-start: 6px;
}

.mr-6 {
  margin-inset-inline-end: 6px;
}

.mt-7 {
  margin-top: 7px;
}

.mb-7 {
  margin-bottom: 7px;
}

.ml-7 {
  margin-inset-inline-start: 7px;
}

.mr-7 {
  margin-inset-inline-end: 7px;
}

.mt-8 {
  margin-top: 8px;
}

.mb-8 {
  margin-bottom: 8px;
}

.ml-8 {
  margin-inset-inline-start: 8px;
}

.mr-8 {
  margin-inset-inline-end: 8px;
}

.mt-9 {
  margin-top: 9px;
}

.mb-9 {
  margin-bottom: 9px;
}

.ml-9 {
  margin-inset-inline-start: 9px;
}

.mr-9 {
  margin-inset-inline-end: 9px;
}

.mt-10 {
  margin-top: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.ml-10 {
  margin-inset-inline-start: 10px;
}

.mr-10 {
  margin-inset-inline-end: 10px;
}

.mt-11 {
  margin-top: 11px;
}

.mb-11 {
  margin-bottom: 11px;
}

.ml-11 {
  margin-inset-inline-start: 11px;
}

.mr-11 {
  margin-inset-inline-end: 11px;
}

.mt-12 {
  margin-top: 12px;
}

.mb-12 {
  margin-bottom: 12px;
}

.ml-12 {
  margin-inset-inline-start: 12px;
}

.mr-12 {
  margin-inset-inline-end: 12px;
}

.mt-13 {
  margin-top: 13px;
}

.mb-13 {
  margin-bottom: 13px;
}

.ml-13 {
  margin-inset-inline-start: 13px;
}

.mr-13 {
  margin-inset-inline-end: 13px;
}

.mt-14 {
  margin-top: 14px;
}

.mb-14 {
  margin-bottom: 14px;
}

.ml-14 {
  margin-inset-inline-start: 14px;
}

.mr-14 {
  margin-inset-inline-end: 14px;
}

.mt-15 {
  margin-top: 15px;
}

.mb-15 {
  margin-bottom: 15px;
}

.ml-15 {
  margin-inset-inline-start: 15px;
}

.mr-15 {
  margin-inset-inline-end: 15px;
}

.mt-16 {
  margin-top: 16px;
}

.mb-16 {
  margin-bottom: 16px;
}

.ml-16 {
  margin-inset-inline-start: 16px;
}

.mr-16 {
  margin-inset-inline-end: 16px;
}

.mt-17 {
  margin-top: 17px;
}

.mb-17 {
  margin-bottom: 17px;
}

.ml-17 {
  margin-inset-inline-start: 17px;
}

.mr-17 {
  margin-inset-inline-end: 17px;
}

.mt-18 {
  margin-top: 18px;
}

.mb-18 {
  margin-bottom: 18px;
}

.ml-18 {
  margin-inset-inline-start: 18px;
}

.mr-18 {
  margin-inset-inline-end: 18px;
}

.mt-19 {
  margin-top: 19px;
}

.mb-19 {
  margin-bottom: 19px;
}

.ml-19 {
  margin-inset-inline-start: 19px;
}

.mr-19 {
  margin-inset-inline-end: 19px;
}

.mt-20 {
  margin-top: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.ml-20 {
  margin-inset-inline-start: 20px;
}

.mr-20 {
  margin-inset-inline-end: 20px;
}

.mt-21 {
  margin-top: 21px;
}

.mb-21 {
  margin-bottom: 21px;
}

.ml-21 {
  margin-inset-inline-start: 21px;
}

.mr-21 {
  margin-inset-inline-end: 21px;
}

.mt-22 {
  margin-top: 22px;
}

.mb-22 {
  margin-bottom: 22px;
}

.ml-22 {
  margin-inset-inline-start: 22px;
}

.mr-22 {
  margin-inset-inline-end: 22px;
}

.mt-23 {
  margin-top: 23px;
}

.mb-23 {
  margin-bottom: 23px;
}

.ml-23 {
  margin-inset-inline-start: 23px;
}

.mr-23 {
  margin-inset-inline-end: 23px;
}

.mt-24 {
  margin-top: 24px;
}

.mb-24 {
  margin-bottom: 24px;
}

.ml-24 {
  margin-inset-inline-start: 24px;
}

.mr-24 {
  margin-inset-inline-end: 24px;
}

.mt-25 {
  margin-top: 25px;
}

.mb-25 {
  margin-bottom: 25px;
}

.ml-25 {
  margin-inset-inline-start: 25px;
}

.mr-25 {
  margin-inset-inline-end: 25px;
}

.mt-26 {
  margin-top: 26px;
}

.mb-26 {
  margin-bottom: 26px;
}

.ml-26 {
  margin-inset-inline-start: 26px;
}

.mr-26 {
  margin-inset-inline-end: 26px;
}

.mt-27 {
  margin-top: 27px;
}

.mb-27 {
  margin-bottom: 27px;
}

.ml-27 {
  margin-inset-inline-start: 27px;
}

.mr-27 {
  margin-inset-inline-end: 27px;
}

.mt-28 {
  margin-top: 28px;
}

.mb-28 {
  margin-bottom: 28px;
}

.ml-28 {
  margin-inset-inline-start: 28px;
}

.mr-28 {
  margin-inset-inline-end: 28px;
}

.mt-29 {
  margin-top: 29px;
}

.mb-29 {
  margin-bottom: 29px;
}

.ml-29 {
  margin-inset-inline-start: 29px;
}

.mr-29 {
  margin-inset-inline-end: 29px;
}

.mt-30 {
  margin-top: 30px;
}

.mb-30 {
  margin-bottom: 30px;
}

.ml-30 {
  margin-inset-inline-start: 30px;
}

.mr-30 {
  margin-inset-inline-end: 30px;
}

.mt-31 {
  margin-top: 31px;
}

.mb-31 {
  margin-bottom: 31px;
}

.ml-31 {
  margin-inset-inline-start: 31px;
}

.mr-31 {
  margin-inset-inline-end: 31px;
}

.mt-32 {
  margin-top: 32px;
}

.mb-32 {
  margin-bottom: 32px;
}

.ml-32 {
  margin-inset-inline-start: 32px;
}

.mr-32 {
  margin-inset-inline-end: 32px;
}

.mt-33 {
  margin-top: 33px;
}

.mb-33 {
  margin-bottom: 33px;
}

.ml-33 {
  margin-inset-inline-start: 33px;
}

.mr-33 {
  margin-inset-inline-end: 33px;
}

.mt-34 {
  margin-top: 34px;
}

.mb-34 {
  margin-bottom: 34px;
}

.ml-34 {
  margin-inset-inline-start: 34px;
}

.mr-34 {
  margin-inset-inline-end: 34px;
}

.mt-35 {
  margin-top: 35px;
}

.mb-35 {
  margin-bottom: 35px;
}

.ml-35 {
  margin-inset-inline-start: 35px;
}

.mr-35 {
  margin-inset-inline-end: 35px;
}

.mt-36 {
  margin-top: 36px;
}

.mb-36 {
  margin-bottom: 36px;
}

.ml-36 {
  margin-inset-inline-start: 36px;
}

.mr-36 {
  margin-inset-inline-end: 36px;
}

.mt-37 {
  margin-top: 37px;
}

.mb-37 {
  margin-bottom: 37px;
}

.ml-37 {
  margin-inset-inline-start: 37px;
}

.mr-37 {
  margin-inset-inline-end: 37px;
}

.mt-38 {
  margin-top: 38px;
}

.mb-38 {
  margin-bottom: 38px;
}

.ml-38 {
  margin-inset-inline-start: 38px;
}

.mr-38 {
  margin-inset-inline-end: 38px;
}

.mt-39 {
  margin-top: 39px;
}

.mb-39 {
  margin-bottom: 39px;
}

.ml-39 {
  margin-inset-inline-start: 39px;
}

.mr-39 {
  margin-inset-inline-end: 39px;
}

.mt-40 {
  margin-top: 40px;
}

.mb-40 {
  margin-bottom: 40px;
}

.ml-40 {
  margin-inset-inline-start: 40px;
}

.mr-40 {
  margin-inset-inline-end: 40px;
}

.mt-41 {
  margin-top: 41px;
}

.mb-41 {
  margin-bottom: 41px;
}

.ml-41 {
  margin-inset-inline-start: 41px;
}

.mr-41 {
  margin-inset-inline-end: 41px;
}

.mt-42 {
  margin-top: 42px;
}

.mb-42 {
  margin-bottom: 42px;
}

.ml-42 {
  margin-inset-inline-start: 42px;
}

.mr-42 {
  margin-inset-inline-end: 42px;
}

.mt-43 {
  margin-top: 43px;
}

.mb-43 {
  margin-bottom: 43px;
}

.ml-43 {
  margin-inset-inline-start: 43px;
}

.mr-43 {
  margin-inset-inline-end: 43px;
}

.mt-44 {
  margin-top: 44px;
}

.mb-44 {
  margin-bottom: 44px;
}

.ml-44 {
  margin-inset-inline-start: 44px;
}

.mr-44 {
  margin-inset-inline-end: 44px;
}

.mt-45 {
  margin-top: 45px;
}

.mb-45 {
  margin-bottom: 45px;
}

.ml-45 {
  margin-inset-inline-start: 45px;
}

.mr-45 {
  margin-inset-inline-end: 45px;
}

.mt-46 {
  margin-top: 46px;
}

.mb-46 {
  margin-bottom: 46px;
}

.ml-46 {
  margin-inset-inline-start: 46px;
}

.mr-46 {
  margin-inset-inline-end: 46px;
}

.mt-47 {
  margin-top: 47px;
}

.mb-47 {
  margin-bottom: 47px;
}

.ml-47 {
  margin-inset-inline-start: 47px;
}

.mr-47 {
  margin-inset-inline-end: 47px;
}

.mt-48 {
  margin-top: 48px;
}

.mb-48 {
  margin-bottom: 48px;
}

.ml-48 {
  margin-inset-inline-start: 48px;
}

.mr-48 {
  margin-inset-inline-end: 48px;
}

.mt-49 {
  margin-top: 49px;
}

.mb-49 {
  margin-bottom: 49px;
}

.ml-49 {
  margin-inset-inline-start: 49px;
}

.mr-49 {
  margin-inset-inline-end: 49px;
}

.mt-50 {
  margin-top: 50px;
}

.mb-50 {
  margin-bottom: 50px;
}

.ml-50 {
  margin-inset-inline-start: 50px;
}

.mr-50 {
  margin-inset-inline-end: 50px;
}

.mt-51 {
  margin-top: 51px;
}

.mb-51 {
  margin-bottom: 51px;
}

.ml-51 {
  margin-inset-inline-start: 51px;
}

.mr-51 {
  margin-inset-inline-end: 51px;
}

.mt-52 {
  margin-top: 52px;
}

.mb-52 {
  margin-bottom: 52px;
}

.ml-52 {
  margin-inset-inline-start: 52px;
}

.mr-52 {
  margin-inset-inline-end: 52px;
}

.mt-53 {
  margin-top: 53px;
}

.mb-53 {
  margin-bottom: 53px;
}

.ml-53 {
  margin-inset-inline-start: 53px;
}

.mr-53 {
  margin-inset-inline-end: 53px;
}

.mt-54 {
  margin-top: 54px;
}

.mb-54 {
  margin-bottom: 54px;
}

.ml-54 {
  margin-inset-inline-start: 54px;
}

.mr-54 {
  margin-inset-inline-end: 54px;
}

.mt-55 {
  margin-top: 55px;
}

.mb-55 {
  margin-bottom: 55px;
}

.ml-55 {
  margin-inset-inline-start: 55px;
}

.mr-55 {
  margin-inset-inline-end: 55px;
}

.mt-56 {
  margin-top: 56px;
}

.mb-56 {
  margin-bottom: 56px;
}

.ml-56 {
  margin-inset-inline-start: 56px;
}

.mr-56 {
  margin-inset-inline-end: 56px;
}

.mt-57 {
  margin-top: 57px;
}

.mb-57 {
  margin-bottom: 57px;
}

.ml-57 {
  margin-inset-inline-start: 57px;
}

.mr-57 {
  margin-inset-inline-end: 57px;
}

.mt-58 {
  margin-top: 58px;
}

.mb-58 {
  margin-bottom: 58px;
}

.ml-58 {
  margin-inset-inline-start: 58px;
}

.mr-58 {
  margin-inset-inline-end: 58px;
}

.mt-59 {
  margin-top: 59px;
}

.mb-59 {
  margin-bottom: 59px;
}

.ml-59 {
  margin-inset-inline-start: 59px;
}

.mr-59 {
  margin-inset-inline-end: 59px;
}

.mt-60 {
  margin-top: 60px;
}

.mb-60 {
  margin-bottom: 60px;
}

.ml-60 {
  margin-inset-inline-start: 60px;
}

.mr-60 {
  margin-inset-inline-end: 60px;
}

.mt-61 {
  margin-top: 61px;
}

.mb-61 {
  margin-bottom: 61px;
}

.ml-61 {
  margin-inset-inline-start: 61px;
}

.mr-61 {
  margin-inset-inline-end: 61px;
}

.mt-62 {
  margin-top: 62px;
}

.mb-62 {
  margin-bottom: 62px;
}

.ml-62 {
  margin-inset-inline-start: 62px;
}

.mr-62 {
  margin-inset-inline-end: 62px;
}

.mt-63 {
  margin-top: 63px;
}

.mb-63 {
  margin-bottom: 63px;
}

.ml-63 {
  margin-inset-inline-start: 63px;
}

.mr-63 {
  margin-inset-inline-end: 63px;
}

.mt-64 {
  margin-top: 64px;
}

.mb-64 {
  margin-bottom: 64px;
}

.ml-64 {
  margin-inset-inline-start: 64px;
}

.mr-64 {
  margin-inset-inline-end: 64px;
}

.mt-65 {
  margin-top: 65px;
}

.mb-65 {
  margin-bottom: 65px;
}

.ml-65 {
  margin-inset-inline-start: 65px;
}

.mr-65 {
  margin-inset-inline-end: 65px;
}

.mt-66 {
  margin-top: 66px;
}

.mb-66 {
  margin-bottom: 66px;
}

.ml-66 {
  margin-inset-inline-start: 66px;
}

.mr-66 {
  margin-inset-inline-end: 66px;
}

.mt-67 {
  margin-top: 67px;
}

.mb-67 {
  margin-bottom: 67px;
}

.ml-67 {
  margin-inset-inline-start: 67px;
}

.mr-67 {
  margin-inset-inline-end: 67px;
}

.mt-68 {
  margin-top: 68px;
}

.mb-68 {
  margin-bottom: 68px;
}

.ml-68 {
  margin-inset-inline-start: 68px;
}

.mr-68 {
  margin-inset-inline-end: 68px;
}

.mt-69 {
  margin-top: 69px;
}

.mb-69 {
  margin-bottom: 69px;
}

.ml-69 {
  margin-inset-inline-start: 69px;
}

.mr-69 {
  margin-inset-inline-end: 69px;
}

.mt-70 {
  margin-top: 70px;
}

.mb-70 {
  margin-bottom: 70px;
}

.ml-70 {
  margin-inset-inline-start: 70px;
}

.mr-70 {
  margin-inset-inline-end: 70px;
}

.mt-71 {
  margin-top: 71px;
}

.mb-71 {
  margin-bottom: 71px;
}

.ml-71 {
  margin-inset-inline-start: 71px;
}

.mr-71 {
  margin-inset-inline-end: 71px;
}

.mt-72 {
  margin-top: 72px;
}

.mb-72 {
  margin-bottom: 72px;
}

.ml-72 {
  margin-inset-inline-start: 72px;
}

.mr-72 {
  margin-inset-inline-end: 72px;
}

.mt-73 {
  margin-top: 73px;
}

.mb-73 {
  margin-bottom: 73px;
}

.ml-73 {
  margin-inset-inline-start: 73px;
}

.mr-73 {
  margin-inset-inline-end: 73px;
}

.mt-74 {
  margin-top: 74px;
}

.mb-74 {
  margin-bottom: 74px;
}

.ml-74 {
  margin-inset-inline-start: 74px;
}

.mr-74 {
  margin-inset-inline-end: 74px;
}

.mt-75 {
  margin-top: 75px;
}

.mb-75 {
  margin-bottom: 75px;
}

.ml-75 {
  margin-inset-inline-start: 75px;
}

.mr-75 {
  margin-inset-inline-end: 75px;
}

.mt-76 {
  margin-top: 76px;
}

.mb-76 {
  margin-bottom: 76px;
}

.ml-76 {
  margin-inset-inline-start: 76px;
}

.mr-76 {
  margin-inset-inline-end: 76px;
}

.mt-77 {
  margin-top: 77px;
}

.mb-77 {
  margin-bottom: 77px;
}

.ml-77 {
  margin-inset-inline-start: 77px;
}

.mr-77 {
  margin-inset-inline-end: 77px;
}

.mt-78 {
  margin-top: 78px;
}

.mb-78 {
  margin-bottom: 78px;
}

.ml-78 {
  margin-inset-inline-start: 78px;
}

.mr-78 {
  margin-inset-inline-end: 78px;
}

.mt-79 {
  margin-top: 79px;
}

.mb-79 {
  margin-bottom: 79px;
}

.ml-79 {
  margin-inset-inline-start: 79px;
}

.mr-79 {
  margin-inset-inline-end: 79px;
}

.mt-80 {
  margin-top: 80px;
}

.mb-80 {
  margin-bottom: 80px;
}

.ml-80 {
  margin-inset-inline-start: 80px;
}

.mr-80 {
  margin-inset-inline-end: 80px;
}

.mt-81 {
  margin-top: 81px;
}

.mb-81 {
  margin-bottom: 81px;
}

.ml-81 {
  margin-inset-inline-start: 81px;
}

.mr-81 {
  margin-inset-inline-end: 81px;
}

.mt-82 {
  margin-top: 82px;
}

.mb-82 {
  margin-bottom: 82px;
}

.ml-82 {
  margin-inset-inline-start: 82px;
}

.mr-82 {
  margin-inset-inline-end: 82px;
}

.mt-83 {
  margin-top: 83px;
}

.mb-83 {
  margin-bottom: 83px;
}

.ml-83 {
  margin-inset-inline-start: 83px;
}

.mr-83 {
  margin-inset-inline-end: 83px;
}

.mt-84 {
  margin-top: 84px;
}

.mb-84 {
  margin-bottom: 84px;
}

.ml-84 {
  margin-inset-inline-start: 84px;
}

.mr-84 {
  margin-inset-inline-end: 84px;
}

.mt-85 {
  margin-top: 85px;
}

.mb-85 {
  margin-bottom: 85px;
}

.ml-85 {
  margin-inset-inline-start: 85px;
}

.mr-85 {
  margin-inset-inline-end: 85px;
}

.mt-86 {
  margin-top: 86px;
}

.mb-86 {
  margin-bottom: 86px;
}

.ml-86 {
  margin-inset-inline-start: 86px;
}

.mr-86 {
  margin-inset-inline-end: 86px;
}

.mt-87 {
  margin-top: 87px;
}

.mb-87 {
  margin-bottom: 87px;
}

.ml-87 {
  margin-inset-inline-start: 87px;
}

.mr-87 {
  margin-inset-inline-end: 87px;
}

.mt-88 {
  margin-top: 88px;
}

.mb-88 {
  margin-bottom: 88px;
}

.ml-88 {
  margin-inset-inline-start: 88px;
}

.mr-88 {
  margin-inset-inline-end: 88px;
}

.mt-89 {
  margin-top: 89px;
}

.mb-89 {
  margin-bottom: 89px;
}

.ml-89 {
  margin-inset-inline-start: 89px;
}

.mr-89 {
  margin-inset-inline-end: 89px;
}

.mt-90 {
  margin-top: 90px;
}

.mb-90 {
  margin-bottom: 90px;
}

.ml-90 {
  margin-inset-inline-start: 90px;
}

.mr-90 {
  margin-inset-inline-end: 90px;
}

.mt-91 {
  margin-top: 91px;
}

.mb-91 {
  margin-bottom: 91px;
}

.ml-91 {
  margin-inset-inline-start: 91px;
}

.mr-91 {
  margin-inset-inline-end: 91px;
}

.mt-92 {
  margin-top: 92px;
}

.mb-92 {
  margin-bottom: 92px;
}

.ml-92 {
  margin-inset-inline-start: 92px;
}

.mr-92 {
  margin-inset-inline-end: 92px;
}

.mt-93 {
  margin-top: 93px;
}

.mb-93 {
  margin-bottom: 93px;
}

.ml-93 {
  margin-inset-inline-start: 93px;
}

.mr-93 {
  margin-inset-inline-end: 93px;
}

.mt-94 {
  margin-top: 94px;
}

.mb-94 {
  margin-bottom: 94px;
}

.ml-94 {
  margin-inset-inline-start: 94px;
}

.mr-94 {
  margin-inset-inline-end: 94px;
}

.mt-95 {
  margin-top: 95px;
}

.mb-95 {
  margin-bottom: 95px;
}

.ml-95 {
  margin-inset-inline-start: 95px;
}

.mr-95 {
  margin-inset-inline-end: 95px;
}

.mt-96 {
  margin-top: 96px;
}

.mb-96 {
  margin-bottom: 96px;
}

.ml-96 {
  margin-inset-inline-start: 96px;
}

.mr-96 {
  margin-inset-inline-end: 96px;
}

.mt-97 {
  margin-top: 97px;
}

.mb-97 {
  margin-bottom: 97px;
}

.ml-97 {
  margin-inset-inline-start: 97px;
}

.mr-97 {
  margin-inset-inline-end: 97px;
}

.mt-98 {
  margin-top: 98px;
}

.mb-98 {
  margin-bottom: 98px;
}

.ml-98 {
  margin-inset-inline-start: 98px;
}

.mr-98 {
  margin-inset-inline-end: 98px;
}

.mt-99 {
  margin-top: 99px;
}

.mb-99 {
  margin-bottom: 99px;
}

.ml-99 {
  margin-inset-inline-start: 99px;
}

.mr-99 {
  margin-inset-inline-end: 99px;
}

.mt-100 {
  margin-top: 100px;
}

.mb-100 {
  margin-bottom: 100px;
}

.ml-100 {
  margin-inset-inline-start: 100px;
}

.mr-100 {
  margin-inset-inline-end: 100px;
}

.mt-101 {
  margin-top: 101px;
}

.mb-101 {
  margin-bottom: 101px;
}

.ml-101 {
  margin-inset-inline-start: 101px;
}

.mr-101 {
  margin-inset-inline-end: 101px;
}

.mt-102 {
  margin-top: 102px;
}

.mb-102 {
  margin-bottom: 102px;
}

.ml-102 {
  margin-inset-inline-start: 102px;
}

.mr-102 {
  margin-inset-inline-end: 102px;
}

.mt-103 {
  margin-top: 103px;
}

.mb-103 {
  margin-bottom: 103px;
}

.ml-103 {
  margin-inset-inline-start: 103px;
}

.mr-103 {
  margin-inset-inline-end: 103px;
}

.mt-104 {
  margin-top: 104px;
}

.mb-104 {
  margin-bottom: 104px;
}

.ml-104 {
  margin-inset-inline-start: 104px;
}

.mr-104 {
  margin-inset-inline-end: 104px;
}

.mt-105 {
  margin-top: 105px;
}

.mb-105 {
  margin-bottom: 105px;
}

.ml-105 {
  margin-inset-inline-start: 105px;
}

.mr-105 {
  margin-inset-inline-end: 105px;
}

.mt-106 {
  margin-top: 106px;
}

.mb-106 {
  margin-bottom: 106px;
}

.ml-106 {
  margin-inset-inline-start: 106px;
}

.mr-106 {
  margin-inset-inline-end: 106px;
}

.mt-107 {
  margin-top: 107px;
}

.mb-107 {
  margin-bottom: 107px;
}

.ml-107 {
  margin-inset-inline-start: 107px;
}

.mr-107 {
  margin-inset-inline-end: 107px;
}

.mt-108 {
  margin-top: 108px;
}

.mb-108 {
  margin-bottom: 108px;
}

.ml-108 {
  margin-inset-inline-start: 108px;
}

.mr-108 {
  margin-inset-inline-end: 108px;
}

.mt-109 {
  margin-top: 109px;
}

.mb-109 {
  margin-bottom: 109px;
}

.ml-109 {
  margin-inset-inline-start: 109px;
}

.mr-109 {
  margin-inset-inline-end: 109px;
}

.mt-110 {
  margin-top: 110px;
}

.mb-110 {
  margin-bottom: 110px;
}

.ml-110 {
  margin-inset-inline-start: 110px;
}

.mr-110 {
  margin-inset-inline-end: 110px;
}

.mt-111 {
  margin-top: 111px;
}

.mb-111 {
  margin-bottom: 111px;
}

.ml-111 {
  margin-inset-inline-start: 111px;
}

.mr-111 {
  margin-inset-inline-end: 111px;
}

.mt-112 {
  margin-top: 112px;
}

.mb-112 {
  margin-bottom: 112px;
}

.ml-112 {
  margin-inset-inline-start: 112px;
}

.mr-112 {
  margin-inset-inline-end: 112px;
}

.mt-113 {
  margin-top: 113px;
}

.mb-113 {
  margin-bottom: 113px;
}

.ml-113 {
  margin-inset-inline-start: 113px;
}

.mr-113 {
  margin-inset-inline-end: 113px;
}

.mt-114 {
  margin-top: 114px;
}

.mb-114 {
  margin-bottom: 114px;
}

.ml-114 {
  margin-inset-inline-start: 114px;
}

.mr-114 {
  margin-inset-inline-end: 114px;
}

.mt-115 {
  margin-top: 115px;
}

.mb-115 {
  margin-bottom: 115px;
}

.ml-115 {
  margin-inset-inline-start: 115px;
}

.mr-115 {
  margin-inset-inline-end: 115px;
}

.mt-116 {
  margin-top: 116px;
}

.mb-116 {
  margin-bottom: 116px;
}

.ml-116 {
  margin-inset-inline-start: 116px;
}

.mr-116 {
  margin-inset-inline-end: 116px;
}

.mt-117 {
  margin-top: 117px;
}

.mb-117 {
  margin-bottom: 117px;
}

.ml-117 {
  margin-inset-inline-start: 117px;
}

.mr-117 {
  margin-inset-inline-end: 117px;
}

.mt-118 {
  margin-top: 118px;
}

.mb-118 {
  margin-bottom: 118px;
}

.ml-118 {
  margin-inset-inline-start: 118px;
}

.mr-118 {
  margin-inset-inline-end: 118px;
}

.mt-119 {
  margin-top: 119px;
}

.mb-119 {
  margin-bottom: 119px;
}

.ml-119 {
  margin-inset-inline-start: 119px;
}

.mr-119 {
  margin-inset-inline-end: 119px;
}

.mt-120 {
  margin-top: 120px;
}

.mb-120 {
  margin-bottom: 120px;
}

.ml-120 {
  margin-inset-inline-start: 120px;
}

.mr-120 {
  margin-inset-inline-end: 120px;
}

.mt-121 {
  margin-top: 121px;
}

.mb-121 {
  margin-bottom: 121px;
}

.ml-121 {
  margin-inset-inline-start: 121px;
}

.mr-121 {
  margin-inset-inline-end: 121px;
}

.mt-122 {
  margin-top: 122px;
}

.mb-122 {
  margin-bottom: 122px;
}

.ml-122 {
  margin-inset-inline-start: 122px;
}

.mr-122 {
  margin-inset-inline-end: 122px;
}

.mt-123 {
  margin-top: 123px;
}

.mb-123 {
  margin-bottom: 123px;
}

.ml-123 {
  margin-inset-inline-start: 123px;
}

.mr-123 {
  margin-inset-inline-end: 123px;
}

.mt-124 {
  margin-top: 124px;
}

.mb-124 {
  margin-bottom: 124px;
}

.ml-124 {
  margin-inset-inline-start: 124px;
}

.mr-124 {
  margin-inset-inline-end: 124px;
}

.mt-125 {
  margin-top: 125px;
}

.mb-125 {
  margin-bottom: 125px;
}

.ml-125 {
  margin-inset-inline-start: 125px;
}

.mr-125 {
  margin-inset-inline-end: 125px;
}

.mt-126 {
  margin-top: 126px;
}

.mb-126 {
  margin-bottom: 126px;
}

.ml-126 {
  margin-inset-inline-start: 126px;
}

.mr-126 {
  margin-inset-inline-end: 126px;
}

.mt-127 {
  margin-top: 127px;
}

.mb-127 {
  margin-bottom: 127px;
}

.ml-127 {
  margin-inset-inline-start: 127px;
}

.mr-127 {
  margin-inset-inline-end: 127px;
}

.mt-128 {
  margin-top: 128px;
}

.mb-128 {
  margin-bottom: 128px;
}

.ml-128 {
  margin-inset-inline-start: 128px;
}

.mr-128 {
  margin-inset-inline-end: 128px;
}

.mt-129 {
  margin-top: 129px;
}

.mb-129 {
  margin-bottom: 129px;
}

.ml-129 {
  margin-inset-inline-start: 129px;
}

.mr-129 {
  margin-inset-inline-end: 129px;
}

.mt-130 {
  margin-top: 130px;
}

.mb-130 {
  margin-bottom: 130px;
}

.ml-130 {
  margin-inset-inline-start: 130px;
}

.mr-130 {
  margin-inset-inline-end: 130px;
}

.mt-131 {
  margin-top: 131px;
}

.mb-131 {
  margin-bottom: 131px;
}

.ml-131 {
  margin-inset-inline-start: 131px;
}

.mr-131 {
  margin-inset-inline-end: 131px;
}

.mt-132 {
  margin-top: 132px;
}

.mb-132 {
  margin-bottom: 132px;
}

.ml-132 {
  margin-inset-inline-start: 132px;
}

.mr-132 {
  margin-inset-inline-end: 132px;
}

.mt-133 {
  margin-top: 133px;
}

.mb-133 {
  margin-bottom: 133px;
}

.ml-133 {
  margin-inset-inline-start: 133px;
}

.mr-133 {
  margin-inset-inline-end: 133px;
}

.mt-134 {
  margin-top: 134px;
}

.mb-134 {
  margin-bottom: 134px;
}

.ml-134 {
  margin-inset-inline-start: 134px;
}

.mr-134 {
  margin-inset-inline-end: 134px;
}

.mt-135 {
  margin-top: 135px;
}

.mb-135 {
  margin-bottom: 135px;
}

.ml-135 {
  margin-inset-inline-start: 135px;
}

.mr-135 {
  margin-inset-inline-end: 135px;
}

.mt-136 {
  margin-top: 136px;
}

.mb-136 {
  margin-bottom: 136px;
}

.ml-136 {
  margin-inset-inline-start: 136px;
}

.mr-136 {
  margin-inset-inline-end: 136px;
}

.mt-137 {
  margin-top: 137px;
}

.mb-137 {
  margin-bottom: 137px;
}

.ml-137 {
  margin-inset-inline-start: 137px;
}

.mr-137 {
  margin-inset-inline-end: 137px;
}

.mt-138 {
  margin-top: 138px;
}

.mb-138 {
  margin-bottom: 138px;
}

.ml-138 {
  margin-inset-inline-start: 138px;
}

.mr-138 {
  margin-inset-inline-end: 138px;
}

.mt-139 {
  margin-top: 139px;
}

.mb-139 {
  margin-bottom: 139px;
}

.ml-139 {
  margin-inset-inline-start: 139px;
}

.mr-139 {
  margin-inset-inline-end: 139px;
}

.mt-140 {
  margin-top: 140px;
}

.mb-140 {
  margin-bottom: 140px;
}

.ml-140 {
  margin-inset-inline-start: 140px;
}

.mr-140 {
  margin-inset-inline-end: 140px;
}

.mt-141 {
  margin-top: 141px;
}

.mb-141 {
  margin-bottom: 141px;
}

.ml-141 {
  margin-inset-inline-start: 141px;
}

.mr-141 {
  margin-inset-inline-end: 141px;
}

.mt-142 {
  margin-top: 142px;
}

.mb-142 {
  margin-bottom: 142px;
}

.ml-142 {
  margin-inset-inline-start: 142px;
}

.mr-142 {
  margin-inset-inline-end: 142px;
}

.mt-143 {
  margin-top: 143px;
}

.mb-143 {
  margin-bottom: 143px;
}

.ml-143 {
  margin-inset-inline-start: 143px;
}

.mr-143 {
  margin-inset-inline-end: 143px;
}

.mt-144 {
  margin-top: 144px;
}

.mb-144 {
  margin-bottom: 144px;
}

.ml-144 {
  margin-inset-inline-start: 144px;
}

.mr-144 {
  margin-inset-inline-end: 144px;
}

.mt-145 {
  margin-top: 145px;
}

.mb-145 {
  margin-bottom: 145px;
}

.ml-145 {
  margin-inset-inline-start: 145px;
}

.mr-145 {
  margin-inset-inline-end: 145px;
}

.mt-146 {
  margin-top: 146px;
}

.mb-146 {
  margin-bottom: 146px;
}

.ml-146 {
  margin-inset-inline-start: 146px;
}

.mr-146 {
  margin-inset-inline-end: 146px;
}

.mt-147 {
  margin-top: 147px;
}

.mb-147 {
  margin-bottom: 147px;
}

.ml-147 {
  margin-inset-inline-start: 147px;
}

.mr-147 {
  margin-inset-inline-end: 147px;
}

.mt-148 {
  margin-top: 148px;
}

.mb-148 {
  margin-bottom: 148px;
}

.ml-148 {
  margin-inset-inline-start: 148px;
}

.mr-148 {
  margin-inset-inline-end: 148px;
}

.mt-149 {
  margin-top: 149px;
}

.mb-149 {
  margin-bottom: 149px;
}

.ml-149 {
  margin-inset-inline-start: 149px;
}

.mr-149 {
  margin-inset-inline-end: 149px;
}

.mt-150 {
  margin-top: 150px;
}

.mb-150 {
  margin-bottom: 150px;
}

.ml-150 {
  margin-inset-inline-start: 150px;
}

.mr-150 {
  margin-inset-inline-end: 150px;
}

.mt-151 {
  margin-top: 151px;
}

.mb-151 {
  margin-bottom: 151px;
}

.ml-151 {
  margin-inset-inline-start: 151px;
}

.mr-151 {
  margin-inset-inline-end: 151px;
}

.mt-152 {
  margin-top: 152px;
}

.mb-152 {
  margin-bottom: 152px;
}

.ml-152 {
  margin-inset-inline-start: 152px;
}

.mr-152 {
  margin-inset-inline-end: 152px;
}

.mt-153 {
  margin-top: 153px;
}

.mb-153 {
  margin-bottom: 153px;
}

.ml-153 {
  margin-inset-inline-start: 153px;
}

.mr-153 {
  margin-inset-inline-end: 153px;
}

.mt-154 {
  margin-top: 154px;
}

.mb-154 {
  margin-bottom: 154px;
}

.ml-154 {
  margin-inset-inline-start: 154px;
}

.mr-154 {
  margin-inset-inline-end: 154px;
}

.mt-155 {
  margin-top: 155px;
}

.mb-155 {
  margin-bottom: 155px;
}

.ml-155 {
  margin-inset-inline-start: 155px;
}

.mr-155 {
  margin-inset-inline-end: 155px;
}

.mt-156 {
  margin-top: 156px;
}

.mb-156 {
  margin-bottom: 156px;
}

.ml-156 {
  margin-inset-inline-start: 156px;
}

.mr-156 {
  margin-inset-inline-end: 156px;
}

.mt-157 {
  margin-top: 157px;
}

.mb-157 {
  margin-bottom: 157px;
}

.ml-157 {
  margin-inset-inline-start: 157px;
}

.mr-157 {
  margin-inset-inline-end: 157px;
}

.mt-158 {
  margin-top: 158px;
}

.mb-158 {
  margin-bottom: 158px;
}

.ml-158 {
  margin-inset-inline-start: 158px;
}

.mr-158 {
  margin-inset-inline-end: 158px;
}

.mt-159 {
  margin-top: 159px;
}

.mb-159 {
  margin-bottom: 159px;
}

.ml-159 {
  margin-inset-inline-start: 159px;
}

.mr-159 {
  margin-inset-inline-end: 159px;
}

.mt-160 {
  margin-top: 160px;
}

.mb-160 {
  margin-bottom: 160px;
}

.ml-160 {
  margin-inset-inline-start: 160px;
}

.mr-160 {
  margin-inset-inline-end: 160px;
}

.mt-161 {
  margin-top: 161px;
}

.mb-161 {
  margin-bottom: 161px;
}

.ml-161 {
  margin-inset-inline-start: 161px;
}

.mr-161 {
  margin-inset-inline-end: 161px;
}

.mt-162 {
  margin-top: 162px;
}

.mb-162 {
  margin-bottom: 162px;
}

.ml-162 {
  margin-inset-inline-start: 162px;
}

.mr-162 {
  margin-inset-inline-end: 162px;
}

.mt-163 {
  margin-top: 163px;
}

.mb-163 {
  margin-bottom: 163px;
}

.ml-163 {
  margin-inset-inline-start: 163px;
}

.mr-163 {
  margin-inset-inline-end: 163px;
}

.mt-164 {
  margin-top: 164px;
}

.mb-164 {
  margin-bottom: 164px;
}

.ml-164 {
  margin-inset-inline-start: 164px;
}

.mr-164 {
  margin-inset-inline-end: 164px;
}

.mt-165 {
  margin-top: 165px;
}

.mb-165 {
  margin-bottom: 165px;
}

.ml-165 {
  margin-inset-inline-start: 165px;
}

.mr-165 {
  margin-inset-inline-end: 165px;
}

.mt-166 {
  margin-top: 166px;
}

.mb-166 {
  margin-bottom: 166px;
}

.ml-166 {
  margin-inset-inline-start: 166px;
}

.mr-166 {
  margin-inset-inline-end: 166px;
}

.mt-167 {
  margin-top: 167px;
}

.mb-167 {
  margin-bottom: 167px;
}

.ml-167 {
  margin-inset-inline-start: 167px;
}

.mr-167 {
  margin-inset-inline-end: 167px;
}

.mt-168 {
  margin-top: 168px;
}

.mb-168 {
  margin-bottom: 168px;
}

.ml-168 {
  margin-inset-inline-start: 168px;
}

.mr-168 {
  margin-inset-inline-end: 168px;
}

.mt-169 {
  margin-top: 169px;
}

.mb-169 {
  margin-bottom: 169px;
}

.ml-169 {
  margin-inset-inline-start: 169px;
}

.mr-169 {
  margin-inset-inline-end: 169px;
}

.mt-170 {
  margin-top: 170px;
}

.mb-170 {
  margin-bottom: 170px;
}

.ml-170 {
  margin-inset-inline-start: 170px;
}

.mr-170 {
  margin-inset-inline-end: 170px;
}

.mt-171 {
  margin-top: 171px;
}

.mb-171 {
  margin-bottom: 171px;
}

.ml-171 {
  margin-inset-inline-start: 171px;
}

.mr-171 {
  margin-inset-inline-end: 171px;
}

.mt-172 {
  margin-top: 172px;
}

.mb-172 {
  margin-bottom: 172px;
}

.ml-172 {
  margin-inset-inline-start: 172px;
}

.mr-172 {
  margin-inset-inline-end: 172px;
}

.mt-173 {
  margin-top: 173px;
}

.mb-173 {
  margin-bottom: 173px;
}

.ml-173 {
  margin-inset-inline-start: 173px;
}

.mr-173 {
  margin-inset-inline-end: 173px;
}

.mt-174 {
  margin-top: 174px;
}

.mb-174 {
  margin-bottom: 174px;
}

.ml-174 {
  margin-inset-inline-start: 174px;
}

.mr-174 {
  margin-inset-inline-end: 174px;
}

.mt-175 {
  margin-top: 175px;
}

.mb-175 {
  margin-bottom: 175px;
}

.ml-175 {
  margin-inset-inline-start: 175px;
}

.mr-175 {
  margin-inset-inline-end: 175px;
}

.mt-176 {
  margin-top: 176px;
}

.mb-176 {
  margin-bottom: 176px;
}

.ml-176 {
  margin-inset-inline-start: 176px;
}

.mr-176 {
  margin-inset-inline-end: 176px;
}

.mt-177 {
  margin-top: 177px;
}

.mb-177 {
  margin-bottom: 177px;
}

.ml-177 {
  margin-inset-inline-start: 177px;
}

.mr-177 {
  margin-inset-inline-end: 177px;
}

.mt-178 {
  margin-top: 178px;
}

.mb-178 {
  margin-bottom: 178px;
}

.ml-178 {
  margin-inset-inline-start: 178px;
}

.mr-178 {
  margin-inset-inline-end: 178px;
}

.mt-179 {
  margin-top: 179px;
}

.mb-179 {
  margin-bottom: 179px;
}

.ml-179 {
  margin-inset-inline-start: 179px;
}

.mr-179 {
  margin-inset-inline-end: 179px;
}

.mt-180 {
  margin-top: 180px;
}

.mb-180 {
  margin-bottom: 180px;
}

.ml-180 {
  margin-inset-inline-start: 180px;
}

.mr-180 {
  margin-inset-inline-end: 180px;
}

.mt-181 {
  margin-top: 181px;
}

.mb-181 {
  margin-bottom: 181px;
}

.ml-181 {
  margin-inset-inline-start: 181px;
}

.mr-181 {
  margin-inset-inline-end: 181px;
}

.mt-182 {
  margin-top: 182px;
}

.mb-182 {
  margin-bottom: 182px;
}

.ml-182 {
  margin-inset-inline-start: 182px;
}

.mr-182 {
  margin-inset-inline-end: 182px;
}

.mt-183 {
  margin-top: 183px;
}

.mb-183 {
  margin-bottom: 183px;
}

.ml-183 {
  margin-inset-inline-start: 183px;
}

.mr-183 {
  margin-inset-inline-end: 183px;
}

.mt-184 {
  margin-top: 184px;
}

.mb-184 {
  margin-bottom: 184px;
}

.ml-184 {
  margin-inset-inline-start: 184px;
}

.mr-184 {
  margin-inset-inline-end: 184px;
}

.mt-185 {
  margin-top: 185px;
}

.mb-185 {
  margin-bottom: 185px;
}

.ml-185 {
  margin-inset-inline-start: 185px;
}

.mr-185 {
  margin-inset-inline-end: 185px;
}

.mt-186 {
  margin-top: 186px;
}

.mb-186 {
  margin-bottom: 186px;
}

.ml-186 {
  margin-inset-inline-start: 186px;
}

.mr-186 {
  margin-inset-inline-end: 186px;
}

.mt-187 {
  margin-top: 187px;
}

.mb-187 {
  margin-bottom: 187px;
}

.ml-187 {
  margin-inset-inline-start: 187px;
}

.mr-187 {
  margin-inset-inline-end: 187px;
}

.mt-188 {
  margin-top: 188px;
}

.mb-188 {
  margin-bottom: 188px;
}

.ml-188 {
  margin-inset-inline-start: 188px;
}

.mr-188 {
  margin-inset-inline-end: 188px;
}

.mt-189 {
  margin-top: 189px;
}

.mb-189 {
  margin-bottom: 189px;
}

.ml-189 {
  margin-inset-inline-start: 189px;
}

.mr-189 {
  margin-inset-inline-end: 189px;
}

.mt-190 {
  margin-top: 190px;
}

.mb-190 {
  margin-bottom: 190px;
}

.ml-190 {
  margin-inset-inline-start: 190px;
}

.mr-190 {
  margin-inset-inline-end: 190px;
}

.mt-191 {
  margin-top: 191px;
}

.mb-191 {
  margin-bottom: 191px;
}

.ml-191 {
  margin-inset-inline-start: 191px;
}

.mr-191 {
  margin-inset-inline-end: 191px;
}

.mt-192 {
  margin-top: 192px;
}

.mb-192 {
  margin-bottom: 192px;
}

.ml-192 {
  margin-inset-inline-start: 192px;
}

.mr-192 {
  margin-inset-inline-end: 192px;
}

.mt-193 {
  margin-top: 193px;
}

.mb-193 {
  margin-bottom: 193px;
}

.ml-193 {
  margin-inset-inline-start: 193px;
}

.mr-193 {
  margin-inset-inline-end: 193px;
}

.mt-194 {
  margin-top: 194px;
}

.mb-194 {
  margin-bottom: 194px;
}

.ml-194 {
  margin-inset-inline-start: 194px;
}

.mr-194 {
  margin-inset-inline-end: 194px;
}

.mt-195 {
  margin-top: 195px;
}

.mb-195 {
  margin-bottom: 195px;
}

.ml-195 {
  margin-inset-inline-start: 195px;
}

.mr-195 {
  margin-inset-inline-end: 195px;
}

.mt-196 {
  margin-top: 196px;
}

.mb-196 {
  margin-bottom: 196px;
}

.ml-196 {
  margin-inset-inline-start: 196px;
}

.mr-196 {
  margin-inset-inline-end: 196px;
}

.mt-197 {
  margin-top: 197px;
}

.mb-197 {
  margin-bottom: 197px;
}

.ml-197 {
  margin-inset-inline-start: 197px;
}

.mr-197 {
  margin-inset-inline-end: 197px;
}

.mt-198 {
  margin-top: 198px;
}

.mb-198 {
  margin-bottom: 198px;
}

.ml-198 {
  margin-inset-inline-start: 198px;
}

.mr-198 {
  margin-inset-inline-end: 198px;
}

.mt-199 {
  margin-top: 199px;
}

.mb-199 {
  margin-bottom: 199px;
}

.ml-199 {
  margin-inset-inline-start: 199px;
}

.mr-199 {
  margin-inset-inline-end: 199px;
}

.mt-200 {
  margin-top: 200px;
}

.mb-200 {
  margin-bottom: 200px;
}

.ml-200 {
  margin-inset-inline-start: 200px;
}

.mr-200 {
  margin-inset-inline-end: 200px;
}

.pt-1 {
  padding-top: 1px;
}

.pb-1 {
  padding-bottom: 1px;
}

.pl-1 {
  padding-inset-inline-start: 1px;
}

.pr-1 {
  padding-inset-inline-end: 1px;
}

.pt-2 {
  padding-top: 2px;
}

.pb-2 {
  padding-bottom: 2px;
}

.pl-2 {
  padding-inset-inline-start: 2px;
}

.pr-2 {
  padding-inset-inline-end: 2px;
}

.pt-3 {
  padding-top: 3px;
}

.pb-3 {
  padding-bottom: 3px;
}

.pl-3 {
  padding-inset-inline-start: 3px;
}

.pr-3 {
  padding-inset-inline-end: 3px;
}

.pt-4 {
  padding-top: 4px;
}

.pb-4 {
  padding-bottom: 4px;
}

.pl-4 {
  padding-inset-inline-start: 4px;
}

.pr-4 {
  padding-inset-inline-end: 4px;
}

.pt-5 {
  padding-top: 5px;
}

.pb-5 {
  padding-bottom: 5px;
}

.pl-5 {
  padding-inset-inline-start: 5px;
}

.pr-5 {
  padding-inset-inline-end: 5px;
}

.pt-6 {
  padding-top: 6px;
}

.pb-6 {
  padding-bottom: 6px;
}

.pl-6 {
  padding-inset-inline-start: 6px;
}

.pr-6 {
  padding-inset-inline-end: 6px;
}

.pt-7 {
  padding-top: 7px;
}

.pb-7 {
  padding-bottom: 7px;
}

.pl-7 {
  padding-inset-inline-start: 7px;
}

.pr-7 {
  padding-inset-inline-end: 7px;
}

.pt-8 {
  padding-top: 8px;
}

.pb-8 {
  padding-bottom: 8px;
}

.pl-8 {
  padding-inset-inline-start: 8px;
}

.pr-8 {
  padding-inset-inline-end: 8px;
}

.pt-9 {
  padding-top: 9px;
}

.pb-9 {
  padding-bottom: 9px;
}

.pl-9 {
  padding-inset-inline-start: 9px;
}

.pr-9 {
  padding-inset-inline-end: 9px;
}

.pt-10 {
  padding-top: 10px;
}

.pb-10 {
  padding-bottom: 10px;
}

.pl-10 {
  padding-inset-inline-start: 10px;
}

.pr-10 {
  padding-inset-inline-end: 10px;
}

.pt-11 {
  padding-top: 11px;
}

.pb-11 {
  padding-bottom: 11px;
}

.pl-11 {
  padding-inset-inline-start: 11px;
}

.pr-11 {
  padding-inset-inline-end: 11px;
}

.pt-12 {
  padding-top: 12px;
}

.pb-12 {
  padding-bottom: 12px;
}

.pl-12 {
  padding-inset-inline-start: 12px;
}

.pr-12 {
  padding-inset-inline-end: 12px;
}

.pt-13 {
  padding-top: 13px;
}

.pb-13 {
  padding-bottom: 13px;
}

.pl-13 {
  padding-inset-inline-start: 13px;
}

.pr-13 {
  padding-inset-inline-end: 13px;
}

.pt-14 {
  padding-top: 14px;
}

.pb-14 {
  padding-bottom: 14px;
}

.pl-14 {
  padding-inset-inline-start: 14px;
}

.pr-14 {
  padding-inset-inline-end: 14px;
}

.pt-15 {
  padding-top: 15px;
}

.pb-15 {
  padding-bottom: 15px;
}

.pl-15 {
  padding-inset-inline-start: 15px;
}

.pr-15 {
  padding-inset-inline-end: 15px;
}

.pt-16 {
  padding-top: 16px;
}

.pb-16 {
  padding-bottom: 16px;
}

.pl-16 {
  padding-inset-inline-start: 16px;
}

.pr-16 {
  padding-inset-inline-end: 16px;
}

.pt-17 {
  padding-top: 17px;
}

.pb-17 {
  padding-bottom: 17px;
}

.pl-17 {
  padding-inset-inline-start: 17px;
}

.pr-17 {
  padding-inset-inline-end: 17px;
}

.pt-18 {
  padding-top: 18px;
}

.pb-18 {
  padding-bottom: 18px;
}

.pl-18 {
  padding-inset-inline-start: 18px;
}

.pr-18 {
  padding-inset-inline-end: 18px;
}

.pt-19 {
  padding-top: 19px;
}

.pb-19 {
  padding-bottom: 19px;
}

.pl-19 {
  padding-inset-inline-start: 19px;
}

.pr-19 {
  padding-inset-inline-end: 19px;
}

.pt-20 {
  padding-top: 20px;
}

.pb-20 {
  padding-bottom: 20px;
}

.pl-20 {
  padding-inset-inline-start: 20px;
}

.pr-20 {
  padding-inset-inline-end: 20px;
}

.pt-21 {
  padding-top: 21px;
}

.pb-21 {
  padding-bottom: 21px;
}

.pl-21 {
  padding-inset-inline-start: 21px;
}

.pr-21 {
  padding-inset-inline-end: 21px;
}

.pt-22 {
  padding-top: 22px;
}

.pb-22 {
  padding-bottom: 22px;
}

.pl-22 {
  padding-inset-inline-start: 22px;
}

.pr-22 {
  padding-inset-inline-end: 22px;
}

.pt-23 {
  padding-top: 23px;
}

.pb-23 {
  padding-bottom: 23px;
}

.pl-23 {
  padding-inset-inline-start: 23px;
}

.pr-23 {
  padding-inset-inline-end: 23px;
}

.pt-24 {
  padding-top: 24px;
}

.pb-24 {
  padding-bottom: 24px;
}

.pl-24 {
  padding-inset-inline-start: 24px;
}

.pr-24 {
  padding-inset-inline-end: 24px;
}

.pt-25 {
  padding-top: 25px;
}

.pb-25 {
  padding-bottom: 25px;
}

.pl-25 {
  padding-inset-inline-start: 25px;
}

.pr-25 {
  padding-inset-inline-end: 25px;
}

.pt-26 {
  padding-top: 26px;
}

.pb-26 {
  padding-bottom: 26px;
}

.pl-26 {
  padding-inset-inline-start: 26px;
}

.pr-26 {
  padding-inset-inline-end: 26px;
}

.pt-27 {
  padding-top: 27px;
}

.pb-27 {
  padding-bottom: 27px;
}

.pl-27 {
  padding-inset-inline-start: 27px;
}

.pr-27 {
  padding-inset-inline-end: 27px;
}

.pt-28 {
  padding-top: 28px;
}

.pb-28 {
  padding-bottom: 28px;
}

.pl-28 {
  padding-inset-inline-start: 28px;
}

.pr-28 {
  padding-inset-inline-end: 28px;
}

.pt-29 {
  padding-top: 29px;
}

.pb-29 {
  padding-bottom: 29px;
}

.pl-29 {
  padding-inset-inline-start: 29px;
}

.pr-29 {
  padding-inset-inline-end: 29px;
}

.pt-30 {
  padding-top: 30px;
}

.pb-30 {
  padding-bottom: 30px;
}

.pl-30 {
  padding-inset-inline-start: 30px;
}

.pr-30 {
  padding-inset-inline-end: 30px;
}

.pt-31 {
  padding-top: 31px;
}

.pb-31 {
  padding-bottom: 31px;
}

.pl-31 {
  padding-inset-inline-start: 31px;
}

.pr-31 {
  padding-inset-inline-end: 31px;
}

.pt-32 {
  padding-top: 32px;
}

.pb-32 {
  padding-bottom: 32px;
}

.pl-32 {
  padding-inset-inline-start: 32px;
}

.pr-32 {
  padding-inset-inline-end: 32px;
}

.pt-33 {
  padding-top: 33px;
}

.pb-33 {
  padding-bottom: 33px;
}

.pl-33 {
  padding-inset-inline-start: 33px;
}

.pr-33 {
  padding-inset-inline-end: 33px;
}

.pt-34 {
  padding-top: 34px;
}

.pb-34 {
  padding-bottom: 34px;
}

.pl-34 {
  padding-inset-inline-start: 34px;
}

.pr-34 {
  padding-inset-inline-end: 34px;
}

.pt-35 {
  padding-top: 35px;
}

.pb-35 {
  padding-bottom: 35px;
}

.pl-35 {
  padding-inset-inline-start: 35px;
}

.pr-35 {
  padding-inset-inline-end: 35px;
}

.pt-36 {
  padding-top: 36px;
}

.pb-36 {
  padding-bottom: 36px;
}

.pl-36 {
  padding-inset-inline-start: 36px;
}

.pr-36 {
  padding-inset-inline-end: 36px;
}

.pt-37 {
  padding-top: 37px;
}

.pb-37 {
  padding-bottom: 37px;
}

.pl-37 {
  padding-inset-inline-start: 37px;
}

.pr-37 {
  padding-inset-inline-end: 37px;
}

.pt-38 {
  padding-top: 38px;
}

.pb-38 {
  padding-bottom: 38px;
}

.pl-38 {
  padding-inset-inline-start: 38px;
}

.pr-38 {
  padding-inset-inline-end: 38px;
}

.pt-39 {
  padding-top: 39px;
}

.pb-39 {
  padding-bottom: 39px;
}

.pl-39 {
  padding-inset-inline-start: 39px;
}

.pr-39 {
  padding-inset-inline-end: 39px;
}

.pt-40 {
  padding-top: 40px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pl-40 {
  padding-inset-inline-start: 40px;
}

.pr-40 {
  padding-inset-inline-end: 40px;
}

.pt-41 {
  padding-top: 41px;
}

.pb-41 {
  padding-bottom: 41px;
}

.pl-41 {
  padding-inset-inline-start: 41px;
}

.pr-41 {
  padding-inset-inline-end: 41px;
}

.pt-42 {
  padding-top: 42px;
}

.pb-42 {
  padding-bottom: 42px;
}

.pl-42 {
  padding-inset-inline-start: 42px;
}

.pr-42 {
  padding-inset-inline-end: 42px;
}

.pt-43 {
  padding-top: 43px;
}

.pb-43 {
  padding-bottom: 43px;
}

.pl-43 {
  padding-inset-inline-start: 43px;
}

.pr-43 {
  padding-inset-inline-end: 43px;
}

.pt-44 {
  padding-top: 44px;
}

.pb-44 {
  padding-bottom: 44px;
}

.pl-44 {
  padding-inset-inline-start: 44px;
}

.pr-44 {
  padding-inset-inline-end: 44px;
}

.pt-45 {
  padding-top: 45px;
}

.pb-45 {
  padding-bottom: 45px;
}

.pl-45 {
  padding-inset-inline-start: 45px;
}

.pr-45 {
  padding-inset-inline-end: 45px;
}

.pt-46 {
  padding-top: 46px;
}

.pb-46 {
  padding-bottom: 46px;
}

.pl-46 {
  padding-inset-inline-start: 46px;
}

.pr-46 {
  padding-inset-inline-end: 46px;
}

.pt-47 {
  padding-top: 47px;
}

.pb-47 {
  padding-bottom: 47px;
}

.pl-47 {
  padding-inset-inline-start: 47px;
}

.pr-47 {
  padding-inset-inline-end: 47px;
}

.pt-48 {
  padding-top: 48px;
}

.pb-48 {
  padding-bottom: 48px;
}

.pl-48 {
  padding-inset-inline-start: 48px;
}

.pr-48 {
  padding-inset-inline-end: 48px;
}

.pt-49 {
  padding-top: 49px;
}

.pb-49 {
  padding-bottom: 49px;
}

.pl-49 {
  padding-inset-inline-start: 49px;
}

.pr-49 {
  padding-inset-inline-end: 49px;
}

.pt-50 {
  padding-top: 50px;
}

.pb-50 {
  padding-bottom: 50px;
}

.pl-50 {
  padding-inset-inline-start: 50px;
}

.pr-50 {
  padding-inset-inline-end: 50px;
}

.pt-51 {
  padding-top: 51px;
}

.pb-51 {
  padding-bottom: 51px;
}

.pl-51 {
  padding-inset-inline-start: 51px;
}

.pr-51 {
  padding-inset-inline-end: 51px;
}

.pt-52 {
  padding-top: 52px;
}

.pb-52 {
  padding-bottom: 52px;
}

.pl-52 {
  padding-inset-inline-start: 52px;
}

.pr-52 {
  padding-inset-inline-end: 52px;
}

.pt-53 {
  padding-top: 53px;
}

.pb-53 {
  padding-bottom: 53px;
}

.pl-53 {
  padding-inset-inline-start: 53px;
}

.pr-53 {
  padding-inset-inline-end: 53px;
}

.pt-54 {
  padding-top: 54px;
}

.pb-54 {
  padding-bottom: 54px;
}

.pl-54 {
  padding-inset-inline-start: 54px;
}

.pr-54 {
  padding-inset-inline-end: 54px;
}

.pt-55 {
  padding-top: 55px;
}

.pb-55 {
  padding-bottom: 55px;
}

.pl-55 {
  padding-inset-inline-start: 55px;
}

.pr-55 {
  padding-inset-inline-end: 55px;
}

.pt-56 {
  padding-top: 56px;
}

.pb-56 {
  padding-bottom: 56px;
}

.pl-56 {
  padding-inset-inline-start: 56px;
}

.pr-56 {
  padding-inset-inline-end: 56px;
}

.pt-57 {
  padding-top: 57px;
}

.pb-57 {
  padding-bottom: 57px;
}

.pl-57 {
  padding-inset-inline-start: 57px;
}

.pr-57 {
  padding-inset-inline-end: 57px;
}

.pt-58 {
  padding-top: 58px;
}

.pb-58 {
  padding-bottom: 58px;
}

.pl-58 {
  padding-inset-inline-start: 58px;
}

.pr-58 {
  padding-inset-inline-end: 58px;
}

.pt-59 {
  padding-top: 59px;
}

.pb-59 {
  padding-bottom: 59px;
}

.pl-59 {
  padding-inset-inline-start: 59px;
}

.pr-59 {
  padding-inset-inline-end: 59px;
}

.pt-60 {
  padding-top: 60px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pl-60 {
  padding-inset-inline-start: 60px;
}

.pr-60 {
  padding-inset-inline-end: 60px;
}

.pt-61 {
  padding-top: 61px;
}

.pb-61 {
  padding-bottom: 61px;
}

.pl-61 {
  padding-inset-inline-start: 61px;
}

.pr-61 {
  padding-inset-inline-end: 61px;
}

.pt-62 {
  padding-top: 62px;
}

.pb-62 {
  padding-bottom: 62px;
}

.pl-62 {
  padding-inset-inline-start: 62px;
}

.pr-62 {
  padding-inset-inline-end: 62px;
}

.pt-63 {
  padding-top: 63px;
}

.pb-63 {
  padding-bottom: 63px;
}

.pl-63 {
  padding-inset-inline-start: 63px;
}

.pr-63 {
  padding-inset-inline-end: 63px;
}

.pt-64 {
  padding-top: 64px;
}

.pb-64 {
  padding-bottom: 64px;
}

.pl-64 {
  padding-inset-inline-start: 64px;
}

.pr-64 {
  padding-inset-inline-end: 64px;
}

.pt-65 {
  padding-top: 65px;
}

.pb-65 {
  padding-bottom: 65px;
}

.pl-65 {
  padding-inset-inline-start: 65px;
}

.pr-65 {
  padding-inset-inline-end: 65px;
}

.pt-66 {
  padding-top: 66px;
}

.pb-66 {
  padding-bottom: 66px;
}

.pl-66 {
  padding-inset-inline-start: 66px;
}

.pr-66 {
  padding-inset-inline-end: 66px;
}

.pt-67 {
  padding-top: 67px;
}

.pb-67 {
  padding-bottom: 67px;
}

.pl-67 {
  padding-inset-inline-start: 67px;
}

.pr-67 {
  padding-inset-inline-end: 67px;
}

.pt-68 {
  padding-top: 68px;
}

.pb-68 {
  padding-bottom: 68px;
}

.pl-68 {
  padding-inset-inline-start: 68px;
}

.pr-68 {
  padding-inset-inline-end: 68px;
}

.pt-69 {
  padding-top: 69px;
}

.pb-69 {
  padding-bottom: 69px;
}

.pl-69 {
  padding-inset-inline-start: 69px;
}

.pr-69 {
  padding-inset-inline-end: 69px;
}

.pt-70 {
  padding-top: 70px;
}

.pb-70 {
  padding-bottom: 70px;
}

.pl-70 {
  padding-inset-inline-start: 70px;
}

.pr-70 {
  padding-inset-inline-end: 70px;
}

.pt-71 {
  padding-top: 71px;
}

.pb-71 {
  padding-bottom: 71px;
}

.pl-71 {
  padding-inset-inline-start: 71px;
}

.pr-71 {
  padding-inset-inline-end: 71px;
}

.pt-72 {
  padding-top: 72px;
}

.pb-72 {
  padding-bottom: 72px;
}

.pl-72 {
  padding-inset-inline-start: 72px;
}

.pr-72 {
  padding-inset-inline-end: 72px;
}

.pt-73 {
  padding-top: 73px;
}

.pb-73 {
  padding-bottom: 73px;
}

.pl-73 {
  padding-inset-inline-start: 73px;
}

.pr-73 {
  padding-inset-inline-end: 73px;
}

.pt-74 {
  padding-top: 74px;
}

.pb-74 {
  padding-bottom: 74px;
}

.pl-74 {
  padding-inset-inline-start: 74px;
}

.pr-74 {
  padding-inset-inline-end: 74px;
}

.pt-75 {
  padding-top: 75px;
}

.pb-75 {
  padding-bottom: 75px;
}

.pl-75 {
  padding-inset-inline-start: 75px;
}

.pr-75 {
  padding-inset-inline-end: 75px;
}

.pt-76 {
  padding-top: 76px;
}

.pb-76 {
  padding-bottom: 76px;
}

.pl-76 {
  padding-inset-inline-start: 76px;
}

.pr-76 {
  padding-inset-inline-end: 76px;
}

.pt-77 {
  padding-top: 77px;
}

.pb-77 {
  padding-bottom: 77px;
}

.pl-77 {
  padding-inset-inline-start: 77px;
}

.pr-77 {
  padding-inset-inline-end: 77px;
}

.pt-78 {
  padding-top: 78px;
}

.pb-78 {
  padding-bottom: 78px;
}

.pl-78 {
  padding-inset-inline-start: 78px;
}

.pr-78 {
  padding-inset-inline-end: 78px;
}

.pt-79 {
  padding-top: 79px;
}

.pb-79 {
  padding-bottom: 79px;
}

.pl-79 {
  padding-inset-inline-start: 79px;
}

.pr-79 {
  padding-inset-inline-end: 79px;
}

.pt-80 {
  padding-top: 80px;
}

.pb-80 {
  padding-bottom: 80px;
}

.pl-80 {
  padding-inset-inline-start: 80px;
}

.pr-80 {
  padding-inset-inline-end: 80px;
}

.pt-81 {
  padding-top: 81px;
}

.pb-81 {
  padding-bottom: 81px;
}

.pl-81 {
  padding-inset-inline-start: 81px;
}

.pr-81 {
  padding-inset-inline-end: 81px;
}

.pt-82 {
  padding-top: 82px;
}

.pb-82 {
  padding-bottom: 82px;
}

.pl-82 {
  padding-inset-inline-start: 82px;
}

.pr-82 {
  padding-inset-inline-end: 82px;
}

.pt-83 {
  padding-top: 83px;
}

.pb-83 {
  padding-bottom: 83px;
}

.pl-83 {
  padding-inset-inline-start: 83px;
}

.pr-83 {
  padding-inset-inline-end: 83px;
}

.pt-84 {
  padding-top: 84px;
}

.pb-84 {
  padding-bottom: 84px;
}

.pl-84 {
  padding-inset-inline-start: 84px;
}

.pr-84 {
  padding-inset-inline-end: 84px;
}

.pt-85 {
  padding-top: 85px;
}

.pb-85 {
  padding-bottom: 85px;
}

.pl-85 {
  padding-inset-inline-start: 85px;
}

.pr-85 {
  padding-inset-inline-end: 85px;
}

.pt-86 {
  padding-top: 86px;
}

.pb-86 {
  padding-bottom: 86px;
}

.pl-86 {
  padding-inset-inline-start: 86px;
}

.pr-86 {
  padding-inset-inline-end: 86px;
}

.pt-87 {
  padding-top: 87px;
}

.pb-87 {
  padding-bottom: 87px;
}

.pl-87 {
  padding-inset-inline-start: 87px;
}

.pr-87 {
  padding-inset-inline-end: 87px;
}

.pt-88 {
  padding-top: 88px;
}

.pb-88 {
  padding-bottom: 88px;
}

.pl-88 {
  padding-inset-inline-start: 88px;
}

.pr-88 {
  padding-inset-inline-end: 88px;
}

.pt-89 {
  padding-top: 89px;
}

.pb-89 {
  padding-bottom: 89px;
}

.pl-89 {
  padding-inset-inline-start: 89px;
}

.pr-89 {
  padding-inset-inline-end: 89px;
}

.pt-90 {
  padding-top: 90px;
}

.pb-90 {
  padding-bottom: 90px;
}

.pl-90 {
  padding-inset-inline-start: 90px;
}

.pr-90 {
  padding-inset-inline-end: 90px;
}

.pt-91 {
  padding-top: 91px;
}

.pb-91 {
  padding-bottom: 91px;
}

.pl-91 {
  padding-inset-inline-start: 91px;
}

.pr-91 {
  padding-inset-inline-end: 91px;
}

.pt-92 {
  padding-top: 92px;
}

.pb-92 {
  padding-bottom: 92px;
}

.pl-92 {
  padding-inset-inline-start: 92px;
}

.pr-92 {
  padding-inset-inline-end: 92px;
}

.pt-93 {
  padding-top: 93px;
}

.pb-93 {
  padding-bottom: 93px;
}

.pl-93 {
  padding-inset-inline-start: 93px;
}

.pr-93 {
  padding-inset-inline-end: 93px;
}

.pt-94 {
  padding-top: 94px;
}

.pb-94 {
  padding-bottom: 94px;
}

.pl-94 {
  padding-inset-inline-start: 94px;
}

.pr-94 {
  padding-inset-inline-end: 94px;
}

.pt-95 {
  padding-top: 95px;
}

.pb-95 {
  padding-bottom: 95px;
}

.pl-95 {
  padding-inset-inline-start: 95px;
}

.pr-95 {
  padding-inset-inline-end: 95px;
}

.pt-96 {
  padding-top: 96px;
}

.pb-96 {
  padding-bottom: 96px;
}

.pl-96 {
  padding-inset-inline-start: 96px;
}

.pr-96 {
  padding-inset-inline-end: 96px;
}

.pt-97 {
  padding-top: 97px;
}

.pb-97 {
  padding-bottom: 97px;
}

.pl-97 {
  padding-inset-inline-start: 97px;
}

.pr-97 {
  padding-inset-inline-end: 97px;
}

.pt-98 {
  padding-top: 98px;
}

.pb-98 {
  padding-bottom: 98px;
}

.pl-98 {
  padding-inset-inline-start: 98px;
}

.pr-98 {
  padding-inset-inline-end: 98px;
}

.pt-99 {
  padding-top: 99px;
}

.pb-99 {
  padding-bottom: 99px;
}

.pl-99 {
  padding-inset-inline-start: 99px;
}

.pr-99 {
  padding-inset-inline-end: 99px;
}

.pt-100 {
  padding-top: 100px;
}

.pb-100 {
  padding-bottom: 100px;
}

.pl-100 {
  padding-inset-inline-start: 100px;
}

.pr-100 {
  padding-inset-inline-end: 100px;
}

.pt-101 {
  padding-top: 101px;
}

.pb-101 {
  padding-bottom: 101px;
}

.pl-101 {
  padding-inset-inline-start: 101px;
}

.pr-101 {
  padding-inset-inline-end: 101px;
}

.pt-102 {
  padding-top: 102px;
}

.pb-102 {
  padding-bottom: 102px;
}

.pl-102 {
  padding-inset-inline-start: 102px;
}

.pr-102 {
  padding-inset-inline-end: 102px;
}

.pt-103 {
  padding-top: 103px;
}

.pb-103 {
  padding-bottom: 103px;
}

.pl-103 {
  padding-inset-inline-start: 103px;
}

.pr-103 {
  padding-inset-inline-end: 103px;
}

.pt-104 {
  padding-top: 104px;
}

.pb-104 {
  padding-bottom: 104px;
}

.pl-104 {
  padding-inset-inline-start: 104px;
}

.pr-104 {
  padding-inset-inline-end: 104px;
}

.pt-105 {
  padding-top: 105px;
}

.pb-105 {
  padding-bottom: 105px;
}

.pl-105 {
  padding-inset-inline-start: 105px;
}

.pr-105 {
  padding-inset-inline-end: 105px;
}

.pt-106 {
  padding-top: 106px;
}

.pb-106 {
  padding-bottom: 106px;
}

.pl-106 {
  padding-inset-inline-start: 106px;
}

.pr-106 {
  padding-inset-inline-end: 106px;
}

.pt-107 {
  padding-top: 107px;
}

.pb-107 {
  padding-bottom: 107px;
}

.pl-107 {
  padding-inset-inline-start: 107px;
}

.pr-107 {
  padding-inset-inline-end: 107px;
}

.pt-108 {
  padding-top: 108px;
}

.pb-108 {
  padding-bottom: 108px;
}

.pl-108 {
  padding-inset-inline-start: 108px;
}

.pr-108 {
  padding-inset-inline-end: 108px;
}

.pt-109 {
  padding-top: 109px;
}

.pb-109 {
  padding-bottom: 109px;
}

.pl-109 {
  padding-inset-inline-start: 109px;
}

.pr-109 {
  padding-inset-inline-end: 109px;
}

.pt-110 {
  padding-top: 110px;
}

.pb-110 {
  padding-bottom: 110px;
}

.pl-110 {
  padding-inset-inline-start: 110px;
}

.pr-110 {
  padding-inset-inline-end: 110px;
}

.pt-111 {
  padding-top: 111px;
}

.pb-111 {
  padding-bottom: 111px;
}

.pl-111 {
  padding-inset-inline-start: 111px;
}

.pr-111 {
  padding-inset-inline-end: 111px;
}

.pt-112 {
  padding-top: 112px;
}

.pb-112 {
  padding-bottom: 112px;
}

.pl-112 {
  padding-inset-inline-start: 112px;
}

.pr-112 {
  padding-inset-inline-end: 112px;
}

.pt-113 {
  padding-top: 113px;
}

.pb-113 {
  padding-bottom: 113px;
}

.pl-113 {
  padding-inset-inline-start: 113px;
}

.pr-113 {
  padding-inset-inline-end: 113px;
}

.pt-114 {
  padding-top: 114px;
}

.pb-114 {
  padding-bottom: 114px;
}

.pl-114 {
  padding-inset-inline-start: 114px;
}

.pr-114 {
  padding-inset-inline-end: 114px;
}

.pt-115 {
  padding-top: 115px;
}

.pb-115 {
  padding-bottom: 115px;
}

.pl-115 {
  padding-inset-inline-start: 115px;
}

.pr-115 {
  padding-inset-inline-end: 115px;
}

.pt-116 {
  padding-top: 116px;
}

.pb-116 {
  padding-bottom: 116px;
}

.pl-116 {
  padding-inset-inline-start: 116px;
}

.pr-116 {
  padding-inset-inline-end: 116px;
}

.pt-117 {
  padding-top: 117px;
}

.pb-117 {
  padding-bottom: 117px;
}

.pl-117 {
  padding-inset-inline-start: 117px;
}

.pr-117 {
  padding-inset-inline-end: 117px;
}

.pt-118 {
  padding-top: 118px;
}

.pb-118 {
  padding-bottom: 118px;
}

.pl-118 {
  padding-inset-inline-start: 118px;
}

.pr-118 {
  padding-inset-inline-end: 118px;
}

.pt-119 {
  padding-top: 119px;
}

.pb-119 {
  padding-bottom: 119px;
}

.pl-119 {
  padding-inset-inline-start: 119px;
}

.pr-119 {
  padding-inset-inline-end: 119px;
}

.pt-120 {
  padding-top: 120px;
}

.pb-120 {
  padding-bottom: 120px;
}

.pl-120 {
  padding-inset-inline-start: 120px;
}

.pr-120 {
  padding-inset-inline-end: 120px;
}

.pt-121 {
  padding-top: 121px;
}

.pb-121 {
  padding-bottom: 121px;
}

.pl-121 {
  padding-inset-inline-start: 121px;
}

.pr-121 {
  padding-inset-inline-end: 121px;
}

.pt-122 {
  padding-top: 122px;
}

.pb-122 {
  padding-bottom: 122px;
}

.pl-122 {
  padding-inset-inline-start: 122px;
}

.pr-122 {
  padding-inset-inline-end: 122px;
}

.pt-123 {
  padding-top: 123px;
}

.pb-123 {
  padding-bottom: 123px;
}

.pl-123 {
  padding-inset-inline-start: 123px;
}

.pr-123 {
  padding-inset-inline-end: 123px;
}

.pt-124 {
  padding-top: 124px;
}

.pb-124 {
  padding-bottom: 124px;
}

.pl-124 {
  padding-inset-inline-start: 124px;
}

.pr-124 {
  padding-inset-inline-end: 124px;
}

.pt-125 {
  padding-top: 125px;
}

.pb-125 {
  padding-bottom: 125px;
}

.pl-125 {
  padding-inset-inline-start: 125px;
}

.pr-125 {
  padding-inset-inline-end: 125px;
}

.pt-126 {
  padding-top: 126px;
}

.pb-126 {
  padding-bottom: 126px;
}

.pl-126 {
  padding-inset-inline-start: 126px;
}

.pr-126 {
  padding-inset-inline-end: 126px;
}

.pt-127 {
  padding-top: 127px;
}

.pb-127 {
  padding-bottom: 127px;
}

.pl-127 {
  padding-inset-inline-start: 127px;
}

.pr-127 {
  padding-inset-inline-end: 127px;
}

.pt-128 {
  padding-top: 128px;
}

.pb-128 {
  padding-bottom: 128px;
}

.pl-128 {
  padding-inset-inline-start: 128px;
}

.pr-128 {
  padding-inset-inline-end: 128px;
}

.pt-129 {
  padding-top: 129px;
}

.pb-129 {
  padding-bottom: 129px;
}

.pl-129 {
  padding-inset-inline-start: 129px;
}

.pr-129 {
  padding-inset-inline-end: 129px;
}

.pt-130 {
  padding-top: 130px;
}

.pb-130 {
  padding-bottom: 130px;
}

.pl-130 {
  padding-inset-inline-start: 130px;
}

.pr-130 {
  padding-inset-inline-end: 130px;
}

.pt-131 {
  padding-top: 131px;
}

.pb-131 {
  padding-bottom: 131px;
}

.pl-131 {
  padding-inset-inline-start: 131px;
}

.pr-131 {
  padding-inset-inline-end: 131px;
}

.pt-132 {
  padding-top: 132px;
}

.pb-132 {
  padding-bottom: 132px;
}

.pl-132 {
  padding-inset-inline-start: 132px;
}

.pr-132 {
  padding-inset-inline-end: 132px;
}

.pt-133 {
  padding-top: 133px;
}

.pb-133 {
  padding-bottom: 133px;
}

.pl-133 {
  padding-inset-inline-start: 133px;
}

.pr-133 {
  padding-inset-inline-end: 133px;
}

.pt-134 {
  padding-top: 134px;
}

.pb-134 {
  padding-bottom: 134px;
}

.pl-134 {
  padding-inset-inline-start: 134px;
}

.pr-134 {
  padding-inset-inline-end: 134px;
}

.pt-135 {
  padding-top: 135px;
}

.pb-135 {
  padding-bottom: 135px;
}

.pl-135 {
  padding-inset-inline-start: 135px;
}

.pr-135 {
  padding-inset-inline-end: 135px;
}

.pt-136 {
  padding-top: 136px;
}

.pb-136 {
  padding-bottom: 136px;
}

.pl-136 {
  padding-inset-inline-start: 136px;
}

.pr-136 {
  padding-inset-inline-end: 136px;
}

.pt-137 {
  padding-top: 137px;
}

.pb-137 {
  padding-bottom: 137px;
}

.pl-137 {
  padding-inset-inline-start: 137px;
}

.pr-137 {
  padding-inset-inline-end: 137px;
}

.pt-138 {
  padding-top: 138px;
}

.pb-138 {
  padding-bottom: 138px;
}

.pl-138 {
  padding-inset-inline-start: 138px;
}

.pr-138 {
  padding-inset-inline-end: 138px;
}

.pt-139 {
  padding-top: 139px;
}

.pb-139 {
  padding-bottom: 139px;
}

.pl-139 {
  padding-inset-inline-start: 139px;
}

.pr-139 {
  padding-inset-inline-end: 139px;
}

.pt-140 {
  padding-top: 140px;
}

.pb-140 {
  padding-bottom: 140px;
}

.pl-140 {
  padding-inset-inline-start: 140px;
}

.pr-140 {
  padding-inset-inline-end: 140px;
}

.pt-141 {
  padding-top: 141px;
}

.pb-141 {
  padding-bottom: 141px;
}

.pl-141 {
  padding-inset-inline-start: 141px;
}

.pr-141 {
  padding-inset-inline-end: 141px;
}

.pt-142 {
  padding-top: 142px;
}

.pb-142 {
  padding-bottom: 142px;
}

.pl-142 {
  padding-inset-inline-start: 142px;
}

.pr-142 {
  padding-inset-inline-end: 142px;
}

.pt-143 {
  padding-top: 143px;
}

.pb-143 {
  padding-bottom: 143px;
}

.pl-143 {
  padding-inset-inline-start: 143px;
}

.pr-143 {
  padding-inset-inline-end: 143px;
}

.pt-144 {
  padding-top: 144px;
}

.pb-144 {
  padding-bottom: 144px;
}

.pl-144 {
  padding-inset-inline-start: 144px;
}

.pr-144 {
  padding-inset-inline-end: 144px;
}

.pt-145 {
  padding-top: 145px;
}

.pb-145 {
  padding-bottom: 145px;
}

.pl-145 {
  padding-inset-inline-start: 145px;
}

.pr-145 {
  padding-inset-inline-end: 145px;
}

.pt-146 {
  padding-top: 146px;
}

.pb-146 {
  padding-bottom: 146px;
}

.pl-146 {
  padding-inset-inline-start: 146px;
}

.pr-146 {
  padding-inset-inline-end: 146px;
}

.pt-147 {
  padding-top: 147px;
}

.pb-147 {
  padding-bottom: 147px;
}

.pl-147 {
  padding-inset-inline-start: 147px;
}

.pr-147 {
  padding-inset-inline-end: 147px;
}

.pt-148 {
  padding-top: 148px;
}

.pb-148 {
  padding-bottom: 148px;
}

.pl-148 {
  padding-inset-inline-start: 148px;
}

.pr-148 {
  padding-inset-inline-end: 148px;
}

.pt-149 {
  padding-top: 149px;
}

.pb-149 {
  padding-bottom: 149px;
}

.pl-149 {
  padding-inset-inline-start: 149px;
}

.pr-149 {
  padding-inset-inline-end: 149px;
}

.pt-150 {
  padding-top: 150px;
}

.pb-150 {
  padding-bottom: 150px;
}

.pl-150 {
  padding-inset-inline-start: 150px;
}

.pr-150 {
  padding-inset-inline-end: 150px;
}

.pt-151 {
  padding-top: 151px;
}

.pb-151 {
  padding-bottom: 151px;
}

.pl-151 {
  padding-inset-inline-start: 151px;
}

.pr-151 {
  padding-inset-inline-end: 151px;
}

.pt-152 {
  padding-top: 152px;
}

.pb-152 {
  padding-bottom: 152px;
}

.pl-152 {
  padding-inset-inline-start: 152px;
}

.pr-152 {
  padding-inset-inline-end: 152px;
}

.pt-153 {
  padding-top: 153px;
}

.pb-153 {
  padding-bottom: 153px;
}

.pl-153 {
  padding-inset-inline-start: 153px;
}

.pr-153 {
  padding-inset-inline-end: 153px;
}

.pt-154 {
  padding-top: 154px;
}

.pb-154 {
  padding-bottom: 154px;
}

.pl-154 {
  padding-inset-inline-start: 154px;
}

.pr-154 {
  padding-inset-inline-end: 154px;
}

.pt-155 {
  padding-top: 155px;
}

.pb-155 {
  padding-bottom: 155px;
}

.pl-155 {
  padding-inset-inline-start: 155px;
}

.pr-155 {
  padding-inset-inline-end: 155px;
}

.pt-156 {
  padding-top: 156px;
}

.pb-156 {
  padding-bottom: 156px;
}

.pl-156 {
  padding-inset-inline-start: 156px;
}

.pr-156 {
  padding-inset-inline-end: 156px;
}

.pt-157 {
  padding-top: 157px;
}

.pb-157 {
  padding-bottom: 157px;
}

.pl-157 {
  padding-inset-inline-start: 157px;
}

.pr-157 {
  padding-inset-inline-end: 157px;
}

.pt-158 {
  padding-top: 158px;
}

.pb-158 {
  padding-bottom: 158px;
}

.pl-158 {
  padding-inset-inline-start: 158px;
}

.pr-158 {
  padding-inset-inline-end: 158px;
}

.pt-159 {
  padding-top: 159px;
}

.pb-159 {
  padding-bottom: 159px;
}

.pl-159 {
  padding-inset-inline-start: 159px;
}

.pr-159 {
  padding-inset-inline-end: 159px;
}

.pt-160 {
  padding-top: 160px;
}

.pb-160 {
  padding-bottom: 160px;
}

.pl-160 {
  padding-inset-inline-start: 160px;
}

.pr-160 {
  padding-inset-inline-end: 160px;
}

.pt-161 {
  padding-top: 161px;
}

.pb-161 {
  padding-bottom: 161px;
}

.pl-161 {
  padding-inset-inline-start: 161px;
}

.pr-161 {
  padding-inset-inline-end: 161px;
}

.pt-162 {
  padding-top: 162px;
}

.pb-162 {
  padding-bottom: 162px;
}

.pl-162 {
  padding-inset-inline-start: 162px;
}

.pr-162 {
  padding-inset-inline-end: 162px;
}

.pt-163 {
  padding-top: 163px;
}

.pb-163 {
  padding-bottom: 163px;
}

.pl-163 {
  padding-inset-inline-start: 163px;
}

.pr-163 {
  padding-inset-inline-end: 163px;
}

.pt-164 {
  padding-top: 164px;
}

.pb-164 {
  padding-bottom: 164px;
}

.pl-164 {
  padding-inset-inline-start: 164px;
}

.pr-164 {
  padding-inset-inline-end: 164px;
}

.pt-165 {
  padding-top: 165px;
}

.pb-165 {
  padding-bottom: 165px;
}

.pl-165 {
  padding-inset-inline-start: 165px;
}

.pr-165 {
  padding-inset-inline-end: 165px;
}

.pt-166 {
  padding-top: 166px;
}

.pb-166 {
  padding-bottom: 166px;
}

.pl-166 {
  padding-inset-inline-start: 166px;
}

.pr-166 {
  padding-inset-inline-end: 166px;
}

.pt-167 {
  padding-top: 167px;
}

.pb-167 {
  padding-bottom: 167px;
}

.pl-167 {
  padding-inset-inline-start: 167px;
}

.pr-167 {
  padding-inset-inline-end: 167px;
}

.pt-168 {
  padding-top: 168px;
}

.pb-168 {
  padding-bottom: 168px;
}

.pl-168 {
  padding-inset-inline-start: 168px;
}

.pr-168 {
  padding-inset-inline-end: 168px;
}

.pt-169 {
  padding-top: 169px;
}

.pb-169 {
  padding-bottom: 169px;
}

.pl-169 {
  padding-inset-inline-start: 169px;
}

.pr-169 {
  padding-inset-inline-end: 169px;
}

.pt-170 {
  padding-top: 170px;
}

.pb-170 {
  padding-bottom: 170px;
}

.pl-170 {
  padding-inset-inline-start: 170px;
}

.pr-170 {
  padding-inset-inline-end: 170px;
}

.pt-171 {
  padding-top: 171px;
}

.pb-171 {
  padding-bottom: 171px;
}

.pl-171 {
  padding-inset-inline-start: 171px;
}

.pr-171 {
  padding-inset-inline-end: 171px;
}

.pt-172 {
  padding-top: 172px;
}

.pb-172 {
  padding-bottom: 172px;
}

.pl-172 {
  padding-inset-inline-start: 172px;
}

.pr-172 {
  padding-inset-inline-end: 172px;
}

.pt-173 {
  padding-top: 173px;
}

.pb-173 {
  padding-bottom: 173px;
}

.pl-173 {
  padding-inset-inline-start: 173px;
}

.pr-173 {
  padding-inset-inline-end: 173px;
}

.pt-174 {
  padding-top: 174px;
}

.pb-174 {
  padding-bottom: 174px;
}

.pl-174 {
  padding-inset-inline-start: 174px;
}

.pr-174 {
  padding-inset-inline-end: 174px;
}

.pt-175 {
  padding-top: 175px;
}

.pb-175 {
  padding-bottom: 175px;
}

.pl-175 {
  padding-inset-inline-start: 175px;
}

.pr-175 {
  padding-inset-inline-end: 175px;
}

.pt-176 {
  padding-top: 176px;
}

.pb-176 {
  padding-bottom: 176px;
}

.pl-176 {
  padding-inset-inline-start: 176px;
}

.pr-176 {
  padding-inset-inline-end: 176px;
}

.pt-177 {
  padding-top: 177px;
}

.pb-177 {
  padding-bottom: 177px;
}

.pl-177 {
  padding-inset-inline-start: 177px;
}

.pr-177 {
  padding-inset-inline-end: 177px;
}

.pt-178 {
  padding-top: 178px;
}

.pb-178 {
  padding-bottom: 178px;
}

.pl-178 {
  padding-inset-inline-start: 178px;
}

.pr-178 {
  padding-inset-inline-end: 178px;
}

.pt-179 {
  padding-top: 179px;
}

.pb-179 {
  padding-bottom: 179px;
}

.pl-179 {
  padding-inset-inline-start: 179px;
}

.pr-179 {
  padding-inset-inline-end: 179px;
}

.pt-180 {
  padding-top: 180px;
}

.pb-180 {
  padding-bottom: 180px;
}

.pl-180 {
  padding-inset-inline-start: 180px;
}

.pr-180 {
  padding-inset-inline-end: 180px;
}

.pt-181 {
  padding-top: 181px;
}

.pb-181 {
  padding-bottom: 181px;
}

.pl-181 {
  padding-inset-inline-start: 181px;
}

.pr-181 {
  padding-inset-inline-end: 181px;
}

.pt-182 {
  padding-top: 182px;
}

.pb-182 {
  padding-bottom: 182px;
}

.pl-182 {
  padding-inset-inline-start: 182px;
}

.pr-182 {
  padding-inset-inline-end: 182px;
}

.pt-183 {
  padding-top: 183px;
}

.pb-183 {
  padding-bottom: 183px;
}

.pl-183 {
  padding-inset-inline-start: 183px;
}

.pr-183 {
  padding-inset-inline-end: 183px;
}

.pt-184 {
  padding-top: 184px;
}

.pb-184 {
  padding-bottom: 184px;
}

.pl-184 {
  padding-inset-inline-start: 184px;
}

.pr-184 {
  padding-inset-inline-end: 184px;
}

.pt-185 {
  padding-top: 185px;
}

.pb-185 {
  padding-bottom: 185px;
}

.pl-185 {
  padding-inset-inline-start: 185px;
}

.pr-185 {
  padding-inset-inline-end: 185px;
}

.pt-186 {
  padding-top: 186px;
}

.pb-186 {
  padding-bottom: 186px;
}

.pl-186 {
  padding-inset-inline-start: 186px;
}

.pr-186 {
  padding-inset-inline-end: 186px;
}

.pt-187 {
  padding-top: 187px;
}

.pb-187 {
  padding-bottom: 187px;
}

.pl-187 {
  padding-inset-inline-start: 187px;
}

.pr-187 {
  padding-inset-inline-end: 187px;
}

.pt-188 {
  padding-top: 188px;
}

.pb-188 {
  padding-bottom: 188px;
}

.pl-188 {
  padding-inset-inline-start: 188px;
}

.pr-188 {
  padding-inset-inline-end: 188px;
}

.pt-189 {
  padding-top: 189px;
}

.pb-189 {
  padding-bottom: 189px;
}

.pl-189 {
  padding-inset-inline-start: 189px;
}

.pr-189 {
  padding-inset-inline-end: 189px;
}

.pt-190 {
  padding-top: 190px;
}

.pb-190 {
  padding-bottom: 190px;
}

.pl-190 {
  padding-inset-inline-start: 190px;
}

.pr-190 {
  padding-inset-inline-end: 190px;
}

.pt-191 {
  padding-top: 191px;
}

.pb-191 {
  padding-bottom: 191px;
}

.pl-191 {
  padding-inset-inline-start: 191px;
}

.pr-191 {
  padding-inset-inline-end: 191px;
}

.pt-192 {
  padding-top: 192px;
}

.pb-192 {
  padding-bottom: 192px;
}

.pl-192 {
  padding-inset-inline-start: 192px;
}

.pr-192 {
  padding-inset-inline-end: 192px;
}

.pt-193 {
  padding-top: 193px;
}

.pb-193 {
  padding-bottom: 193px;
}

.pl-193 {
  padding-inset-inline-start: 193px;
}

.pr-193 {
  padding-inset-inline-end: 193px;
}

.pt-194 {
  padding-top: 194px;
}

.pb-194 {
  padding-bottom: 194px;
}

.pl-194 {
  padding-inset-inline-start: 194px;
}

.pr-194 {
  padding-inset-inline-end: 194px;
}

.pt-195 {
  padding-top: 195px;
}

.pb-195 {
  padding-bottom: 195px;
}

.pl-195 {
  padding-inset-inline-start: 195px;
}

.pr-195 {
  padding-inset-inline-end: 195px;
}

.pt-196 {
  padding-top: 196px;
}

.pb-196 {
  padding-bottom: 196px;
}

.pl-196 {
  padding-inset-inline-start: 196px;
}

.pr-196 {
  padding-inset-inline-end: 196px;
}

.pt-197 {
  padding-top: 197px;
}

.pb-197 {
  padding-bottom: 197px;
}

.pl-197 {
  padding-inset-inline-start: 197px;
}

.pr-197 {
  padding-inset-inline-end: 197px;
}

.pt-198 {
  padding-top: 198px;
}

.pb-198 {
  padding-bottom: 198px;
}

.pl-198 {
  padding-inset-inline-start: 198px;
}

.pr-198 {
  padding-inset-inline-end: 198px;
}

.pt-199 {
  padding-top: 199px;
}

.pb-199 {
  padding-bottom: 199px;
}

.pl-199 {
  padding-inset-inline-start: 199px;
}

.pr-199 {
  padding-inset-inline-end: 199px;
}

.pt-200 {
  padding-top: 200px;
}

.pb-200 {
  padding-bottom: 200px;
}

.pl-200 {
  padding-inset-inline-start: 200px;
}

.pr-200 {
  padding-inset-inline-end: 200px;
}

:root {
  --td-heading-font:"Marcellus", serif;
  --td-body-font:"Jost", sans-serif;
  --td-ff-fontawesome: "Font Awesome 6 Free";
  --td-white: hsl(0, 0%, 100%);
  --td-black: hsl(0, 0%, 0%);
  --td-placeholder: hsla(0, 0%, 0%, 0.5);
  --td-selection: hsl(0, 0%, 0%);
  --td-heading: #151415;
  --td-primary: #AA8453;
  --td-secondary:#151415;
  --td-text-primary: #444344;
  --td-text-secondary: #A7A7A7;
  --td-bg-1: #FFF4E5;
}

/*---------------------------------
/*  1.2 spacing
---------------------------------*/
.section_space-py {
  padding-block-start: clamp(1.625rem, 6.5vw + 1rem, 5rem);
  padding-block-end: clamp(1.625rem, 6.5vw + 1rem, 5rem);
}

.section_space-my {
  margin-block-start: clamp(1.625rem, 6.5vw + 1rem, 5rem);
  margin-block-end: clamp(1.625rem, 6.5vw + 1rem, 5rem);
}

.section_space-mT {
  margin-block-start: clamp(1.625rem, 6.5vw + 1rem, 5rem);
}

.section_space-mB {
  margin-block-end: clamp(1.625rem, 6.5vw + 1rem, 5rem);
}

.section_space-pT {
  padding-block-start: clamp(1.625rem, 6.5vw + 1rem, 5rem);
}

.section_space-pB {
  padding-block-end: clamp(1.625rem, 6.5vw + 1rem, 5rem);
}

.title_mt {
  margin-block-start: clamp(1.125rem, 6.5vw, 3.125rem);
}

.title_mb {
  margin-block-end: clamp(1.125rem, 6.5vw, 3.125rem);
}

/*----------------------------------------*/
/*   1.3 typography
/*----------------------------------------*/
* {
  margin: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

body {
  font-family: var(--td-body-font);
  font-size: 16px;
  font-weight: normal;
  line-height: 1.5;
  color: var(--td-text-ternary);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--td-heading-font);
  color: var(--td-heading);
  margin-top: 0px;
  line-height: 1.3;
  margin-bottom: 0;
  word-break: break-word;
}

h1,
.h1 {
  font-size: clamp(1.875rem, 2vw + 1rem, 5rem);
  line-height: 1.2;
  font-weight: 500;
}

h2,
.h2 {
  font-size: clamp(1.5rem, 1.5vw + 1rem, 3.75rem);
  line-height: 1.3;
  font-weight: 500;
}

h3,
.h3 {
  font-size: clamp(1.25rem, 0.75vw + 1rem, 3rem);
  line-height: 1.4;
  font-weight: 500;
}

h4,
.h4 {
  font-size: clamp(1.125rem, 1vw + 1rem, 1.875rem);
  line-height: 1.5;
  font-weight: 500;
}

h5,
.h5 {
  font-size: clamp(1rem, 0.5vw + 1rem, 1.25rem);
  line-height: 1.6;
  font-weight: 500;
}

h6,
.h6 {
  font-size: clamp(0.875rem, 0.4vw + 1rem, 1.125rem);
  line-height: 1.6;
  font-weight: 500;
}

ul {
  margin: 0px;
  padding: 0px;
}

p {
  font-size: clamp(0.75rem, 0.4vw + 1rem, 1rem);
  line-height: 1.625;
  color: var(--td-text-primary);
  margin-bottom: 0;
}

a {
  text-decoration: none;
}

a,
.btn,
button,
img,
.transition-3,
h1,
h2,
h3,
h4,
h5,
h6 {
  -webkit-transition: all 0.3s 0s ease-out;
  -moz-transition: all 0.3s 0s ease-out;
  -ms-transition: all 0.3s 0s ease-out;
  -o-transition: all 0.3s 0s ease-out;
  transition: all 0.3s 0s ease-out;
}

a:focus,
.button:focus {
  text-decoration: none;
  outline: none;
}

a:focus,
a:hover {
  text-decoration: none;
  color: inherit;
}

a,
button {
  color: inherit;
  outline: none;
  border: none;
  background: transparent;
}

.o-x-clip {
  overflow-x: clip;
}

img {
  max-width: 100%;
  object-fit: cover;
}

button {
  font-family: var(--td-ff-body) !important;
}

button:hover {
  cursor: pointer;
}

button:focus {
  outline: 0;
  font-family: var(--td-ff-body);
}

.uppercase {
  text-transform: uppercase;
}

.capitalize {
  text-transform: capitalize;
}

hr:not([size]) {
  border-color: var(--td-card-bg-1);
  opacity: 1;
  border-width: 1px;
}

*::-moz-selection {
  background: var(--td-black);
  color: var(--td-white);
  text-shadow: none;
}

::-moz-selection {
  background: var(--td-black);
  color: var(--td-white);
  text-shadow: none;
}

::selection {
  background: var(--td-black);
  color: var(--td-white);
  text-shadow: none;
}

*::-moz-placeholder {
  opacity: 1;
  font-size: 14px;
}

*::placeholder {
  opacity: 1;
  font-size: 14px;
  font-weight: 400;
}

/*---------------------------------
  1.2 Common Classes
---------------------------------*/
.w-img img {
  width: 100%;
}

.m-img img {
  max-width: 100%;
}

.fix {
  overflow: hidden;
}

.clear {
  clear: both;
}

.f-left {
  float: left;
}

.f-right {
  float: right;
}

.z-index-1 {
  z-index: 1;
}

.z-index-11 {
  z-index: 11;
}

.p-relative {
  position: relative;
}

.p-absolute {
  position: absolute;
}

.position-absolute {
  position: absolute;
}

.include-bg {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}

.hr-1 {
  border-top: 1px solid rgb(232, 232, 232);
}

.x-clip {
  overflow-x: clip;
}

.o-visible {
  overflow: visible;
}

.valign {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
}

/*----------------------------------------
  Bootstrap customize
-----------------------------------------*/
@media (min-width: 1601px) {
  .container,
  .container-lg,
  .container-md,
  .container-sm,
  .container-xl,
  .container-xxl {
    max-width: 1345px;
  }
}
/*----------------------------------------
  Mfp customize
-----------------------------------------*/
.mfp-iframe-holder .mfp-content {
  line-height: 0;
  width: 100%;
  max-width: 1280px;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1600px) and (max-width: 1800px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 1000px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 850px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 820px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 750px;
  }
}

.mfp-close {
  -webkit-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}
.mfp-close:hover {
  color: var(--td-white);
}
.mfp-close::after {
  position: absolute;
  content: "\f00d";
  height: 100%;
  width: 100%;
  font-family: var(--td-ff-fontawesome);
  font-size: 20px;
  font-weight: 900;
  inset-inline-end: -5px;
  margin-top: -16px;
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .mfp-close::after {
    inset-inline-end: 15px;
    margin-top: -30px;
  }
}

.home-2 {
  background-color: var(--td-bg);
}

/*----------------------------------------*/
/*  flatpickr customize styles
/*----------------------------------------*/
.flatpickr-day.flatpickr-disabled,
.flatpickr-day.flatpickr-disabled:hover {
  cursor: not-allowed;
  color: rgba(57, 57, 57, 0.3);
}

.flatpickr-day,
.flatpickr-day.nextMonthDay {
  background: #F5F5F5;
  color: #393939;
}

.flatpickr-calendar {
  font-family: var(--td-body-font) !important;
}

.flatpickr-day.today {
  border-color: #fff;
  background: #151415;
  color: #fff !important;
}

/*----------------------------------------*/
/*  2.7 forms
/*----------------------------------------*/
input[type=text],
input[type=search],
input[type=email],
input[type=tel],
input[type=number],
input[type=password],
textarea {
  outline: none;
  height: 45px;
  width: 100%;
  padding: 0 15px;
  border: 1px solid #CACACA;
  color: rgba(8, 8, 8, 0.6);
  background: transparent;
  font-size: 14px;
}
input[type=text]:focus,
input[type=search]:focus,
input[type=email]:focus,
input[type=tel]:focus,
input[type=number]:focus,
input[type=password]:focus,
textarea:focus {
  border-color: var(--td-primary);
  box-shadow: unset;
  opacity: 1;
}
input[type=text].input-exceptional,
input[type=search].input-exceptional,
input[type=email].input-exceptional,
input[type=tel].input-exceptional,
input[type=number].input-exceptional,
input[type=password].input-exceptional,
textarea.input-exceptional {
  background: #F4F4F4;
}
input[type=text].input-exceptional-2,
input[type=search].input-exceptional-2,
input[type=email].input-exceptional-2,
input[type=tel].input-exceptional-2,
input[type=number].input-exceptional-2,
input[type=password].input-exceptional-2,
textarea.input-exceptional-2 {
  height: 35px;
  background-color: var(--td-white);
}
input[type=text].input-design-2,
input[type=search].input-design-2,
input[type=email].input-design-2,
input[type=tel].input-design-2,
input[type=number].input-design-2,
input[type=password].input-design-2,
textarea.input-design-2 {
  background-color: transparent;
  border: none;
  border-bottom: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 0;
  color: var(--td-white);
}
input[type=text].input-design-2:focus,
input[type=search].input-design-2:focus,
input[type=email].input-design-2:focus,
input[type=tel].input-design-2:focus,
input[type=number].input-design-2:focus,
input[type=password].input-design-2:focus,
textarea.input-design-2:focus {
  border-color: var(--td-white);
  box-shadow: unset;
  opacity: 1;
  background-color: rgba(8, 8, 8, 0.2);
}
input[type=text].input-design-pxNone,
input[type=search].input-design-pxNone,
input[type=email].input-design-pxNone,
input[type=tel].input-design-pxNone,
input[type=number].input-design-pxNone,
input[type=password].input-design-pxNone,
textarea.input-design-pxNone {
  padding: 0 0;
}
input[type=text].input-design-pxNone:focus,
input[type=search].input-design-pxNone:focus,
input[type=email].input-design-pxNone:focus,
input[type=tel].input-design-pxNone:focus,
input[type=number].input-design-pxNone:focus,
input[type=password].input-design-pxNone:focus,
textarea.input-design-pxNone:focus {
  background-color: rgba(8, 8, 8, 0);
}

textarea {
  padding: 14px 24px;
}
textarea:focus {
  border-color: var(--td-heading);
}

.form-switch {
  display: flex;
  align-items: center;
}
.form-switch input[type=checkbox] {
  opacity: 1;
  position: relative;
  margin-inline-start: 0 !important;
  margin-top: 0;
  outline: none;
  margin-bottom: 0;
}
.form-switch input[type=checkbox]:checked {
  background-color: var(--td-primary);
  border-color: var(--td-primary);
}
.form-switch input[type=checkbox]:focus {
  outline: 0;
  box-shadow: none;
}
.form-switch input[type=checkbox] ~ label {
  padding-inline-start: 10px;
}
.form-switch input[type=checkbox] ~ label::before, .form-switch input[type=checkbox] ~ label::after {
  display: none;
}

.animate-custom .cbx {
  -webkit-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
}
.animate-custom .cbx::before {
  display: none;
}
.animate-custom .cbx span {
  display: inline-block;
  vertical-align: middle;
}
.animate-custom .cbx span a {
  color: var(--td-primary);
}
.animate-custom .cbx span a:hover {
  color: #000000;
}
.animate-custom .cbx span:first-child {
  position: relative;
  width: 18px;
  height: 18px;
  -webkit-border-radius: 0px;
  -moz-border-radius: 0px;
  -o-border-radius: 0px;
  -ms-border-radius: 0px;
  border-radius: 0px;
  transform: scale(1);
  vertical-align: middle;
  border: 1px solid rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}
.animate-custom .cbx span:first-child svg {
  position: absolute;
  z-index: 1;
  top: 4px;
  inset-inline-start: 2px;
  fill: none;
  stroke: var(--td-white);
  stroke-width: 1;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-dasharray: 16px;
  stroke-dashoffset: 16px;
  transition: all 0.3s ease;
  transition-delay: 0.1s;
  transform: translate3d(0, 0, 0);
}
.animate-custom .cbx span:first-child:before {
  content: "";
  width: 100%;
  height: 100%;
  background: var(--td-primary);
  display: block;
  transform: scale(0);
  opacity: 1;
  border-radius: 50%;
  transition-delay: 0.2s;
}
.animate-custom .cbx span:last-child {
  margin-inline-start: 5px;
  color: var(--td-text-primary);
  font-weight: 500;
  font-size: 14px;
}
@media (max-width: 767px) {
  .animate-custom .cbx span:last-child {
    font-size: 10px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .animate-custom .cbx span:last-child {
    font-size: 14px;
  }
}
.animate-custom .cbx span:last-child:after {
  content: "";
  position: absolute;
  top: 8px;
  inset-inline-start: 0;
  height: 1px;
  width: 100%;
  background: #b9b8c3;
  transform-origin: 0 0;
  transform: scaleX(0);
}
.animate-custom .cbx:hover span:first-child {
  border-color: var(--td-primary);
}
.animate-custom .inp-cbx:checked + .cbx span:first-child {
  border-color: var(--td-primary);
  background: var(--td-primary);
  animation: check-15 0.6s ease;
}
.animate-custom .inp-cbx:checked + .cbx span:first-child svg {
  stroke-dashoffset: 0;
}
.animate-custom .inp-cbx:checked + .cbx span:first-child:before {
  transform: scale(2.2);
  opacity: 0;
  transition: all 0.6s ease;
}
.animate-custom .inp-cbx:checked + .cbx span:last-child {
  transition: all 0.3s ease;
}
.animate-custom input[type=checkbox] ~ label::after {
  display: none;
}
.animate-custom input[type=checkbox] ~ label {
  padding-inline-start: 0;
}

@keyframes check-15 {
  50% {
    transform: scale(1.2);
  }
}
.was-validated .td-form-group .input-field {
  position: relative;
}
.was-validated .td-form-group .input-field input {
  border-color: var(--td-danger);
  background: rgba(220, 29, 75, 0.1);
}
.was-validated .td-form-group .input-field input:focus {
  background: rgba(220, 29, 75, 0.1);
}

.td-form-group.has-right-icon .input-field .form-control {
  padding-inline-end: 50px;
}
.td-form-group.has-right-icon .input-icon {
  position: absolute;
  inset-inline-end: 15px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
}
.td-form-group.has-right-icon .input-icon i {
  font-size: 14px;
}
.td-form-group.has-right-icon .input-icon.eyeicon {
  cursor: pointer;
  inset-inline-end: 20px !important;
  inset-inline-start: auto !important;
}
.td-form-group.has-right-icon .input-icon.eyeicon img {
  width: 18px;
}
.td-form-group.has-right-icon .input-icon.icon-selected svg * {
  stroke: rgba(21, 20, 21, 0.7);
  /* Change stroke color */
  fill: rgba(21, 20, 21, 0.7);
  /* Change stroke color */
  stroke-opacity: 1;
  /* Full opacity */
  transition: all 0.3s ease;
  /* Smooth animation */
}
.td-form-group.selected_icon .input-icon {
  inset-inline-end: 33px;
  cursor: pointer;
}
.td-form-group.has-left-icon .input-field .form-control {
  padding-inline-end: 50px;
}
.td-form-group.has-left-icon .input-icon {
  position: absolute;
  inset-inline-start: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20px;
  width: max-content;
}
.td-form-group.has-left-icon .input-icon.eyeicon {
  cursor: pointer;
}
.td-form-group .input-field {
  position: relative;
}
.td-form-group .input-field.date-of-birth {
  position: relative;
}
.td-form-group .input-field.date-of-birth .icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-end: 15px;
}
.td-form-group .input-field.has-right-icon {
  position: relative;
}
.td-form-group .input-field.has-right-icon .form-control {
  color: #008080;
}
.td-form-group .input-field.has-right-icon .icon {
  position: absolute;
  inset-inline-end: 15px;
  top: 50%;
  transform: translateY(-50%);
  background-color: #F8F9FA;
}
.td-form-group .input-field.has-right-icon .icon .copy-icon {
  font-size: 14px;
  color: #6B7280;
}
.td-form-group .input-field.has-right-icon .icon .copy-tooltip {
  position: absolute;
  top: -30px;
  inset-inline-end: 0;
  background-color: #000;
  color: var(--td-white);
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  white-space: nowrap;
}
.td-form-group .input-field.has-right-icon .icon.show-tooltip .copy-tooltip {
  opacity: 1;
}
.td-form-group .input-field .edit-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-end: 15px;
  height: 20px;
  display: flex;
  padding: 2px 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 4px;
  background: var(--td-card-bg-1);
  color: var(--td-white);
  font-size: 0.625rem;
  font-weight: 400;
  line-height: 1.6;
}
.td-form-group .input-field.input-group {
  flex-wrap: nowrap;
}
.td-form-group .input-field .input-group-text {
  color: var(--td-white);
  background: rgba(255, 255, 255, 0.08);
  mix-blend-mode: normal;
  border: 1px solid rgba(255, 255, 255, 0.08);
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -o-border-radius: 12px;
  -ms-border-radius: 12px;
  border-radius: 12px;
}
.td-form-group .input-field.disabled input,
.td-form-group .input-field.disabled textarea {
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
}
.td-form-group .input-field.disabled input:focus,
.td-form-group .input-field.disabled textarea:focus {
  border-color: rgba(255, 255, 255, 0.08);
}
.td-form-group .input-field .text-content {
  background: var(--td-white);
  box-shadow: 0px 4px 10px rgba(0, 101, 255, 0.04);
  border-radius: 5px;
  position: absolute;
  top: 50%;
  inset-inline-end: 5px;
  transform: translateY(-50%);
  padding: 5px 8px 6px;
  font-size: 14px;
  font-weight: 500;
  color: var(--td-primary);
}
.td-form-group .input-field input,
.td-form-group .input-field textarea {
  font-size: 14px;
}
.td-form-group .input-field input::-webkit-input-placeholder,
.td-form-group .input-field textarea::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: rgba(21, 20, 21, 0.65);
  font-size: 14px;
}
.td-form-group .input-field input::-moz-placeholder,
.td-form-group .input-field textarea::-moz-placeholder {
  /* Firefox 19+ */
  color: rgba(21, 20, 21, 0.65);
  font-size: 14px;
}
.td-form-group .input-field input:-moz-placeholder,
.td-form-group .input-field textarea:-moz-placeholder {
  /* Firefox 4-18 */
  color: rgba(21, 20, 21, 0.65);
  font-size: 14px;
}
.td-form-group .input-field input:-ms-input-placeholder,
.td-form-group .input-field textarea:-ms-input-placeholder {
  /* IE 10+  Edge*/
  color: rgba(21, 20, 21, 0.65);
  font-size: 14px;
}
.td-form-group .input-field input::placeholder,
.td-form-group .input-field textarea::placeholder {
  /* MODERN BROWSER */
  color: rgba(21, 20, 21, 0.65);
  font-size: 14px;
}
.td-form-group .input-field textarea {
  padding: 12px 15px;
  height: 150px;
  resize: none;
  line-height: 1.5;
  border-radius: 0px;
  border: 1px solid rgba(0, 128, 128, 0.2);
  background: #F8F9FA;
  color: var(--td-heading);
}
.td-form-group .input-field textarea:focus {
  border-color: var(--td-primary);
}
.td-form-group .input-field textarea::placeholder {
  color: #9BA2AE;
}
.td-form-group .input-field.height-large textarea {
  height: 237px;
}
.td-form-group .input-field .form-control {
  height: 50px;
  border-radius: 0px;
  background: transparent;
  color: var(--td-heading);
  font-size: 14px;
  font-weight: 400;
  line-height: 100%;
  border: 1px solid rgba(21, 20, 21, 0.16);
}
.td-form-group .input-field .form-control::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  font-size: 14px;
  font-weight: 500;
  line-height: 100%;
  color: rgba(0, 0, 0, 0.7);
}
.td-form-group .input-field .form-control::-moz-placeholder {
  /* Firefox 19+ */
  font-size: 14px;
  font-weight: 500;
  line-height: 100%;
  color: rgba(0, 0, 0, 0.7);
}
.td-form-group .input-field .form-control:-moz-placeholder {
  /* Firefox 4-18 */
  font-size: 14px;
  font-weight: 500;
  line-height: 100%;
  color: rgba(0, 0, 0, 0.7);
}
.td-form-group .input-field .form-control:-ms-input-placeholder {
  /* IE 10+  Edge*/
  font-size: 14px;
  font-weight: 500;
  line-height: 100%;
  color: rgba(0, 0, 0, 0.7);
}
.td-form-group .input-field .form-control::placeholder {
  /* MODERN BROWSER */
  font-size: 14px;
  font-weight: 500;
  line-height: 100%;
  color: rgba(0, 0, 0, 0.7);
}
.td-form-group .input-field .form-control:focus {
  border: 1px solid var(--td-primary);
}
.td-form-group .input-field .form-control-2 {
  background: var(--td-white);
}
.td-form-group .input-field .form-control-focus:focus {
  border: 1px solid var(--td-primary);
}
.td-form-group .input-field-2 .form-control {
  font-size: 14px;
  border: 1px solid transparent;
  color: rgba(8, 8, 8, 0.7);
  background-color: rgba(8, 8, 8, 0.04);
}
.td-form-group .input-field-icon input {
  padding: 0 45px 0 15px;
}
[dir=rtl] .td-form-group .input-field-icon input {
  padding: 0 15px 0 45px;
}
.td-form-group .input-field-exceptional {
  margin-top: 8px;
}
.td-form-group .input-field-phone {
  position: relative;
}
.td-form-group .input-field-phone .form-control {
  padding: 0 15px 0 75px;
}
.td-form-group .input-field-phone .country-code {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-start: 15px;
  padding-inline-end: 10px;
  border-inline-end: 1px solid #CACACA;
}
.td-form-group .input-description {
  font-size: 12px;
  margin-top: 7px;
}
.td-form-group .input-label {
  color: rgba(0, 0, 0, 0.7);
  font-size: 14px;
  font-weight: 600;
  line-height: 100%;
  display: flex;
  margin-bottom: 0.5em;
}
.td-form-group .input-label span {
  padding-inline-start: 4px;
  display: flex;
  align-items: center;
  gap: 6px;
  color: #EC0707;
}
.td-form-group .input-label-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.td-form-group .input-label-inner > p {
  font-size: 12px;
}
.td-form-group .input-select .nice-select {
  height: 44px;
  width: 100%;
  padding: 0 15px;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  float: none;
  border: 1px solid rgba(255, 255, 255, 0.08);
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -o-border-radius: 12px;
  -ms-border-radius: 12px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.08);
}
.td-form-group .input-select .nice-select .current {
  text-align: left;
  font-size: 14px;
  position: relative;
  color: var(--td-white);
}
.td-form-group .input-select .nice-select .list {
  -webkit-transform: scale(1) translateY(0);
  -moz-transform: scale(1) translateY(0);
  -ms-transform: scale(1) translateY(0);
  -o-transform: scale(1) translateY(0);
  transform: scale(1) translateY(0);
  width: 100%;
  padding: 10px 0;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -o-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
  background: #242424;
  border-radius: 12px;
  border-style: solid;
  border-color: rgba(255, 255, 255, 0.08);
  border-width: 1px;
  padding: 12px 12px 12px 12px;
  max-height: 300px;
  overflow-y: scroll;
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}
.td-form-group .input-select .nice-select::after {
  font-size: 16px;
  inset-inline-end: 16px;
  width: 8px;
  height: 8px;
  border-bottom: 1.5px solid var(--td-text-primary);
  border-inline-end: 1.5px solid var(--td-text-primary);
  font-size: 16px;
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  border: 5px solid;
  border-top-color: rgba(0, 0, 0, 0);
  border-left-color: rgba(0, 0, 0, 0);
  background-color: rgba(0, 0, 0, 0);
  transition: all ease-in-out 0.2s;
  margin-top: -2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  -ms-border-radius: 2px;
  border-radius: 2px;
}
.td-form-group .input-select .nice-select .option {
  font-size: 14px;
  line-height: 38px;
  min-height: 38px;
  color: var(--td-white);
  border-radius: 10px;
  padding: 0 10px;
}
.td-form-group .input-select .nice-select .option.selected {
  font-weight: 500;
}
.td-form-group .input-select .nice-select .option:hover {
  background-color: #353535;
}
.td-form-group .input-select .nice-select .option.selected.focus {
  background-color: #353535;
}
.td-form-group .input-select .nice-select.open, .td-form-group .input-select .nice-select:focus {
  background-color: #353535;
}
.td-form-group.input-fill .input-label {
  font-weight: 700;
}
.td-form-group.input-fill input,
.td-form-group.input-fill select,
.td-form-group.input-fill textarea {
  background-color: #FCFCFC;
  border: 1px solid rgba(21, 20, 21, 0.2);
}
.td-form-group.input-fill input:focus,
.td-form-group.input-fill select:focus,
.td-form-group.input-fill textarea:focus {
  border-color: var(--td-primary);
}
.td-form-group .form-select {
  height: 50px;
  border-radius: 8px;
  font-size: 14px;
}
.td-form-group .form-select:focus {
  font-size: 14px;
}
.td-form-group .otp-verification {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  gap: 10px 10px;
  flex-wrap: wrap;
  max-width: max-content;
  justify-content: center;
  margin: 0 auto;
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .td-form-group .otp-verification {
    gap: 10px 10px;
  }
}
.td-form-group .otp-verification input {
  background: rgba(103, 107, 113, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  width: 69.83px;
  height: 77px;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .td-form-group .otp-verification input {
    height: 55px;
    width: 50px;
  }
}

.feedback-invalid {
  font-size: 12px;
  margin-top: 3px;
  color: #DC1D4B;
  display: none;
}
.feedback-invalid.active {
  display: block;
}

.input-attention {
  font-size: 12px;
  color: var(--tdvar(--td-danger));
}
.input-attention.xs {
  font-size: 10px;
}

.image-upload .td-form-group .input-label-2 {
  color: #6B7280;
  font-size: 14px;
  font-weight: 500;
  line-height: 100%;
  display: flex;
  margin-bottom: 0.4em;
}

*::-moz-placeholder {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  font-weight: 400;
  opacity: 1;
}

*::placeholder {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  font-weight: 400;
  opacity: 1;
}

.common-select2-dropdown .select2-container {
  width: 100% !important;
}
.common-select2-dropdown .select2-container.select2-container--open .select2-selection--single {
  border-radius: 20px 20px 0 0;
}
.common-select2-dropdown .select2-container .select2-selection--single {
  height: 45px;
  border-radius: 40px;
}
.common-select2-dropdown .select2-container--default .select2-selection--single {
  border: 1px solid #CACACA;
  background: var(--td-white);
}
.common-select2-dropdown .select2-container--default .select2-selection--single .select2-selection__rendered {
  color: rgba(8, 8, 8, 0.6);
  line-height: 43px;
  font-size: 14px;
  padding-inline-end: 35px;
  padding-inline-start: 14px;
}
.common-select2-dropdown .select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 40px;
  position: absolute;
  top: 1px;
  inset-inline-end: 10px;
  width: 20px;
}
.common-select2-dropdown .select2-dropdown {
  background-color: var(--td-bg);
  border: 1px solid var(--td-card-bg-2);
  border-radius: 4px;
}
.common-select2-dropdown .select2-results__option:hover {
  background-color: rgba(255, 255, 255, 0.1);
}
.common-select2-dropdown .select2-container--default .select2-search--dropdown .select2-search__field {
  border: 1px solid #aaa;
  color: var(--td-white);
  padding: 0 15px;
}
.common-select2-dropdown .select2-results__option {
  padding: 6px 15px;
  user-select: none;
  -webkit-user-select: none;
  font-size: 14px;
  color: var(--td-white);
}

.common-payment-form input[type=text],
.common-payment-form input[type=number] {
  outline: none;
  height: 40px;
  width: 100%;
  padding: 0 15px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  color: var(--td-white);
  font-size: 12px;
}
.common-payment-form input[type=text]::placeholder,
.common-payment-form input[type=number]::placeholder {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
}

.payment-method-checkbox {
  position: relative;
  margin: 10px 10px 0 0;
  /* Show checkmark only when input is checked */
}
.payment-method-checkbox input[type=radio] {
  display: none;
}
.payment-method-checkbox .check-box-image {
  width: 50px;
  height: 50px;
  background-color: var(--td-white);
  border: 2px solid rgba(8, 8, 8, 0.1);
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  cursor: pointer;
}
.payment-method-checkbox .check-box-image .img {
  width: 40px;
  height: 40px;
}
.payment-method-checkbox .check-box-image .img img {
  height: 100%;
  width: 100%;
  object-fit: contain;
}
.payment-method-checkbox .check-box-image::before {
  content: "✔";
  position: absolute;
  top: 3px;
  inset-inline-start: 3px;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background-color: green;
  color: white;
  font-size: 12px;
  font-weight: bold;
  display: none;
  z-index: 2;
  align-items: center;
  justify-content: center;
}
.payment-method-checkbox .check-box-image::after {
  content: "";
  position: absolute;
  background: rgba(0, 255, 0, 0.1);
  top: 0;
  inset-inline-start: 0;
  width: 100%;
  height: 100%;
  border-radius: 5px;
  z-index: 1;
  display: none;
}
.payment-method-checkbox input[type=radio]:checked + .check-box-image {
  border-color: green;
}
.payment-method-checkbox input[type=radio]:checked + .check-box-image::before {
  display: flex;
}
.payment-method-checkbox input[type=radio]:checked + .check-box-image::after {
  display: block;
}

.custom-file-input {
  height: auto;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  width: 100%;
  padding: 8px 15px;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.04);
  position: relative;
}
.custom-file-input .upload-btn {
  height: 26px;
  display: flex;
  padding: 5px 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--td-white);
  font-size: 10px;
  font-weight: 400;
  line-height: 16px;
}
.custom-file-input #fileInput {
  display: none;
}
.custom-file-input .preview-area {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
}
.custom-file-input .preview-area .image-container {
  position: relative;
  display: inline-block;
  margin-inline-end: 10px;
}
.custom-file-input .preview-area .image-container img {
  width: 26px;
  height: 26px;
  object-fit: cover;
  border-radius: 4px;
}
.custom-file-input .preview-area .image-container .remove-btn {
  position: absolute;
  top: -4px;
  inset-inline-end: -4px;
  cursor: pointer;
  color: red;
  font-size: 12px;
  background: white;
  border-radius: 50%;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.custom-file-input .preview-area #fileName {
  color: rgba(255, 255, 255, 0.4);
  font-size: 14px;
  font-style: italic;
  font-weight: 400;
  line-height: 20px;
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.custom-file-input .preview-area-2 {
  position: relative;
}
.custom-file-input .preview-area-2 img {
  width: 26px;
  height: 26px;
  border-radius: 4px;
}
.custom-file-input .preview-area-2 .remove-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-end: -40px;
  cursor: pointer;
}
.custom-file-input .hidden {
  display: none;
}

.product-details-form .form-title {
  border-bottom: 1px solid var(--td-card-bg-1);
  margin-top: 24px;
}
.product-details-form .form-title h5 {
  color: var(--td-white);
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.3333333333;
  padding-bottom: 8px;
}
.product-details-form .set-infomation-btn {
  margin-top: 16px;
}

.set-method-btn {
  margin-top: 30px;
}

.custom-quill-editor .ql-snow .ql-stroke {
  stroke: var(--td-white);
}
.custom-quill-editor .ql-snow .ql-fill,
.custom-quill-editor .ql-snow .ql-stroke.ql-fill {
  fill: var(--td-white);
}
.custom-quill-editor .ql-snow .ql-picker {
  color: var(--td-white);
}
.custom-quill-editor .ql-toolbar.ql-snow {
  background: rgba(255, 255, 255, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px 12px 0 0;
}
.custom-quill-editor .ql-container.ql-snow {
  background: rgba(255, 255, 255, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0 0 12px 12px;
}
.custom-quill-editor .ql-snow .ql-picker-options {
  background: #1A1E30;
}
.custom-quill-editor .ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options {
  border: 1px solid rgba(255, 255, 255, 0.3);
}
.custom-quill-editor .ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label {
  border-color: rgba(255, 255, 255, 0.3);
}
.custom-quill-editor .ql-snow .ql-editor h1 {
  color: var(--td-white);
}
.custom-quill-editor .ql-snow .ql-editor h2 {
  color: var(--td-white);
}
.custom-quill-editor .ql-snow .ql-editor h3 {
  color: var(--td-white);
}
.custom-quill-editor .ql-snow .ql-editor h4 {
  color: var(--td-white);
}
.custom-quill-editor .ql-snow .ql-editor h5 {
  color: var(--td-white);
}
.custom-quill-editor .ql-snow .ql-editor h6 {
  color: var(--td-white);
}

.common-payment-form {
  padding: 20px;
  border-radius: 18px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: var(--td-card-bg-1);
  backdrop-filter: blur(5px);
}
@media (max-width: 767px) {
  .common-payment-form {
    padding: 16px;
  }
}
.common-payment-form .withdraw-button {
  margin-top: 20px;
}
@media (max-width: 767px) {
  .common-payment-form .withdraw-button {
    margin-top: 16px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .common-payment-form .withdraw-button {
    margin-top: 20px;
  }
}
.common-payment-form .all-payment-method-here .single-payment-method {
  padding: 16px;
  border-radius: 18px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.04);
  backdrop-filter: blur(5px);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 10px;
  position: relative;
}
.common-payment-form .all-payment-method-here .single-payment-method .img {
  width: 100px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.common-payment-form .all-payment-method-here .single-payment-method .img img {
  width: 100%;
}
.common-payment-form .all-payment-method-here .single-payment-method .edit-and-cross {
  position: absolute;
  top: 10px;
  inset-inline-end: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
}
.common-payment-form-exceptional {
  padding: 0px;
  border-radius: 0px;
  border: none;
  background: none;
  backdrop-filter: none;
}

/*----------------------------------------
	Image Preview 
-----------------------------------------*/
.file-upload-wrap .top-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.file-upload-wrap .input-label {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 5px;
  color: #151415;
}
.file-upload-wrap #uploadItems {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.upload-custom-file {
  position: relative;
  display: inline-block;
  width: 100%;
  height: 155px;
  text-align: center;
}
.upload-custom-file input[type=file] {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  width: 2px;
  height: 2px;
  overflow: hidden;
  opacity: 0;
}
.upload-custom-file label {
  z-index: 1;
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  bottom: 0;
  inset-inline-end: 0;
  width: 100%;
  overflow: hidden;
  cursor: pointer;
  border-radius: 8px;
  transition: transform 0.4s;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  -webkit-transition: -webkit-transform 0.4s;
  -moz-transition: -moz-transform 0.4s;
  -ms-transition: -ms-transform 0.4s;
  -o-transition: -o-transform 0.4s;
  transition: transform 0.4s;
  background: rgba(255, 255, 255, 0.04);
  border: 1px solid rgba(21, 20, 21, 0.16);
  border-radius: 0px;
}
.upload-custom-file label span {
  display: block;
  color: var(--td-text-primary);
  font-size: 14px;
  font-weight: 500;
  -webkit-transition: color 0.4s;
  -moz-transition: color 0.4s;
  -ms-transition: color 0.4s;
  -o-transition: color 0.4s;
  transition: color 0.4s;
}
.upload-custom-file label span b {
  color: var(--td-text-primary);
  font-weight: 500;
}
.upload-custom-file label .type-file-text {
  margin-top: 5px;
  color: #E94E5B;
}
.upload-custom-file label .upload-icon {
  width: 40px;
  margin: 0 auto;
  margin-bottom: 4px;
}
.upload-custom-file label.file-ok {
  background-repeat: no-repeat;
  background-position: center center;
  background-size: contain;
  border-color: var(--td-primary);
}
.upload-custom-file label.file-ok span {
  position: absolute;
  bottom: 0;
  inset-inline-start: 0;
  width: 100%;
  padding: 0.3rem;
  color: #ffffff;
  background-color: var(--td-primary);
  font-weight: 500;
  font-size: 14px;
  margin: auto;
  text-decoration: none;
}
.upload-custom-file label.file-ok .upload-icon {
  display: none;
}
.upload-custom-file.without-image {
  height: 167px;
}
.upload-custom-file.without-image label {
  background-color: var(--td-text-primary);
}

.upload-thumb-close {
  position: absolute;
  inset-inline-end: 10px;
  top: 35px;
  z-index: 5;
  color: #E94E5B;
  display: none;
}

.file-upload-close,
.input-file-close {
  position: absolute;
  top: 8px;
  inset-inline-end: 8px;
  color: #F34141;
  font-size: 18px;
  z-index: 55;
}

.custom-input-label {
  color: rgba(8, 8, 8, 0.8);
  font-size: 14px;
  font-weight: 500;
  line-height: 24px;
  margin-bottom: 4px;
}
.custom-input-label .uit--calender {
  display: inline-block;
  width: 14px;
  height: 14px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M19.5 4h-3V2.5a.5.5 0 0 0-1 0V4h-7V2.5a.5.5 0 0 0-1 0V4h-3A2.503 2.503 0 0 0 2 6.5v13A2.503 2.503 0 0 0 4.5 22h15a2.5 2.5 0 0 0 2.5-2.5v-13A2.5 2.5 0 0 0 19.5 4M21 19.5a1.5 1.5 0 0 1-1.5 1.5h-15A1.5 1.5 0 0 1 3 19.5V11h18zm0-9.5H3V6.5C3 5.672 3.67 5 4.5 5h3v1.5a.5.5 0 0 0 1 0V5h7v1.5a.5.5 0 0 0 1 0V5h3A1.5 1.5 0 0 1 21 6.5z'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
  color: rgba(8, 8, 8, 0.8);
  transform: translateY(1px);
}
.custom-input-label .system-uicons--door-alt {
  display: inline-block;
  width: 14px;
  height: 14px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 21 21'%3E%3Cg fill='none' fill-rule='evenodd' transform='translate(4 1)'%3E%3Cpath stroke='%23000' stroke-linecap='round' stroke-linejoin='round' d='M2.5 2.5h2v14h-2a2 2 0 0 1-2-2v-10a2 2 0 0 1 2-2M7.202.513l4 1.5A2 2 0 0 1 12.5 3.886v11.228a2 2 0 0 1-1.298 1.873l-4 1.5A2 2 0 0 1 4.5 16.614V2.386A2 2 0 0 1 7.202.513' stroke-width='1'/%3E%3Ccircle cx='6.5' cy='9.5' r='1' fill='%23000'/%3E%3C/g%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
  color: rgba(8, 8, 8, 0.8);
  transform: translateY(1px);
}
.custom-input-label .pepicons-pencil--persons {
  display: inline-block;
  width: 14px;
  height: 14px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3E%3Cg fill='%23000' fill-rule='evenodd' clip-rule='evenodd'%3E%3Cpath d='M3.36 9.977a5.5 5.5 0 0 0-.923 3.05V14a.5.5 0 1 1-1 0v-.972A6.5 6.5 0 0 1 2.53 9.422l.108-.162a.5.5 0 1 1 .832.555z'/%3E%3Cpath d='M6.18 8.365c-1.09 0-2.107.544-2.711 1.45l-.832-.554a4.26 4.26 0 0 1 3.542-1.896h.22a.5.5 0 0 1 0 1zm3.078 1.6c.47.706.721 1.534.721 2.382h1a5.3 5.3 0 0 0-.889-2.936l-.1-.15a.5.5 0 1 0-.832.554z'/%3E%3Cpath d='M6.448 8.365c1.089 0 2.106.544 2.71 1.45l.832-.554a4.26 4.26 0 0 0-3.542-1.896h-.22a.5.5 0 1 0 0 1z'/%3E%3Cpath d='M6.25 7.25a2.25 2.25 0 1 0 0-4.5a2.25 2.25 0 0 0 0 4.5m0 1a3.25 3.25 0 1 0 0-6.5a3.25 3.25 0 0 0 0 6.5m4.259 4.936a5.5 5.5 0 0 0-.924 3.051v1.034a.5.5 0 1 1-1 0v-1.034a6.5 6.5 0 0 1 1.091-3.605l.133-.2a.5.5 0 0 1 .832.556z'/%3E%3Cpath d='M13.42 11.5a3.34 3.34 0 0 0-2.78 1.488l-.831-.555A4.34 4.34 0 0 1 13.42 10.5h.224a.5.5 0 1 1 0 1zm3.187 1.686a5.5 5.5 0 0 1 .924 3.051v1.034a.5.5 0 1 0 1 0v-1.034a6.5 6.5 0 0 0-1.092-3.605l-.133-.2a.5.5 0 1 0-.832.556z'/%3E%3Cpath d='M13.695 11.5a3.34 3.34 0 0 1 2.78 1.488l.832-.555a4.34 4.34 0 0 0-3.612-1.933h-.225a.5.5 0 1 0 0 1z'/%3E%3Cpath d='M13.5 10.5a2.25 2.25 0 1 0 0-4.5a2.25 2.25 0 0 0 0 4.5m0 1a3.25 3.25 0 1 0 0-6.5a3.25 3.25 0 0 0 0 6.5'/%3E%3C/g%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
  color: rgba(8, 8, 8, 0.8);
  transform: translateY(1px);
}
.custom-input-label-2 {
  color: white;
}
.custom-input-label-2 .uit--calender {
  color: white;
}
.custom-input-label-2 .system-uicons--door-alt {
  color: white;
}
.custom-input-label-2 .pepicons-pencil--persons {
  color: white;
}

.custom-range-calender {
  position: relative;
}
.custom-range-calender .icon {
  position: absolute;
  inset-inline-start: 15px;
  top: 50%;
  transform: translateY(-50%);
}
.custom-range-calender input[type=text] {
  height: 56px;
  background-color: #F2F3F5;
  border-radius: 12px;
  border: none;
  padding: 0 15px 0 40px;
  font-size: 16px;
}
[dir=rtl] .custom-range-calender input[type=text] {
  padding: 0 40px 0 15px;
}

/*----------------------------------------*/
/*  2.6 headings
/*----------------------------------------*/
.common-title-head {
  background: rgba(170, 132, 83, 0.3);
  backdrop-filter: blur(1px);
  display: inline-flex;
  height: 36px;
  padding: 10px 16px;
  align-items: center;
  gap: 6px;
  position: relative;
  margin-bottom: 10px;
}
@media (max-width: 767px) {
  .common-title-head {
    padding: 4px 8px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .common-title-head {
    padding: 8px 12px;
  }
}
.common-title-head p {
  color: var(--td-white);
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  text-transform: uppercase;
  display: flex;
  align-items: center;
  gap: 6px;
}
@media (max-width: 767px) {
  .common-title-head p {
    font-size: 10px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .common-title-head p {
    font-size: 12px;
  }
}
.common-title-head p::before {
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url("../images/icon/text-left-img.svg");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}
.common-title-head p::after {
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url("../images/icon/text-right-img.svg");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}
.common-title-head-2 {
  background: rgba(170, 132, 83, 0.16);
  backdrop-filter: blur(1px);
}
.common-title-head-2 p {
  color: var(--td-primary);
}
.common-title-head-2 p::before {
  background-image: url("../images/icon/text-left-img-primary.svg");
}
.common-title-head-2 p::after {
  background-image: url("../images/icon/text-right-img-primary.svg");
}

.common-section-title {
  display: flex;
  justify-content: space-between;
  align-items: end;
}
@media (max-width: 767px) {
  .common-section-title {
    flex-direction: column;
    align-items: start;
    justify-content: start;
  }
}
.common-section-title .left {
  width: 45%;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .common-section-title .left {
    width: 60%;
  }
}
@media (max-width: 767px) {
  .common-section-title .left {
    width: 100%;
  }
}
.common-section-title .left .section-title {
  color: var(--td-heading);
  font-size: 60px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 16px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .common-section-title .left .section-title {
    font-size: 55px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .common-section-title .left .section-title {
    font-size: 45px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .common-section-title .left .section-title {
    font-size: 35px;
  }
}
@media (max-width: 767px) {
  .common-section-title .left .section-title {
    font-size: 26px;
    margin-bottom: 10px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .common-section-title .left .section-title {
    font-size: 30px;
    margin-bottom: 10px;
  }
}
.common-section-title .left .section-title .highlight-text {
  display: inline-block;
  position: relative;
}
.common-section-title .left .section-title .highlight-text .highlight-text-img {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-end: -60px;
  width: 275px;
  height: 85px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .common-section-title .left .section-title .highlight-text .highlight-text-img {
    width: 216px;
    height: 68px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .common-section-title .left .section-title .highlight-text .highlight-text-img {
    width: 185px;
    height: 55px;
  }
}
@media (max-width: 767px) {
  .common-section-title .left .section-title .highlight-text .highlight-text-img {
    width: 100px;
    height: 55px;
    inset-inline-end: -15px;
    top: 10%;
    transform: translateY(-10%);
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .common-section-title .left .section-title .highlight-text .highlight-text-img {
    width: 125px;
    height: 55px;
    inset-inline-end: -20px;
  }
}
.common-section-title .left .section-title .highlight-text .highlight-text-img-2 {
  width: 206px;
  height: 63px;
  inset-inline-end: -40px;
  top: 0%;
  transform: translateY(0%);
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .common-section-title .left .section-title .highlight-text .highlight-text-img-2 {
    width: 160px;
    height: 68px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .common-section-title .left .section-title .highlight-text .highlight-text-img-2 {
    width: 135px;
    height: 55px;
  }
}
@media (max-width: 767px) {
  .common-section-title .left .section-title .highlight-text .highlight-text-img-2 {
    width: 100px;
    height: 55px;
    inset-inline-end: -20px;
  }
}
.common-section-title .left .section-title .highlight-text .highlight-text-img-3 {
  width: 180px;
  height: 70px;
  inset-inline-end: -30px;
  top: 0%;
  transform: translateY(0%);
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .common-section-title .left .section-title .highlight-text .highlight-text-img-3 {
    width: 150px;
    height: 68px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .common-section-title .left .section-title .highlight-text .highlight-text-img-3 {
    width: 115px;
    height: 55px;
  }
}
@media (max-width: 767px) {
  .common-section-title .left .section-title .highlight-text .highlight-text-img-3 {
    width: 90px;
    height: 55px;
    inset-inline-end: -20px;
  }
}
.common-section-title .left .section-description {
  color: var(--td-text-primary);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.625;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .common-section-title .left .section-description {
    font-size: 14px;
  }
}
@media (max-width: 767px) {
  .common-section-title .right {
    margin-top: 10px;
  }
}
.common-section-title-2 .left {
  width: 100%;
}
.common-section-title-2 .left .section-title {
  width: 50%;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .common-section-title-2 .left .section-title {
    width: 60%;
  }
}
@media (max-width: 767px) {
  .common-section-title-2 .left .section-title {
    width: 100%;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .common-section-title-2 .left .section-title {
    width: 60%;
  }
}
.common-section-title-2 .left .left-action-btn {
  margin-top: 40px;
}
@media (max-width: 767px) {
  .common-section-title-2 .left .left-action-btn {
    margin-top: 20px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .common-section-title-2 .left .left-action-btn {
    margin-top: 30px;
  }
}
.common-section-title-3 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.common-section-title-3 .left {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 60%;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .common-section-title-3 .left {
    width: 80%;
  }
}
@media (max-width: 767px) {
  .common-section-title-3 .left {
    width: 100%;
  }
}
.common-section-title-3 .left .section-title {
  text-align: center;
}
.common-section-title-3 .left .section-title .highlight-text .highlight-text-img {
  inset-inline-end: -20px;
}
.common-section-title-3 .left .section-description {
  text-align: center;
}
.common-section-title-4 .left {
  width: 42%;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .common-section-title-4 .left {
    width: 80%;
  }
}
@media (max-width: 767px) {
  .common-section-title-4 .left {
    width: 100%;
  }
}
.common-section-title-4 .left .section-title {
  text-align: center;
}
.common-section-title-4 .left .section-title .highlight-text .highlight-text-img {
  inset-inline-end: -20px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .common-section-title-4 .left .section-title .highlight-text .highlight-text-img {
    inset-inline-end: 0px;
  }
}
@media (max-width: 767px) {
  .common-section-title-4 .left .section-title .highlight-text .highlight-text-img {
    inset-inline-end: 0px;
  }
}

.hero-title {
  color: var(--td-white);
  text-align: center;
  font-size: 80px;
  font-weight: 400;
  line-height: 1.2;
  margin-bottom: 16px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .hero-title {
    font-size: 70px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .hero-title {
    font-size: 60px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero-title {
    font-size: 50px;
  }
}
@media (max-width: 767px) {
  .hero-title {
    font-size: 30px;
    margin-bottom: 10px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .hero-title {
    font-size: 40px;
    margin-bottom: 10px;
  }
}

.hero-subtitle {
  color: #FFF4E5;
  text-align: center;
  font-size: 20px;
  font-weight: 300;
  line-height: 1.5;
  padding: 0 30px;
  margin-bottom: 40px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero-subtitle {
    font-size: 18px;
  }
}
@media (max-width: 767px) {
  .hero-subtitle {
    font-size: 14px;
    padding: 0 0px;
    margin-bottom: 20px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .hero-subtitle {
    font-size: 16px;
    padding: 0 0px;
    margin-bottom: 30px;
  }
}

.common-page-header {
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
  padding: 32px 0;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .common-page-header {
    padding: 28px 0;
  }
}
@media (max-width: 767px) {
  .common-page-header {
    padding: 24px 0;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .common-page-header {
    padding: 26px 0;
  }
}
.common-page-header .page-header-overlay {
  background: rgba(170, 132, 83, 0.4);
  backdrop-filter: blur(4.5px);
  padding: 50px 0;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .common-page-header .page-header-overlay {
    padding: 45px 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .common-page-header .page-header-overlay {
    padding: 40px 0;
  }
}
@media (max-width: 767px) {
  .common-page-header .page-header-overlay {
    padding: 20px 0;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .common-page-header .page-header-overlay {
    padding: 30px 0;
  }
}
.common-page-header .page-header-overlay h5 {
  color: var(--td-white);
  text-align: center;
  font-size: 30px;
  font-weight: 400;
  line-height: normal;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .common-page-header .page-header-overlay h5 {
    font-size: 28px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .common-page-header .page-header-overlay h5 {
    font-size: 26px;
  }
}
@media (max-width: 767px) {
  .common-page-header .page-header-overlay h5 {
    font-size: 20px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .common-page-header .page-header-overlay h5 {
    font-size: 24px;
  }
}

/*----------------------------------------*/
/*  buttons
/*----------------------------------------*/
.primary-button {
  height: 40px;
  padding: 10px 24px;
  background: var(--td-primary);
  border: 1px solid var(--td-primary);
  color: var(--td-white);
  font-size: 0.875rem;
  font-weight: 400;
  line-height: normal;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease-in-out;
  font-family: var(--td-heading-font);
}
@media (max-width: 767px) {
  .primary-button {
    height: 34px;
    padding: 10px 20px;
  }
}
.primary-button:hover {
  background: transparent;
  border: 1px solid var(--td-primary);
  color: var(--td-heading);
}
.primary-button.white-hover:hover {
  background: transparent;
  border: 1px solid var(--td-white);
  color: var(--td-white);
}
.primary-button.primary-hover:hover {
  background: transparent;
  border: 1px solid var(--td-primary);
  color: var(--td-primary);
}
.primary-button.border-btn {
  background: transparent;
  border: 1px solid var(--td-primary);
  color: var(--td-heading);
}
.primary-button.border-btn:hover {
  background: var(--td-primary);
  border: 1px solid var(--td-primary);
  color: var(--td-white);
}
.primary-button.xl-btn {
  height: 50px;
  padding: 10px 26px;
  font-size: 1rem;
}
@media (max-width: 767px) {
  .primary-button.xl-btn {
    height: 40px;
    padding: 10px 24px;
  }
}
.primary-button.lg-btn {
  height: 52px;
  padding: 12px 32px;
  display: inline-flex;
}
@media (max-width: 767px) {
  .primary-button.lg-btn {
    height: 42px;
    padding: 10px 24px;
  }
}
.primary-button.xxl-btn {
  height: 54px;
  padding: 10px 26px;
  color: var(--td-white);
  text-align: center;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.primary-button.px {
  padding: 12px 20px;
}

.ternary-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--td-text-secondary);
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  transition: all 0.3s ease-in-out;
  font-family: var(--td-heading-font);
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .ternary-button {
    font-size: 16px;
  }
}
.ternary-button svg {
  transform: translateY(2px);
}
.ternary-button:hover {
  color: var(--td-primary);
}
.ternary-button:hover svg path {
  fill: var(--td-primary);
}

.underline-btn {
  font-weight: 500;
  position: relative;
  color: var(--td-primary);
}
.underline-btn::after {
  content: "";
  position: absolute;
  height: 1px;
  transition: 0.3s;
  inset-inline-start: auto;
  bottom: -2px;
  background: var(--td-primary);
  width: 0;
  inset-inline-end: 0;
}
.underline-btn:hover {
  color: var(--td-primary);
}
.underline-btn:hover::after {
  width: 100%;
  inset-inline-start: 0;
  inset-inline-end: auto;
}

/*----------------------------------------*/
/*  offcanvas
/*----------------------------------------*/
.htlib-offcanvas {
  position: fixed;
  z-index: 999;
  background: var(--td-white);
  backdrop-filter: blur(5px);
  width: 250px;
  inset-inline-start: 0;
  top: 0;
  padding: 20px 20px;
  height: 100vh;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-100%);
  transition: 0.3s ease-in-out;
}
[dir=rtl] .htlib-offcanvas {
  transform: translateX(100%);
}
.htlib-offcanvas-open {
  visibility: visible;
  opacity: 1;
  transform: translateX(0);
}
[dir=rtl] .htlib-offcanvas-open {
  inset-inline-end: 0;
  inset-inline-start: auto;
  transform: translateX(0%);
}
.htlib-offcanvas-wrapper {
  position: relative;
}
.htlib-offcanvas-wrapper .htlib-offcanvas-close {
  position: absolute;
  top: 0;
  inset-inline-end: 0;
}
.htlib-offcanvas-wrapper .htlib-offcanvas-navbars > ul {
  list-style-type: none;
}
.htlib-offcanvas-wrapper .htlib-offcanvas-navbars > ul > li {
  margin-bottom: 15px;
}
@media (max-width: 767px) {
  .htlib-offcanvas-wrapper .htlib-offcanvas-navbars > ul > li {
    margin-bottom: 8px;
  }
}
.htlib-offcanvas-wrapper .htlib-offcanvas-navbars > ul > li:last-child {
  margin-bottom: 0;
}
.htlib-offcanvas-wrapper .htlib-offcanvas-navbars > ul > li > a {
  color: var(--td-heading);
  font-size: 12px;
  font-weight: 500;
  line-height: 2;
  text-transform: uppercase;
  transition: all 0.3s ease-in-out;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.htlib-offcanvas-wrapper .htlib-offcanvas-navbars > ul > li > a:hover {
  color: var(--td-primary);
}
.htlib-offcanvas-wrapper .htlib-offcanvas-navbars > ul > li > a.active {
  position: relative;
  transition: all 0.3s ease-in-out;
}
.htlib-offcanvas-wrapper .htlib-offcanvas-navbars > ul > li > a.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  inset-inline-start: 0;
  width: 100%;
  height: 1px;
  background-color: var(--td-heading);
}
.htlib-offcanvas-wrapper .htlib-offcanvas-navbars > ul > li > a .down-icon {
  color: var(--td-heading);
  margin-inline-end: 5px;
  display: flex;
}
.htlib-offcanvas-wrapper .htlib-offcanvas-navbars > ul > li ul {
  list-style-type: none;
  margin-top: 8px;
  display: none;
}
.htlib-offcanvas-wrapper .htlib-offcanvas-navbars > ul > li ul li a {
  font-size: 14px;
  color: var(--td-text-primary);
}
.htlib-offcanvas-wrapper .header-top-content {
  margin-top: 20px;
}
.htlib-offcanvas-wrapper .header-top-content .header-top-right-card {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}
.htlib-offcanvas-wrapper .header-top-content .header-top-right-card:last-child {
  margin-bottom: 0;
}
.htlib-offcanvas-wrapper .header-top-content .header-top-right-card .icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 30px;
  height: 30px;
  border-radius: 5px;
  background-color: var(--td-primary);
  flex-shrink: 0;
}
.htlib-offcanvas-wrapper .header-top-content .header-top-right-card .icon .header-top-icon {
  color: var(--td-white);
  font-size: 16px;
}
.htlib-offcanvas-wrapper .header-top-content .header-top-right-card .text h6 {
  font-size: 14px;
  font-weight: 400;
  color: var(--td-heading);
  font-family: var(--td-body-font);
}
.htlib-offcanvas-wrapper .appointment-btn {
  margin-top: 26px;
}
.htlib-offcanvas-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}
.htlib-offcanvas-header .htlib-offcanvas-logo {
  display: inline-block;
  height: 24px;
}
.htlib-offcanvas-header .htlib-offcanvas-logo img {
  width: 100%;
  height: 100%;
}
.htlib-offcanvas-overlay {
  position: fixed;
  top: 0;
  inset-inline-start: 0;
  z-index: 998;
  width: 100%;
  height: 100%;
  visibility: hidden;
  opacity: 0;
  transition: 0.45s ease-in-out;
  background: rgba(24, 24, 24, 0.4);
}
.htlib-offcanvas-overlay-open {
  visibility: visible;
  opacity: 0.7;
}

.rotate {
  transform: rotate(180deg);
  transition: transform 0.3s ease-in-out;
}

.htlib-offcanvas-close .htlib-offcanvas-close-toggle {
  display: flex;
  justify-content: center;
  align-items: center;
  transform: translateY(1px);
}
.htlib-offcanvas-close .htlib-offcanvas-close-toggle .cancel-icon {
  font-size: 22px;
  color: var(--td-heading);
}

/*----------------------------------------*/
/*  filter
/*----------------------------------------*/
.common-filter-box {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20px;
  padding: 30px;
  background: var(--td-white);
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.1);
  position: relative;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .common-filter-box {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (max-width: 767px) {
  .common-filter-box {
    grid-template-columns: repeat(1, 1fr);
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .common-filter-box {
    grid-template-columns: repeat(2, 1fr);
  }
}
.common-filter-box .common-date-input-box {
  position: relative;
}
.common-filter-box .common-date-input-box .icon {
  position: absolute;
  inset-inline-end: 12px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.common-filter-box .common-date-input-box .icon .dropdown-icon {
  font-size: 20px;
  color: var(--td-heading);
}
.common-filter-box .common-date-input-box label {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-start: 12px;
  font-size: 14px;
  font-weight: 400;
  line-height: 100%;
  font-family: var(--td-heading-font);
  color: var(--td-heading);
  cursor: text;
}
.common-filter-box .common-date-input-box input[type=text],
.common-filter-box .common-date-input-box input[type=search],
.common-filter-box .common-date-input-box input[type=email],
.common-filter-box .common-date-input-box input[type=tel],
.common-filter-box .common-date-input-box input[type=number],
.common-filter-box .common-date-input-box input[type=password],
.common-filter-box .common-date-input-box textarea {
  outline: none;
  height: 54px;
  width: 100%;
  padding: 0 12px;
  border-radius: 0px;
  border: 1px solid rgba(21, 20, 21, 0.16);
  color: var(--td-heading);
  background: var(--td-white);
  font-size: 14px;
  font-weight: 400;
  line-height: 100%;
  font-family: var(--td-body-font);
  text-align: end;
  padding-inline-end: 35px;
}
.common-filter-box .common-date-input-box input[type=text]::placeholder,
.common-filter-box .common-date-input-box input[type=search]::placeholder,
.common-filter-box .common-date-input-box input[type=email]::placeholder,
.common-filter-box .common-date-input-box input[type=tel]::placeholder,
.common-filter-box .common-date-input-box input[type=number]::placeholder,
.common-filter-box .common-date-input-box input[type=password]::placeholder,
.common-filter-box .common-date-input-box textarea::placeholder {
  color: var(--td-heading);
  font-size: 14px;
  font-weight: 400;
  line-height: 100%;
  font-family: var(--td-body-font);
  text-align: end;
}
.common-filter-box .common-date-input-box input[type=text]:focus,
.common-filter-box .common-date-input-box input[type=search]:focus,
.common-filter-box .common-date-input-box input[type=email]:focus,
.common-filter-box .common-date-input-box input[type=tel]:focus,
.common-filter-box .common-date-input-box input[type=number]:focus,
.common-filter-box .common-date-input-box input[type=password]:focus,
.common-filter-box .common-date-input-box textarea:focus {
  border: 1px solid var(--td-primary);
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .common-filter-box .action-btn {
    grid-column: span 2;
  }
}
@media (max-width: 767px) {
  .common-filter-box .action-btn {
    grid-column: span 1;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .common-filter-box .action-btn {
    grid-column: span 2;
  }
}
.common-filter-box-2 {
  background: transparent;
  box-shadow: none;
  border: 1px solid rgba(21, 20, 21, 0.16);
}

/*----------------------------------------*/
/*  Custom dropdown
/*----------------------------------------*/
.custom-dropdown-box {
  position: relative;
}
.custom-dropdown-box .custom-dropdown-btn .input {
  height: 54px;
  width: 100%;
  padding: 0 12px;
  border-radius: 0px;
  border: 1px solid rgba(21, 20, 21, 0.16);
  color: var(--td-heading);
  background: var(--td-white);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.custom-dropdown-box .custom-dropdown-btn .input .left p {
  font-family: var(--td-heading-font);
  font-size: 14px;
  font-weight: 400;
  color: var(--td-heading);
}
.custom-dropdown-box .custom-dropdown-btn .input .left p span {
  color: #FF6A00;
  font-family: var(--td-body-font);
  font-size: 14px;
  font-weight: 500;
  line-height: 1.8571428571;
}
.custom-dropdown-box .custom-dropdown-btn .input .right {
  display: flex;
  align-items: center;
  gap: 10px;
}
.custom-dropdown-box .custom-dropdown-btn .input p {
  font-size: 14px;
}
.custom-dropdown-box .custom-dropdown-btn .input.open {
  border: 1px solid var(--td-primary);
}
.custom-dropdown-box .custom-dropdown-btn .icon {
  display: inline-flex;
  transition: all 0.3s ease-in-out;
}
.custom-dropdown-box .custom-dropdown-btn .icon .dropdown-icon {
  font-size: 20px;
  color: var(--td-heading);
}
.custom-dropdown-box .custom-dropdown-btn .icon.open {
  transform: rotate(180deg);
}
.custom-dropdown-box .custom-dropdown-content {
  position: absolute;
  top: 100%;
  margin-top: 5px;
  inset-inline-start: 0;
  width: 100%;
  padding: 18px;
  border: 1px solid #E5E5E5;
  background: var(--td-white);
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.1);
  visibility: hidden;
  opacity: 0;
  transform: translateY(-5px);
  transition: all 0.3s ease-in-out;
  z-index: 2;
}
.custom-dropdown-box .custom-dropdown-content.open {
  visibility: visible;
  opacity: 1;
  transform: translateY(0px);
}

.counter-card-box {
  margin-bottom: 20px;
}
.counter-card-box:last-child {
  margin-bottom: 0;
}

.counter-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.counter-card .counter-text h5 {
  font-family: var(--td-heading-font);
  color: var(--td-heading);
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.counter {
  display: flex;
  align-items: center;
  gap: 8px;
}
.counter .counter-btn {
  display: flex;
  justify-content: center;
  align-items: center;
}
.counter .counter-btn .count-iocn {
  font-size: 18px;
  color: var(--td-heading);
}
.counter .count {
  width: 30px;
  height: 30px;
  font-family: var(--td-heading-font);
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--td-heading);
  text-align: center;
  font-size: 1.125rem;
  font-weight: 400;
  line-height: 1.4285714286;
}

.common-counter {
  display: flex;
  padding: 7.5px 15px;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  border-radius: 40px;
  border: 1px solid rgba(8, 8, 8, 0.1);
  height: 35px;
}
.common-counter .title h5 {
  color: var(--td-heading);
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.4285714286;
}

.user-dropdown-full {
  position: relative;
}
.user-dropdown-full .user-dropdown-btn {
  display: flex;
  width: 40px;
  height: 40px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  background-color: var(--td-primary);
  cursor: pointer;
}
@media (max-width: 767px) {
  .user-dropdown-full .user-dropdown-btn {
    width: 34px;
    height: 34px;
  }
}
.user-dropdown-full .user-dropdown-btn .user-icon {
  font-size: 22px;
  color: var(--td-white);
}
@media (max-width: 767px) {
  .user-dropdown-full .user-dropdown-btn .user-icon {
    font-size: 18px;
  }
}
.user-dropdown-full .user-dropdown-content-2 {
  position: absolute;
  top: 100%;
  margin-top: 10px;
  inset-inline-end: 0;
  width: 235px;
  background: var(--td-white);
  visibility: hidden;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease-in-out;
  z-index: 10;
}
.user-dropdown-full .user-dropdown-content-2 .header {
  padding: 10px 18px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-bottom: 1px solid rgba(21, 20, 21, 0.16);
}
.user-dropdown-full .user-dropdown-content-2 .header .img {
  width: 31px;
  height: 31px;
}
.user-dropdown-full .user-dropdown-content-2 .header .img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.user-dropdown-full .user-dropdown-content-2 .header .content p {
  color: var(--td-heading);
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  line-height: 1.625;
}
.user-dropdown-full .user-dropdown-content-2 .user-link ul {
  list-style-type: none;
}
.user-dropdown-full .user-dropdown-content-2 .user-link ul li a {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--td-text-primary);
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
  padding: 10px 18px;
  transition: all 0.3s ease-in-out;
}
.user-dropdown-full .user-dropdown-content-2 .user-link ul li a .icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}
.user-dropdown-full .user-dropdown-content-2 .user-link ul li a .icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.user-dropdown-full .user-dropdown-content-2 .user-link ul li a:hover {
  background-color: #F2F2F2;
}
.user-dropdown-full .user-dropdown-content-2 .user-link ul li a.logout {
  background: rgba(233, 58, 45, 0.1);
  color: #E93A2D;
}
.user-dropdown-full .user-dropdown-content-2 .user-link ul li a.logout:hover {
  background: rgba(233, 58, 45, 0.2);
}
.user-dropdown-full .user-dropdown-content-2.active {
  visibility: visible;
  opacity: 1;
  transform: translateY(0px);
}

/*----------------------------------------*/
/*  Cards
/*----------------------------------------*/
.room-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.room-card .room-card-full {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.room-card .room-card-full .room-card-img {
  display: block;
  width: 100%;
  height: 230px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .room-card .room-card-full .room-card-img {
    height: 200px;
  }
}
.room-card .room-card-full .room-card-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.room-card .room-card-full .room-card-content {
  border: 1px solid rgba(21, 20, 21, 0.16);
  border-top: none;
  padding: 16px 20px 20px 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.room-card .room-card-full .room-card-content .rooms-amenities {
  display: flex;
  align-items: center;
  gap: 12px 16px;
  margin-bottom: 10px;
  flex-wrap: wrap;
}
.room-card .room-card-full .room-card-content .rooms-amenities .single-amenities {
  display: flex;
  align-items: center;
  gap: 4px;
}
.room-card .room-card-full .room-card-content .rooms-amenities .single-amenities .icon {
  width: 18px;
  height: 18px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.room-card .room-card-full .room-card-content .rooms-amenities .single-amenities .icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.room-card .room-card-full .room-card-content .rooms-amenities .single-amenities p {
  font-size: 14px;
  color: var(--td-text-primary);
  line-height: normal;
  margin-bottom: 0;
  font-weight: 500;
}
.room-card .room-card-full .room-card-content .room-title {
  display: block;
  color: var(--td-heading);
  font-family: var(--td-heading-font);
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin: 4px 0px 10px 0px;
  transition: all 0.3s ease-in-out;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .room-card .room-card-full .room-card-content .room-title {
    font-size: 26px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .room-card .room-card-full .room-card-content .room-title {
    font-size: 24px;
  }
}
@media (max-width: 767px) {
  .room-card .room-card-full .room-card-content .room-title {
    font-size: 20px;
  }
}
.room-card .room-card-full .room-card-content .room-title:hover {
  color: var(--td-primary);
}
.room-card .room-card-full .room-card-content .room-description {
  color: rgba(68, 67, 68, 0.7);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.625;
}
@media (max-width: 767px) {
  .room-card .room-card-full .room-card-content .room-description {
    font-size: 14px;
  }
}
.room-card .room-card-full .room-card-content .separator {
  width: 78px;
  height: 2px;
  opacity: 0.2;
  background: linear-gradient(90deg, #151415 0%, #151415 49.52%, #151415 100%);
  margin-top: 20px;
  margin-bottom: 10px;
}
.room-card .room-card-full .room-card-content .price-and-action {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (max-width: 767px) {
  .room-card .room-card-full .room-card-content .price-and-action {
    flex-direction: column;
    align-items: start;
    justify-content: start;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .room-card .room-card-full .room-card-content .price-and-action {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
}
@media (max-width: 767px) {
  .room-card .room-card-full .room-card-content .price-and-action .price {
    margin-bottom: 10px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .room-card .room-card-full .room-card-content .price-and-action .price {
    margin-bottom: 0px;
  }
}
.room-card .room-card-full .room-card-content .price-and-action .price h4 {
  color: var(--td-primary);
  font-family: var(--td-body-font);
  font-size: 24px;
  font-weight: 400;
  line-height: normal;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .room-card .room-card-full .room-card-content .price-and-action .price h4 {
    font-size: 20px;
  }
}
@media (max-width: 767px) {
  .room-card .room-card-full .room-card-content .price-and-action .price h4 {
    font-size: 18px;
  }
}
.room-card .room-card-full .room-card-content .price-and-action .price h4 span {
  color: var(--td-text-primary);
  font-family: var(--td-body-font);
  font-size: 16px;
  font-weight: 600;
  line-height: normal;
}
@media (max-width: 767px) {
  .room-card .room-card-full .room-card-content .price-and-action .price h4 span {
    font-size: 14px;
    font-weight: 400;
  }
}
.room-card .room-card-full .room-card-content .price-and-action .price h4 del.old-price {
  color: #A7A7A7;
  font-size: 20px;
  margin-inline-end: 3px;
}

.amenities-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(170, 132, 83, 0.16);
  padding: 30px 20px;
  transition: all 0.3s ease-in-out;
  height: 100%;
  min-height: 195px;
}
@media (max-width: 767px) {
  .amenities-card {
    padding: 20px 10px;
    min-height: 165px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .amenities-card {
    padding: 30px 20px;
    min-height: 195px;
  }
}
.amenities-card .amenities-card-img {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 24px;
}
@media (max-width: 767px) {
  .amenities-card .amenities-card-img {
    width: 30px;
    height: 30px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .amenities-card .amenities-card-img {
    width: 40px;
    height: 40px;
  }
}
.amenities-card .amenities-card-content .amenities-title {
  color: var(--td-heading);
  text-align: center;
  font-family: var(--td-heading-font);
  font-size: 16px;
  font-weight: 400;
  line-height: normal;
}
.amenities-card:hover {
  background: rgba(170, 132, 83, 0.1);
}

.package-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.package-card .package-card-img {
  display: block;
  width: 100%;
  height: 477px;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px) {
  .package-card .package-card-img {
    height: 350px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .package-card .package-card-img {
    height: 300px;
  }
}
.package-card .package-card-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.package-card .package-card-content {
  border: 1px solid rgba(21, 20, 21, 0.16);
  border-top: none;
  padding: 16px 20px 20px 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.package-card .package-card-content .package-title {
  display: block;
  font-family: var(--td-heading-font);
  color: var(--td-heading);
  font-size: 24px;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 14px;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .package-card .package-card-content .package-title {
    font-size: 28px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .package-card .package-card-content .package-title {
    font-size: 26px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .package-card .package-card-content .package-title {
    font-size: 24px;
  }
}
@media (max-width: 767px) {
  .package-card .package-card-content .package-title {
    font-size: 22px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .package-card .package-card-content .package-title {
    font-size: 20px;
  }
}
.package-card .package-card-content .package-price {
  color: #444344;
  font-size: 24px;
  font-weight: 400;
  line-height: normal;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .package-card .package-card-content .package-price {
    font-size: 28px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .package-card .package-card-content .package-price {
    font-size: 26px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .package-card .package-card-content .package-price {
    font-size: 24px;
  }
}
@media (max-width: 767px) {
  .package-card .package-card-content .package-price {
    font-size: 22px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .package-card .package-card-content .package-price {
    font-size: 20px;
  }
}
.package-card .package-card-content .package-price .per {
  color: var(--td-heading);
  font-family: var(--td-body-font);
  font-size: 18px;
  font-weight: 400;
  line-height: normal;
}
@media (max-width: 767px) {
  .package-card .package-card-content .package-price .per {
    font-size: 14px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .package-card .package-card-content .package-price .per {
    font-size: 16px;
  }
}
.package-card .package-card-content .package-price .per-night {
  color: #A7A7A7;
  font-family: var(--td-body-font);
  font-size: 18px;
  font-weight: 400;
  line-height: normal;
  text-decoration-line: line-through;
}
@media (max-width: 767px) {
  .package-card .package-card-content .package-price .per-night {
    font-size: 14px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .package-card .package-card-content .package-price .per-night {
    font-size: 16px;
  }
}
.package-card .package-card-content .action-btn {
  margin-top: 23px;
}

.stats-extra-padding {
  padding-block-start: clamp(1.625rem, 6.5vw + 1rem, 5rem);
}

.stats {
  border: 1px solid rgba(170, 132, 83, 0.2);
  border-inline-start: none;
  border-inline-end: none;
}

.stats-card h2 {
  color: var(--td-primary);
  text-align: center;
  font-size: 60px;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .stats-card h2 {
    margin-bottom: 18px;
    font-size: 50px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .stats-card h2 {
    margin-bottom: 16px;
    font-size: 45px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .stats-card h2 {
    margin-bottom: 14px;
    font-size: 40px;
  }
}
@media (max-width: 767px) {
  .stats-card h2 {
    margin-bottom: 8px;
    font-size: 24px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .stats-card h2 {
    margin-bottom: 10px;
    font-size: 30px;
  }
}
.stats-card p {
  color: #444344;
  text-align: center;
  font-size: 24px;
  font-weight: 400;
  line-height: normal;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .stats-card p {
    font-size: 20px;
  }
}
@media (max-width: 767px) {
  .stats-card p {
    font-size: 16px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .stats-card p {
    font-size: 18px;
  }
}

.blog-card {
  height: 100%;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
}
.blog-card .blog-card-img {
  width: 100%;
  height: 452px;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-card .blog-card-img {
    height: 400px;
  }
}
@media (max-width: 767px) {
  .blog-card .blog-card-img {
    height: 300px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .blog-card .blog-card-img {
    height: 400px;
  }
}
.blog-card .blog-card-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.blog-card::before {
  content: "";
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, rgba(24, 59, 86, 0) 0%, rgba(22, 49, 70, 0.45) 56.25%, #152532 88%);
  z-index: 1;
}
.blog-card .blog-card-content {
  position: absolute;
  bottom: 0;
  inset-inline-start: 0;
  width: 100%;
  padding: 26px;
  z-index: 2;
  transition: all 0.3s ease-in-out;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .blog-card .blog-card-content {
    padding: 18px;
  }
}
.blog-card .blog-card-content .blog-title {
  display: inline-block;
  font-family: var(--td-heading-font);
  color: var(--td-white);
  font-size: 24px;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 10px;
  text-decoration-line: none;
  text-decoration-color: transparent;
  text-decoration-thickness: 1px;
  text-underline-offset: 3px;
  transition: text-decoration-color 0.3s ease-in-out, text-decoration-thickness 0.3s ease-in-out;
}
.blog-card .blog-card-content .blog-title:hover {
  text-decoration-line: underline;
  text-decoration-color: var(--td-white);
  text-decoration-thickness: 1px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-card .blog-card-content .blog-title {
    font-size: 20px;
  }
}
@media (max-width: 767px) {
  .blog-card .blog-card-content .blog-title {
    font-size: 18px;
  }
}
.blog-card .blog-card-content p {
  color: var(--td-text-secondary);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.375;
  transition: all 0.3s ease-in-out;
}
.blog-card .blog-card-content .date-action {
  position: absolute;
  bottom: 26px;
  inset-inline-start: 26px;
  width: calc(100% - 52px);
  display: flex;
  justify-content: space-between;
  align-items: center;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease-in-out;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .blog-card .blog-card-content .date-action {
    bottom: 18px;
    inset-inline-start: 18px;
    width: calc(100% - 36px);
  }
}
.blog-card .blog-card-content .date-action .date {
  color: var(--td-text-secondary);
  text-align: right;
  font-family: var(--td-body-font);
  font-size: 18px;
  font-weight: 400;
  line-height: normal;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .blog-card .blog-card-content .date-action .date {
    font-size: 16px;
  }
}
.blog-card:hover .blog-card-img {
  transform: scale(1.1);
}
.blog-card:hover .blog-card-content {
  padding-bottom: 60px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .blog-card:hover .blog-card-content {
    padding-bottom: 50px;
  }
}
.blog-card:hover .blog-card-content h3,
.blog-card:hover .blog-card-content p {
  transform: translateY(-5px);
}
.blog-card:hover .blog-card-content .date-action {
  opacity: 1;
  transform: translateY(0);
}

.product-gallery {
  position: relative;
}
.product-gallery .main-slider .slick-slide.slick-current.slick-active {
  position: relative;
}
.product-gallery .main-slider .slick-slide.slick-current.slick-active::before {
  content: "";
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%);
  z-index: 1;
}
.product-gallery .slider-container {
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
  position: relative;
}
.product-gallery .main-slider {
  margin-bottom: 0;
}
.product-gallery .main-slider img {
  width: 100%;
  height: auto;
  display: block;
}
.product-gallery .nav-slider {
  position: absolute;
  bottom: 20px;
  inset-inline-start: 20px;
  width: 60%;
  max-width: 380px;
  z-index: 5;
}
@media (max-width: 767px) {
  .product-gallery .nav-slider {
    bottom: 10px;
    inset-inline-start: 10px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .product-gallery .nav-slider {
    bottom: 20px;
    inset-inline-start: 20px;
  }
}
.product-gallery .nav-slider .slick-slide {
  cursor: pointer;
  opacity: 1;
  padding: 0 5px;
}
.product-gallery .nav-slider .slick-slide img {
  border: 2px solid #A9977B;
  height: 80px;
}
@media (max-width: 767px) {
  .product-gallery .nav-slider .slick-slide img {
    height: 40px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .product-gallery .nav-slider .slick-slide img {
    height: 80px;
  }
}
.product-gallery .nav-slider .slick-slide.slick-current {
  opacity: 1;
}
.product-gallery .nav-slider .slick-slide.slick-current img {
  border-color: #F0992A;
}
.product-gallery .slick-arrow {
  display: none;
}

.latest-news-card {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 30px;
}
.latest-news-card:last-child {
  margin-bottom: 0;
}
.latest-news-card .latest-news-card-img {
  width: 96px;
  height: 96px;
  flex-shrink: 0;
}
.latest-news-card .latest-news-card-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.latest-news-card .latest-news-card-content .latest-news-title {
  display: block;
  color: var(--td-heading);
  font-size: 18px;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 16px;
}
.latest-news-card .latest-news-card-content p {
  color: var(--td-text-primary);
  font-size: 16px;
  font-weight: 400;
  line-height: normal;
}

/*----------------------------------------*/
/*  Reservation
/*----------------------------------------*/
.reservation-box {
  padding: 30px;
  background: var(--td-white);
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.1);
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .reservation-box {
    padding: 20px;
  }
}
.reservation-box .title-price {
  display: flex;
  justify-content: space-between;
  align-items: start;
  gap: 10px;
  margin-bottom: 30px;
}
.reservation-box .title-price .left h5 {
  color: var(--td-heading);
  font-size: 30px;
  font-weight: 400;
  line-height: normal;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .reservation-box .title-price .left h5 {
    font-size: 28px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .reservation-box .title-price .left h5 {
    font-size: 26px;
  }
}
@media (max-width: 767px) {
  .reservation-box .title-price .left h5 {
    font-size: 24px;
  }
}
.reservation-box .title-price .right h4 {
  color: var(--td-heading);
  font-size: 30px;
  font-weight: 400;
  line-height: normal;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .reservation-box .title-price .right h4 {
    font-size: 28px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .reservation-box .title-price .right h4 {
    font-size: 26px;
  }
}
@media (max-width: 767px) {
  .reservation-box .title-price .right h4 {
    font-size: 20px;
  }
}
.reservation-box .title-price .right h4 .discount {
  color: var(--td-text-primary);
  font-size: 20px;
  font-weight: 400;
  line-height: normal;
  text-decoration-line: line-through;
}
@media (max-width: 767px) {
  .reservation-box .title-price .right h4 .discount {
    font-size: 18px;
  }
}
.reservation-box .title-price .right p {
  color: var(--td-text-primary);
  font-size: 14px;
  font-weight: 400;
  line-height: normal;
}
@media (max-width: 767px) {
  .reservation-box .date-range {
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
    --webkit-overflow-scrolling: touch;
  }
  .reservation-box .date-range .date-range-full {
    width: 100%;
    min-width: 340px;
    border-collapse: collapse;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .reservation-box .date-range {
    width: unset;
    overflow-x: unset;
    overflow-y: unset;
    --webkit-overflow-scrolling: unset;
  }
  .reservation-box .date-range .date-range-full {
    width: unset;
    min-width: unset;
    border-collapse: unset;
  }
}
.reservation-box .person-select {
  margin-top: 20px;
}
.reservation-box .extra-services {
  margin-top: 30px;
}
.reservation-box .extra-services h5 {
  color: var(--td-heading);
  font-size: 20px;
  font-weight: 400;
  line-height: normal;
}
.reservation-box .extra-services .checkboxes {
  margin-top: 16px;
}
.reservation-box .extra-services .checkboxes .checkbox {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}
.reservation-box .extra-services .checkboxes .checkbox:last-child {
  margin-bottom: 0;
}
.reservation-box .extra-services .checkboxes .checkbox .price h6 {
  color: var(--td-text-primary);
  font-family: var(--td-body-font);
  font-size: 16px;
  font-weight: 400;
  line-height: normal;
}
.reservation-box .extra-services .checkboxes .checkbox .animate-custom .cbx {
  -webkit-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
}
.reservation-box .extra-services .checkboxes .checkbox .animate-custom .cbx::before {
  display: none;
}
.reservation-box .extra-services .checkboxes .checkbox .animate-custom .cbx span {
  display: inline-block;
  vertical-align: middle;
}
.reservation-box .extra-services .checkboxes .checkbox .animate-custom .cbx span a {
  color: var(--td-primary);
}
.reservation-box .extra-services .checkboxes .checkbox .animate-custom .cbx span a:hover {
  color: var(--td-primary);
}
.reservation-box .extra-services .checkboxes .checkbox .animate-custom .cbx span:first-child {
  position: relative;
  width: 17px;
  height: 17px;
  -webkit-border-radius: 0px;
  -moz-border-radius: 0px;
  -o-border-radius: 0px;
  -ms-border-radius: 0px;
  border-radius: 0px;
  transform: scale(1);
  vertical-align: middle;
  border: 1px solid #b9b8c3;
  transition: all 0.2s ease;
}
.reservation-box .extra-services .checkboxes .checkbox .animate-custom .cbx span:first-child svg {
  position: absolute;
  z-index: 1;
  top: 3px;
  inset-inline-start: 1px;
  fill: none;
  stroke: var(--td-white);
  stroke-width: 1;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-dasharray: 16px;
  stroke-dashoffset: 16px;
  transition: all 0.3s ease;
  transition-delay: 0.1s;
  transform: translate3d(0, 0, 0);
}
.reservation-box .extra-services .checkboxes .checkbox .animate-custom .cbx span:first-child:before {
  content: "";
  width: 100%;
  height: 100%;
  background: var(--td-primary);
  display: block;
  transform: scale(0);
  opacity: 1;
  border-radius: 50%;
  transition-delay: 0.2s;
}
.reservation-box .extra-services .checkboxes .checkbox .animate-custom .cbx span:last-child {
  margin-inline-start: 5px;
  color: var(--td-text-primary);
  font-family: var(--td-body-font);
  font-size: 16px;
  font-weight: 400;
  line-height: normal;
}
.reservation-box .extra-services .checkboxes .checkbox .animate-custom .cbx span:last-child:after {
  content: "";
  position: absolute;
  top: 8px;
  inset-inline-start: 0;
  height: 1px;
  width: 100%;
  background: #b9b8c3;
  transform-origin: 0 0;
  transform: scaleX(0);
}
.reservation-box .extra-services .checkboxes .checkbox .animate-custom .cbx:hover span:first-child {
  border-color: var(--td-primary);
}
.reservation-box .extra-services .checkboxes .checkbox .animate-custom .inp-cbx:checked + .cbx span:first-child {
  border-color: var(--td-primary);
  background: var(--td-primary);
  animation: check-15 0.6s ease;
}
.reservation-box .extra-services .checkboxes .checkbox .animate-custom .inp-cbx:checked + .cbx span:first-child svg {
  stroke-dashoffset: 0;
}
.reservation-box .extra-services .checkboxes .checkbox .animate-custom .inp-cbx:checked + .cbx span:first-child:before {
  transform: scale(2.2);
  opacity: 0;
  transition: all 0.6s ease;
}
.reservation-box .extra-services .checkboxes .checkbox .animate-custom .inp-cbx:checked + .cbx span:last-child {
  transition: all 0.3s ease;
}
.reservation-box .extra-services .checkboxes .checkbox .animate-custom input[type=checkbox] ~ label::after {
  display: none;
}
.reservation-box .extra-services .checkboxes .checkbox .animate-custom input[type=checkbox] ~ label {
  padding-inline-start: 0;
}
.reservation-box .extra-services .checkboxes .checkbox .animate-custom-2 .cbx span:last-child {
  color: #6B7280;
}
@keyframes check-15 {
  50% {
    transform: scale(1.2);
  }
}
.reservation-box .details {
  margin-top: 30px;
}
.reservation-box .details h5 {
  color: var(--td-heading);
  font-size: 20px;
  font-weight: 400;
  line-height: normal;
}
.reservation-box .details .details-box {
  margin-top: 16px;
}
.reservation-box .details .details-box .single-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}
.reservation-box .details .details-box .single-details:not(:last-child) {
  border-top: 1px dashed rgba(21, 20, 21, 0.26);
}
.reservation-box .details .details-box .single-details .left p {
  color: var(--td-text-primary);
  font-family: var(--td-body-font);
  font-size: 16px;
  font-weight: 400;
  line-height: normal;
}
.reservation-box .details .details-box .single-details .left p span {
  color: var(--td-heading);
  font-size: 20px;
  font-weight: 500;
  line-height: normal;
}
.reservation-box .details .details-box .single-details .right p {
  color: var(--td-heading);
  font-family: var(--td-body-font);
  font-size: 16px;
  font-weight: 400;
  line-height: normal;
}
.reservation-box .details .details-box .single-details .right p span {
  color: var(--td-heading);
  font-size: 20px;
  font-weight: 500;
  line-height: normal;
}
.reservation-box .details .details-box .single-details:last-child {
  border-bottom: none;
  border-top: 1px solid rgba(21, 20, 21, 0.26);
  padding-top: 15px;
  padding-bottom: 0;
}
.reservation-box .reserve-action-btn {
  margin-top: 30px;
}

.common-product-design .product-details .banner-img {
  height: 521px;
  width: 100%;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .common-product-design .product-details .banner-img {
    height: 450px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .common-product-design .product-details .banner-img {
    height: 400px;
  }
}
@media (max-width: 767px) {
  .common-product-design .product-details .banner-img {
    height: 300px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .common-product-design .product-details .banner-img {
    height: 350px;
  }
}
.common-product-design .product-details .banner-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.common-product-design .product-details .product-details-box {
  margin-top: 50px;
}
.common-product-design .product-details .product-details-box h2 {
  color: var(--td-heading);
  font-size: 30px;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 5px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .common-product-design .product-details .product-details-box h2 {
    font-size: 28px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .common-product-design .product-details .product-details-box h2 {
    font-size: 26px;
  }
}
@media (max-width: 767px) {
  .common-product-design .product-details .product-details-box h2 {
    font-size: 24px;
  }
}
.common-product-design .product-details .product-details-box .rooms-amenities {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}
.common-product-design .product-details .product-details-box .rooms-amenities .single-amenities {
  display: flex;
  align-items: center;
  gap: 4px;
}
.common-product-design .product-details .product-details-box .rooms-amenities .single-amenities .icon {
  width: 18px;
  height: 18px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.common-product-design .product-details .product-details-box .rooms-amenities .single-amenities .icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.common-product-design .product-details .product-details-box .rooms-amenities .single-amenities p {
  font-size: 13px;
  color: var(--td-text-primary);
  line-height: normal;
  margin-bottom: 0;
}
.common-product-design .product-details .product-details-box .description {
  color: var(--td-text-primary);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.625;
  margin-top: 16px;
}
.common-product-design .services h5 {
  color: var(--td-heading);
  font-size: 24px;
  font-weight: 400;
  line-height: normal;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .common-product-design .services h5 {
    font-size: 22px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .common-product-design .services h5 {
    font-size: 20px;
  }
}
@media (max-width: 767px) {
  .common-product-design .services h5 {
    font-size: 18px;
  }
}
.common-product-design .services ul {
  list-style-type: none;
  margin-top: 16px;
}
.common-product-design .services ul li {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}
.common-product-design .services ul li:last-child {
  margin-bottom: 0;
}
.common-product-design .services ul li span {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 14px;
  height: 14px;
  background-color: var(--td-primary);
}
.common-product-design .services ul li span .check-icon {
  color: var(--td-white);
  font-size: 11px;
}
.common-product-design .services ul li p {
  color: var(--td-text-primary);
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
}
.common-product-design .section-saperate {
  margin: 50px 0;
  color: rgba(0, 0, 0, 0.4);
  border: 0;
  border-top: var(--bs-border-width) solid;
  opacity: 0.25;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .common-product-design .section-saperate {
    margin: 40px 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .common-product-design .section-saperate {
    margin: 30px 0;
  }
}
@media (max-width: 767px) {
  .common-product-design .section-saperate {
    margin: 20px 0;
  }
}
.common-product-design .amenities-box-full h5 {
  color: var(--td-heading);
  font-size: 24px;
  font-weight: 400;
  line-height: normal;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .common-product-design .amenities-box-full h5 {
    font-size: 22px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .common-product-design .amenities-box-full h5 {
    font-size: 20px;
  }
}
@media (max-width: 767px) {
  .common-product-design .amenities-box-full h5 {
    font-size: 18px;
  }
}
.common-product-design .amenities-box-full .amenities-box {
  margin-top: 16px;
  display: flex;
  flex-wrap: wrap;
}
.common-product-design .amenities-box-full .amenities-box .amenities {
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: transparent;
}
.common-product-design .amenities-box-full .amenities-box .amenities:not(:last-child) {
  margin-bottom: 16px;
}
.common-product-design .amenities-box-full .amenities-box .amenities .icon {
  display: flex;
  width: 24px;
  height: 24px;
  padding: 2px;
  justify-content: center;
  align-items: center;
}
@media (max-width: 767px) {
  .common-product-design .amenities-box-full .amenities-box .amenities .icon {
    width: 20px;
    height: 20px;
  }
}
.common-product-design .amenities-box-full .amenities-box .amenities .icon img {
  width: 100%;
  height: 100%;
}
.common-product-design .amenities-box-full .amenities-box .amenities .text p {
  color: var(--td-text-primary);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.625;
}
@media (max-width: 767px) {
  .common-product-design .amenities-box-full .amenities-box .amenities .text p {
    font-size: 14px;
  }
}

.date-range-input {
  position: relative;
}
.date-range-input label {
  position: absolute;
  top: 15px;
  inset-inline-start: 12px;
  color: var(--td-heading);
  font-family: var(--td-heading-font);
  font-size: 14px;
  font-weight: 400;
  line-height: 1.625;
}
.date-range-input .flatpickr-range {
  outline: none;
  height: 54px;
  width: 100%;
  padding: 0 12px;
  border-radius: 0px;
  border: 1px solid rgba(21, 20, 21, 0.16);
  color: var(--td-heading);
  background: transparent;
  font-size: 14px;
  text-align: end;
  color: var(--td-heading);
}
.date-range-input .flatpickr-range:focus {
  border: 1px solid rgba(21, 20, 21, 0.16);
  box-shadow: unset;
  opacity: 1;
}
.date-range-input .flatpickr-range::placeholder {
  color: var(--td-heading);
  font-size: 14px;
  font-weight: 400;
  line-height: 100%;
  text-align: end;
}

/*----------------------------------------*/
/*  pagination
/*----------------------------------------*/
.common-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
}
.common-pagination ul {
  display: flex;
  align-items: center;
  gap: 10px;
  list-style-type: none;
}
.common-pagination ul li a {
  font-family: var(--td-heading-font);
  display: flex;
  width: 40px;
  height: 40px;
  padding: 10px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border: 1px solid rgba(170, 132, 83, 0.3);
  color: var(--td-heading);
  text-align: center;
  font-size: 20px;
  font-weight: 400;
  line-height: normal;
}
.common-pagination ul li a:hover, .common-pagination ul li a.active {
  border: 1px solid var(--td-primary);
  background: #FFF4E5;
}
.common-pagination ul li a.navigation.disabled .arrow {
  opacity: 0.2;
  cursor: not-allowed;
}
.common-pagination ul li a.navigation:hover, .common-pagination ul li a.navigation.active {
  border-color: var(--td-primary);
  background: var(--td-primary);
}
.common-pagination ul li a.navigation:hover .arrow, .common-pagination ul li a.navigation.active .arrow {
  color: var(--td-white);
  opacity: 1;
}

/*----------------------------------------*/
/*  custom nice select
/*----------------------------------------*/
.custom-nice-select.has-w-full .nice-select {
  width: 100%;
}
.custom-nice-select.has-w-full .nice-select .list {
  width: 100%;
}
.custom-nice-select .nice-select {
  font-family: var(--td-heading-font);
  background-color: transparent;
  border-radius: 0px;
  border: 1px solid rgba(21, 20, 21, 0.16);
  height: 52px;
  line-height: 50px;
  font-size: 14px;
  font-weight: 400;
  padding-inline-start: 12px;
  padding-inline-end: 28px;
}
@media (max-width: 767px) {
  .custom-nice-select .nice-select {
    height: 45px;
    line-height: 42px;
  }
}
.custom-nice-select .nice-select:after {
  height: 7px;
  border-color: #151415;
  width: 7px;
}
.custom-nice-select .nice-select .list {
  border: 1px solid #E5E5E5;
  background: var(--td-white);
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.1);
  border-radius: 0px;
  inset-inline-start: auto;
  inset-inline-end: 0;
  z-index: 90;
}
.custom-nice-select .nice-select .option {
  padding-inline-start: 16px;
  padding-inline-end: 16px;
}
.custom-nice-select .nice-select .option:hover,
.custom-nice-select .nice-select .option.focus,
.custom-nice-select .nice-select .option.selected.focus {
  background-color: #F2F2F2;
}

/*----------------------------------------*/
/* List info
/*----------------------------------------*/
.list-info ul li {
  list-style: none;
  padding-inline-start: 16px;
  position: relative;
  color: #444344;
}
.list-info ul li::before {
  position: absolute;
  content: "";
  height: 5px;
  width: 5px;
  background: #444344;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-start: 0;
  border-radius: 50%;
}
.list-info ul li:not(:last-child) {
  margin-bottom: 13px;
}

/*----------------------------------------*/
/*  Dashboard table styles 
/*----------------------------------------*/
.custom-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--td-white);
  border: 1px solid rgba(21, 20, 21, 0.2);
}
.custom-table.booking-table {
  min-width: 1000px;
}
.custom-table thead tr th {
  background: #FFF4E5;
  padding: 12px 1rem;
  font-size: 16px;
  font-weight: 500;
  font-family: var(--td-heading-font);
}
.custom-table th {
  padding: 15px 0.9375rem;
  text-align: left;
  border-bottom: 1px solid rgba(21, 20, 21, 0.16);
}
.custom-table td {
  padding: 15px 1rem;
  text-align: left;
  border-bottom: 1px solid rgba(21, 20, 21, 0.16);
  font-size: 14px;
  font-weight: 500;
  color: #444344;
}
.action-buttons-wrap {
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-action-btn {
  display: flex;
  width: 34px;
  height: 34px;
  padding: 0px 11px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  background: #AA8453;
}
.table-action-btn .btn-icon {
  font-size: 18px;
  display: flex;
  align-items: center;
  color: var(--td-white);
}
.table-action-btn.btn-outline {
  background-color: transparent;
  border: 1px solid var(--td-primary);
}
.table-action-btn.btn-outline .btn-icon {
  color: var(--td-black);
}

/*----------------------------------------*/
/* Badge styles
/*----------------------------------------*/
.app-badge {
  justify-content: center;
  display: inline-flex;
  align-items: center;
  padding: 0.1875rem 0.875rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-weight: 600;
  min-width: 3.75rem;
}
.app-badge.badge-danger {
  background-color: #e94e5b;
  color: var(--td-white);
}
.app-badge.badge-success {
  background-color: #0bc355;
  color: var(--td-white);
}
.app-badge.badge-warning {
  background-color: #e9b722;
  color: var(--td-white);
}

/*----------------------------------------*/
/* Alert Styles
/*----------------------------------------*/
.td-alert-box {
  background: var(--td-black);
  padding: 0.75rem 1.25rem 0.75rem 1rem;
  z-index: 1;
  position: relative;
  transition: 0.3s;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  column-gap: 0.75rem;
  align-items: center;
  border: 0.0625rem solid transparent;
  width: 350px;
}
@media (max-width: 480px) {
  .td-alert-box {
    padding: 0.625rem 0.75rem 0.625rem;
    width: 300px;
  }
}
.td-alert-box .alert-content {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  column-gap: 1rem;
  flex-grow: 1;
}
@media (max-width: 767px) {
  .td-alert-box .alert-content {
    column-gap: 0.75rem;
  }
}
.td-alert-box .alert-content .alert-title {
  font-size: 18px;
  font-weight: 500;
  font-family: var(--td-body-font);
}
@media (max-width: 767px) {
  .td-alert-box .alert-content .alert-title {
    font-size: 0.875rem;
  }
}
.td-alert-box .alert-content .alert-message {
  font-size: 14px;
  position: relative;
  margin-top: 2px;
}
.td-alert-box .alert-icon {
  flex: 0 0 auto;
}
.td-alert-box .alert-icon svg {
  width: 3.125rem;
  height: 3.125rem;
}
@media (max-width: 767px) {
  .td-alert-box .alert-icon svg {
    width: 2.25rem;
    height: 2.25rem;
  }
}
.td-alert-box .close-btn {
  padding: 5px;
  position: absolute;
  inset-inline-end: 8px;
  top: 8px;
}
.td-alert-box.hidden {
  opacity: 0;
  transform: translateY(-50%, 1.25rem);
  pointer-events: none;
}
.td-alert-box.has-success {
  border-inline-start: 4px solid #0C9;
  background: #E6FAF5;
}
.td-alert-box.has-warning {
  border-inline-start: 4px solid #F2C94C;
  background: #FDF8E8;
}
.td-alert-box.has-info {
  border-inline-start: 4px solid #5458F7;
  background: #EEEEFE;
}
.td-alert-box.has-danger {
  border-inline-start: 4px solid #E93A2D;
  background: #FFE1DF;
}

.alert-show-status {
  position: fixed;
  top: 1rem;
  inset-inline-end: 1rem;
  z-index: 999;
}

/*----------------------------------------*/
/* cookies style
/*----------------------------------------*/
.caches-privacy {
  max-width: 1060px;
  position: fixed;
  bottom: 20px;
  inset-inline-start: 50%;
  transform: translateX(-50%);
  row-gap: 12px;
  column-gap: 12px;
  border: 1px solid #FFF4E5;
  background: #FFF4E5;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px 24px 20px 24px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 90%;
  z-index: 111;
  transition: 0.3s;
}
[dir=rtl] .caches-privacy {
  inset-inline-start: auto;
  inset-inline-end: 50%;
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px) {
  .caches-privacy {
    flex-direction: column;
    align-items: self-start;
  }
}
@media (max-width: 480px) {
  .caches-privacy {
    padding: 12px 16px 12px 16px;
  }
  .caches-privacy .caches-btns .td-btn {
    font-size: 12px;
  }
}
.caches-privacy .caches-contents {
  display: flex;
  align-items: center;
  gap: 14px;
}
@media (max-width: 480px) {
  .caches-privacy .caches-contents {
    flex-direction: column;
    align-items: self-start;
  }
}
.caches-privacy .caches-contents img {
  height: 35px;
  flex: 0 0 auto;
}
.caches-privacy .caches-contents .title {
  font-size: 16px;
  margin-bottom: 8px;
  color: var(--td-white);
  font-weight: 600;
}
@media (max-width: 767px) {
  .caches-privacy .caches-contents .title {
    font-size: 18px;
  }
}
.caches-privacy .caches-contents p {
  font-size: 16px;
  margin-bottom: 0;
}
.caches-privacy .caches-contents p a {
  color: #13A158;
}
.caches-privacy .caches-btns {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 0 0 auto;
  flex-wrap: wrap;
  gap: 12px;
}

/*----------------------------------------*/
/*  fix design
/*----------------------------------------*/
.seperate-div {
  border-color: rgba(0, 0, 0, 0.1) !important;
}

/*----------------------------------------*/
/* Auth styles
/*----------------------------------------*/
.auth-section .auth-left {
  width: 45%;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 1400px) and (max-width: 1599px) {
  .auth-section .auth-left {
    margin: 30px;
    margin-inline-end: 0;
  }
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .auth-section .auth-left {
    display: none;
  }
}
.auth-section .auth-right {
  margin: 30px;
  width: 55%;
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .auth-section .auth-right {
    width: 100%;
  }
}
.auth-section .auth-right .inner {
  position: relative;
  padding: 80px 30px;
  height: 100%;
  border: 1px solid #c4a76d;
  border-left: 0;
  display: grid;
  place-items: center;
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 1400px) and (max-width: 1599px) {
  .auth-section .auth-right .inner {
    border-left: 1px solid #c4a76d;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .auth-section .auth-right .inner {
    padding: 60px 30px;
  }
}
@media (max-width: 767px) {
  .auth-section .auth-right .inner {
    padding: 30px 30px;
  }
}
.auth-section .auth-right .inner::before {
  position: absolute;
  content: "";
  bottom: -1px;
  right: 100%;
  width: 100%;
  height: 1px;
  z-index: -1;
  background: #c4a76d;
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 1400px) and (max-width: 1599px) {
  .auth-section .auth-right .inner::before {
    display: none;
  }
}
.auth-section .auth-right .inner::after {
  position: absolute;
  content: "";
  top: 0px;
  inset-inline-start: 0;
  width: 1px;
  height: calc(100% + 13px);
  background: #c4a76d;
  transform: rotate(9deg);
  transform-origin: top center;
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 1400px) and (max-width: 1599px) {
  .auth-section .auth-right .inner::after {
    display: none;
  }
}

.auth-main-grid {
  display: flex;
  min-height: 100vh;
}

.auth-thumb {
  height: 100%;
  clip-path: polygon(0% 0%, 100% 0%, 81.633% 100%, 0% 100%, 0% 0%);
  position: relative;
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 1400px) and (max-width: 1599px) {
  .auth-thumb {
    clip-path: none;
  }
}
.auth-thumb::before {
  position: absolute;
  content: "";
  background-image: url(../images/auth/rectangle-shape-01.png);
  top: 50%;
  inset-inline-start: 50%;
  transform: translate(-50%, -50%);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: calc(100% - 60px);
  height: calc(100% - 60px);
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 1400px) and (max-width: 1599px) {
  .auth-thumb::before {
    display: none;
  }
}
.auth-thumb img {
  width: 100%;
  height: 100%;
}

.auth-form-box {
  width: 600px;
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .auth-form-box {
    width: 100%;
  }
}
.auth-form-box.is-signup {
  width: 730px;
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 1400px) and (max-width: 1599px) {
  .auth-form-box.is-signup {
    width: 100%;
  }
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .auth-form-box {
    width: 100%;
  }
}
.auth-form-box .auth-top-contents .auth-logo {
  margin-bottom: 40px;
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .auth-form-box .auth-top-contents .auth-logo {
    margin-bottom: 30px;
  }
}
.auth-form-box .auth-top-contents .auth-logo a {
  display: block;
}
.auth-form-box .auth-top-contents .auth-logo img {
  height: 40px;
}
.auth-form-box .auth-top-contents .intro-contents {
  margin-bottom: 45px;
}
.auth-form-box .auth-top-contents .intro-contents .title {
  font-size: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .auth-form-box .auth-top-contents .intro-contents .title {
    font-size: 26px;
  }
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px) {
  .auth-form-box .auth-top-contents .intro-contents .title {
    font-size: 24px;
  }
}
.auth-form-box .auth-top-contents .intro-contents .description {
  margin-top: 3px;
}
.auth-form-box .auth-login-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  flex-wrap: wrap;
  margin-top: 12px;
  margin-bottom: 35px;
}

.auth-bottom-contents {
  text-align: center;
}

.have-auth-account {
  margin-top: 12px;
}
.have-auth-account .description {
  font-size: 16px;
  font-weight: 500;
}

/* Header css*/
/*----------------------------------------*/
/*  3.1 Header-1
/*----------------------------------------*/
.header-top {
  background-color: var(--td-secondary);
  padding: 10px 0;
}
.header-top .header-top-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.header-top .header-top-content .left p {
  color: var(--td-white);
  font-size: 0.875rem;
  font-weight: 400;
  line-height: normal;
}
.header-top .header-top-content .right {
  display: flex;
  align-items: center;
  gap: 26px;
}
.header-top .header-top-content .right a {
  color: var(--td-white);
  font-size: 0.875rem;
  font-weight: 400;
  line-height: normal;
}
.header-top .header-top-content .right .language-dropdown .custom-nice-select .nice-select {
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  color: var(--td-white);
  border-radius: 0px;
  border: solid 1px rgba(232, 232, 232, 0);
  font-size: 14px;
  font-weight: normal;
  height: 20px;
  line-height: 19px;
  padding-inline-start: 0px;
  padding-inline-end: 18px;
}
.header-top .header-top-content .right .language-dropdown .custom-nice-select .nice-select:after {
  border-color: 1px solid var(--td-white);
  height: 7px;
  margin-top: -5px;
  inset-inline-end: 2px;
  width: 7px;
}
[dir=rtl] .header-top .header-top-content .right .language-dropdown .custom-nice-select .nice-select:after {
  inset-inline-start: 100%;
  width: 7px;
  inset-inline-end: auto;
  margin-inline-start: -9px;
}
.header-top .header-top-content .right .language-dropdown .custom-nice-select .nice-select .list {
  background-color: var(--td-white);
  border-radius: 0px;
  border: 1px solid #E5E5E5;
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.1);
  inset-inline-start: auto;
  inset-inline-end: 0;
  z-index: 90;
}
.header-top .header-top-content .right .language-dropdown .custom-nice-select .nice-select .option {
  cursor: pointer;
  font-weight: 400;
  line-height: 26px;
  list-style: none;
  min-height: 26px;
  outline: none;
  padding-inline-start: 14px;
  padding-inline-end: 14px;
  text-align: left;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
  color: var(--td-heading);
}
.header-top .header-top-content .right .language-dropdown .custom-nice-select .nice-select .option:hover,
.header-top .header-top-content .right .language-dropdown .custom-nice-select .nice-select .option.focus,
.header-top .header-top-content .right .language-dropdown .custom-nice-select .nice-select .option.selected.focus {
  background-color: #f6f6f6;
}
.header-top .header-top-content .right .language-dropdown .custom-nice-select .nice-select .option.selected {
  font-weight: normal;
}

.main-header {
  background: var(--td-bg-1);
  backdrop-filter: blur(18px);
  height: 80px;
  padding: 0 20px;
  position: relative;
  z-index: 80;
  display: flex;
  align-items: center;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .main-header {
    height: 70px;
    padding: 0 15px;
  }
}
@media (max-width: 767px) {
  .main-header {
    height: 60px;
  }
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .main-header {
    padding: 0px 0;
  }
}
.main-header .main-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.main-header .main-header-content .left .logo {
  display: inline-block;
  height: 40px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .main-header .main-header-content .left .logo {
    height: 36px;
  }
}
@media (max-width: 767px) {
  .main-header .main-header-content .left .logo {
    height: 30px;
  }
}
.main-header .main-header-content .left .logo img {
  height: 100%;
}
.main-header .main-header-content .main-menu ul {
  display: flex;
  align-items: center;
  gap: 30px;
  list-style-type: none;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .main-header .main-header-content .main-menu ul {
    gap: 22px;
  }
}
.main-header .main-header-content .main-menu ul li a {
  color: var(--td-heading);
  font-size: 1rem;
  font-weight: 400;
  line-height: normal;
  transition: all 0.3s ease-in-out;
}
.main-header .main-header-content .main-menu ul li a:hover, .main-header .main-header-content .main-menu ul li a.active {
  color: var(--td-primary);
}
.main-header .main-header-content .main-menu ul li.active a {
  color: var(--td-primary);
}
.main-header .main-header-content .right .btn-wrapper {
  display: flex;
  align-items: center;
  gap: 13px;
}
.main-header .main-header-content .right .btn-wrapper .toggle-btn .htlib-toggle-btn {
  display: flex;
  align-items: center;
}
.main-header .main-header-content .right .btn-wrapper .toggle-btn .htlib-toggle-btn .menu-icon {
  color: var(--td-heading);
  font-size: 24px;
}

.language-dropdown-header-bar .nice-select {
  background-color: transparent;
  border: solid 1px var(--td-primary);
  border-radius: 0px;
  height: 40px;
  line-height: 38px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .language-dropdown-header-bar .nice-select {
    height: 34px;
    line-height: 32px;
  }
}
.language-dropdown-header-bar .nice-select:after {
  border-bottom: 1px solid var(--td-heading);
  border-right: 1px solid var(--td-heading);
  height: 6px;
  width: 6px;
}
.language-dropdown-header-bar .nice-select .option:hover,
.language-dropdown-header-bar .nice-select .option.focus,
.language-dropdown-header-bar .nice-select .option.selected.focus {
  background-color: rgba(170, 132, 83, 0.2);
}
.language-dropdown-header-bar .nice-select .list {
  border-radius: 0px;
}
.language-dropdown-header-bar .nice-select .option.selected {
  font-weight: normal;
}

/*----------------------------------------*/
/*  hero
/*----------------------------------------*/
.hero {
  position: relative;
  width: 100%;
}
.hero .hero-main .hero-video {
  z-index: 1 !important;
}
.hero .hero-main .hero-video,
.hero .hero-main .hero-thumb {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}
.hero .hero-main video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}
.hero .hero-main .video-overlay {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.5) 100%);
  z-index: 1;
}
.hero .hero-main img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.hero .container {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 120px 20px 188px 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .hero .container {
    padding: 100px 20px 178px 20px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .hero .container {
    padding: 90px 20px 168px 20px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero .container {
    padding: 80px 20px 158px 20px;
  }
}
@media (max-width: 767px) {
  .hero .container {
    padding: 60px 20px 138px 20px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .hero .container {
    padding: 70px 20px 148px 20px;
  }
}
.hero .container .hero-content {
  max-width: 850px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}
.hero .container .hero-filter {
  position: absolute;
  bottom: 0;
  inset-inline-start: 0;
  width: 100%;
  z-index: 5;
  transform: translateY(55px);
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .hero .container .hero-filter {
    transform: translateY(95px);
  }
}
@media (max-width: 767px) {
  .hero .container .hero-filter {
    transform: translateY(320px);
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .hero .container .hero-filter {
    transform: translateY(155px);
  }
}

.no-video .hero-main video {
  display: none;
}
.no-video .hero-main img {
  display: block;
}

.extra-mt {
  margin-top: 55px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .extra-mt {
    margin-top: 95px;
  }
}
@media (max-width: 767px) {
  .extra-mt {
    margin-top: 320px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .extra-mt {
    margin-top: 155px;
  }
}

/*----------------------------------------*/
/*  About
/*----------------------------------------*/
.about-us {
  position: relative;
}
.about-us .about-us-content {
  position: relative;
  z-index: 2;
}
.about-us .about-us-content .left {
  margin-inline-end: 30px;
  position: relative;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .about-us .about-us-content .left {
    margin-inline-end: 10px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .about-us .about-us-content .left {
    margin-inline-end: 0px;
  }
}
.about-us .about-us-content .left .left-element {
  position: absolute;
  top: 0%;
  inset-inline-start: 0%;
  z-index: 1;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .about-us .about-us-content .left .left-element img {
    width: 450px;
  }
}
@media (max-width: 767px) {
  .about-us .about-us-content .left .left-element img {
    width: 350px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .about-us .about-us-content .left .left-element img {
    width: 450px;
  }
}
.about-us .about-us-content .left .all-img .left-img {
  display: flex;
  flex-direction: column;
  justify-content: end;
}
.about-us .about-us-content .left .all-img .left-img .short-img {
  width: 100%;
  height: 224px;
  position: relative;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .about-us .about-us-content .left .all-img .left-img .short-img {
    height: 180px;
  }
}
@media (max-width: 767px) {
  .about-us .about-us-content .left .all-img .left-img .short-img {
    height: 100px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .about-us .about-us-content .left .all-img .left-img .short-img {
    height: 180px;
  }
}
.about-us .about-us-content .left .all-img .left-img .short-img::before {
  content: "";
  position: absolute;
  top: 50%;
  inset-inline-start: 50%;
  transform: translate(-50%, -50%);
  width: 88%;
  height: 88%;
  background: rgba(0, 0, 0, 0);
  border: 1px solid rgba(255, 255, 255, 0.4);
  z-index: 1;
}
[dir=rtl] .about-us .about-us-content .left .all-img .left-img .short-img::before {
  inset-inline-start: auto;
  inset-inline-end: 50%;
}
.about-us .about-us-content .left .all-img .left-img .short-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.about-us .about-us-content .left .all-img .right-img .long-img {
  width: 100%;
  height: 588px;
  position: relative;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .about-us .about-us-content .left .all-img .right-img .long-img {
    height: 488px;
  }
}
@media (max-width: 767px) {
  .about-us .about-us-content .left .all-img .right-img .long-img {
    height: 288px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .about-us .about-us-content .left .all-img .right-img .long-img {
    height: 488px;
  }
}
.about-us .about-us-content .left .all-img .right-img .long-img::before {
  content: "";
  position: absolute;
  top: 50%;
  inset-inline-start: 50%;
  transform: translate(-50%, -50%);
  width: 88%;
  height: 94%;
  background: rgba(0, 0, 0, 0);
  border: 1px solid rgba(255, 255, 255, 0.4);
  z-index: 1;
}
[dir=rtl] .about-us .about-us-content .left .all-img .right-img .long-img::before {
  inset-inline-start: auto;
  inset-inline-end: 50%;
}
.about-us .about-us-content .left .all-img .right-img .long-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.about-us .about-element {
  position: absolute;
  inset-inline-end: 0%;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .about-us .about-element {
    display: none;
  }
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .about-us .about-element img {
    width: 400px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .about-us .about-element img {
    width: 300px;
  }
}

.about-contents-two {
  padding-inline-end: 50px;
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .about-contents-two {
    padding-inline-end: 0;
  }
}
.about-contents-two .heading-contents .title {
  font-size: 40px;
  margin-bottom: 1rem;
}
.about-contents-two .signature img {
  height: 60px;
}
.about-contents-two .btn-link {
  margin-top: 40px;
}

.about-thumb-wrapper-two {
  position: relative;
  margin-bottom: 145px;
}
.about-thumb-wrapper-two .about-thumb-one img {
  width: 338px;
}
.about-thumb-wrapper-two .about-thumb-two {
  position: absolute;
  top: 145px;
  inset-inline-end: 0;
}
.about-thumb-wrapper-two .about-thumb-two img {
  width: 338px;
}
.about-thumb-wrapper-two .border-shape {
  width: 338px;
  height: 303px;
  border: 4px solid #AA8453;
  position: absolute;
  inset-inline-end: 60px;
  top: 47px;
  z-index: -1;
}
@media (max-width: 480px) {
  .about-thumb-wrapper-two .border-shape {
    width: 300px;
    height: 300px;
    inset-inline-end: 20px;
  }
}

.about-two-shapes .shape-one {
  position: absolute;
  bottom: 9%;
  inset-inline-start: 48%;
  transform: translateX(-50%);
}
.about-two-shapes .shape-one img {
  width: 430px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .about-two-shapes .shape-one img {
    width: 260px;
  }
}

/*----------------------------------------*/
/*  Amenities
/*----------------------------------------*/
.amenities {
  background-color: var(--td-bg-1);
}

/*----------------------------------------*/
/*  packages
/*----------------------------------------*/
.our-packages .all-packages {
  padding: 0 63px;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .our-packages .all-packages {
    padding: 0 0px;
  }
}
.our-packages .all-packages .all-package-card:nth-child(odd) {
  transform: translateY(67px);
}
@media only screen and (min-width: 1400px) and (max-width: 1599px) {
  .our-packages .all-packages .all-package-card:nth-child(odd) {
    transform: translateY(55px);
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .our-packages .all-packages .all-package-card:nth-child(odd) {
    transform: translateY(45px);
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .our-packages .all-packages .all-package-card:nth-child(odd) {
    transform: translateY(0px);
  }
}
.our-packages .all-packages-2 {
  padding: 0 0px;
}
.our-packages .extra-transition-margin {
  margin-top: 67px;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px) {
  .our-packages .extra-transition-margin {
    margin-top: 55px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .our-packages .extra-transition-margin {
    margin-top: 45px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .our-packages .extra-transition-margin {
    margin-top: 0px;
  }
}

/*----------------------------------------*/
/*  gallery
/*----------------------------------------*/
.gallery {
  width: 100%;
}
.gallery .gallery-content {
  display: flex;
  gap: 30px;
  width: 100%;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .gallery .gallery-content {
    gap: 20px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .gallery .gallery-content {
    flex-direction: column;
  }
}
.gallery .gallery-content .left {
  width: 25%;
  position: relative;
  transition: all 0.3s ease-in-out;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .gallery .gallery-content .left {
    width: 100%;
  }
}
.gallery .gallery-content .left .left-img {
  width: 100%;
  height: 780px;
  position: relative;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .gallery .gallery-content .left .left-img {
    height: 700px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .gallery .gallery-content .left .left-img {
    height: 500px;
  }
}
@media (max-width: 767px) {
  .gallery .gallery-content .left .left-img {
    height: 300px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .gallery .gallery-content .left .left-img {
    height: 500px;
  }
}
.gallery .gallery-content .left .left-img::before {
  content: "";
  position: absolute;
  top: 50%;
  inset-inline-start: 50%;
  transform: translate(-50%, -50%);
  width: 88%;
  height: 94%;
  background: rgba(0, 0, 0, 0);
  border: 1px solid rgba(255, 255, 255, 0.4);
  z-index: 1;
}
[dir=rtl] .gallery .gallery-content .left .left-img::before {
  inset-inline-start: auto;
  inset-inline-end: 50%;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .gallery .gallery-content .left .left-img::before {
    width: 93%;
    height: 88%;
  }
}
.gallery .gallery-content .left .left-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.gallery .gallery-content .left .hotel-view {
  position: absolute;
  top: 50%;
  inset-inline-start: 50%;
  transform: translate(-50%, -50%) scale(0.8);
  z-index: 2;
  width: 77%;
  height: 88%;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  visibility: hidden;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
[dir=rtl] .gallery .gallery-content .left .hotel-view {
  inset-inline-start: auto;
  inset-inline-end: 50%;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .gallery .gallery-content .left .hotel-view {
    width: 87%;
    height: 78%;
  }
}
.gallery .gallery-content .left .hotel-view a {
  color: var(--td-white);
  text-align: center;
  font-family: var(--td-heading-font);
  font-size: 30px;
  font-weight: 400;
  line-height: normal;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .gallery .gallery-content .left .hotel-view a {
    font-size: 20px;
  }
}
@media (max-width: 767px) {
  .gallery .gallery-content .left .hotel-view a {
    font-size: 16px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .gallery .gallery-content .left .hotel-view a {
    font-size: 18px;
  }
}
.gallery .gallery-content .left:hover .hotel-view {
  visibility: visible;
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}
.gallery .gallery-content .middle {
  width: 40%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .gallery .gallery-content .middle {
    gap: 20px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .gallery .gallery-content .middle {
    width: 100%;
  }
}
.gallery .gallery-content .middle .image-middle {
  position: relative;
  transition: all 0.3s ease-in-out;
}
.gallery .gallery-content .middle .image-middle .square-img {
  width: 100%;
  height: 375px;
  position: relative;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .gallery .gallery-content .middle .image-middle .square-img {
    height: 340px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .gallery .gallery-content .middle .image-middle .square-img {
    height: 240px;
  }
}
@media (max-width: 767px) {
  .gallery .gallery-content .middle .image-middle .square-img {
    height: 150px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .gallery .gallery-content .middle .image-middle .square-img {
    height: 240px;
  }
}
.gallery .gallery-content .middle .image-middle .square-img::before {
  content: "";
  position: absolute;
  top: 50%;
  inset-inline-start: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  height: 90%;
  background: rgba(0, 0, 0, 0);
  border: 1px solid rgba(255, 255, 255, 0.4);
  z-index: 1;
}
[dir=rtl] .gallery .gallery-content .middle .image-middle .square-img::before {
  inset-inline-start: auto;
  inset-inline-end: 50%;
}
.gallery .gallery-content .middle .image-middle .square-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.gallery .gallery-content .middle .image-middle .hotel-view {
  position: absolute;
  top: 50%;
  inset-inline-start: 50%;
  transform: translate(-50%, -50%) scale(0.8);
  z-index: 2;
  width: 78%;
  height: 78%;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  visibility: hidden;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
[dir=rtl] .gallery .gallery-content .middle .image-middle .hotel-view {
  inset-inline-start: auto;
  inset-inline-end: 50%;
}
.gallery .gallery-content .middle .image-middle .hotel-view a {
  color: var(--td-white);
  text-align: center;
  font-family: var(--td-heading-font);
  font-size: 30px;
  font-weight: 400;
  line-height: normal;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .gallery .gallery-content .middle .image-middle .hotel-view a {
    font-size: 20px;
  }
}
@media (max-width: 767px) {
  .gallery .gallery-content .middle .image-middle .hotel-view a {
    font-size: 16px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .gallery .gallery-content .middle .image-middle .hotel-view a {
    font-size: 18px;
  }
}
.gallery .gallery-content .middle .image-middle:hover .hotel-view {
  visibility: visible;
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}
.gallery .gallery-content .right {
  width: 35%;
  position: relative;
  transition: all 0.3s ease-in-out;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .gallery .gallery-content .right {
    width: 100%;
  }
}
.gallery .gallery-content .right .right-img {
  width: 100%;
  height: 780px;
  position: relative;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .gallery .gallery-content .right .right-img {
    height: 700px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .gallery .gallery-content .right .right-img {
    height: 500px;
  }
}
@media (max-width: 767px) {
  .gallery .gallery-content .right .right-img {
    height: 300px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .gallery .gallery-content .right .right-img {
    height: 500px;
  }
}
.gallery .gallery-content .right .right-img::before {
  content: "";
  position: absolute;
  top: 50%;
  inset-inline-start: 50%;
  transform: translate(-50%, -50%);
  width: 92%;
  height: 94%;
  background: rgba(0, 0, 0, 0);
  border: 1px solid rgba(255, 255, 255, 0.4);
  z-index: 1;
}
[dir=rtl] .gallery .gallery-content .right .right-img::before {
  inset-inline-start: auto;
  inset-inline-end: 50%;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .gallery .gallery-content .right .right-img::before {
    width: 93%;
    height: 88%;
  }
}
.gallery .gallery-content .right .right-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.gallery .gallery-content .right .hotel-view {
  position: absolute;
  top: 50%;
  inset-inline-start: 50%;
  transform: translate(-50%, -50%) scale(0.8);
  z-index: 2;
  width: 83%;
  height: 87%;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  visibility: hidden;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .gallery .gallery-content .right .hotel-view {
    width: 87%;
    height: 78%;
  }
}
[dir=rtl] .gallery .gallery-content .right .hotel-view {
  inset-inline-start: auto;
  inset-inline-end: 50%;
}
.gallery .gallery-content .right .hotel-view a {
  color: var(--td-white);
  text-align: center;
  font-family: var(--td-heading-font);
  font-size: 30px;
  font-weight: 400;
  line-height: normal;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .gallery .gallery-content .right .hotel-view a {
    font-size: 20px;
  }
}
@media (max-width: 767px) {
  .gallery .gallery-content .right .hotel-view a {
    font-size: 16px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .gallery .gallery-content .right .hotel-view a {
    font-size: 18px;
  }
}
.gallery .gallery-content .right:hover .hotel-view {
  visibility: visible;
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

/*----------------------------------------*/
/*  blogs
/*----------------------------------------*/
.blog {
  position: relative;
  overflow: hidden;
}
.blog .blog-content {
  position: relative;
  z-index: 5;
}
.blog .blog-element {
  position: absolute;
  top: 30px;
  inset-inline-end: -260px;
  z-index: 1;
  width: 100%;
  height: 100%;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .blog .blog-element {
    display: none;
  }
}
.blog .blog-element img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/*----------------------------------------*/
/*  Subscribe
/*----------------------------------------*/
.subscribe-newsletter .subscribe-newsletter-content {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--td-bg-1);
  padding: 50px 0;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .subscribe-newsletter .subscribe-newsletter-content {
    padding: 40px 0;
  }
}
@media (max-width: 767px) {
  .subscribe-newsletter .subscribe-newsletter-content {
    padding: 30px 0;
  }
}
.subscribe-newsletter .subscribe-newsletter-content .subscribe-newsletter-content-full {
  width: 100%;
  max-width: 735px;
}
.subscribe-newsletter .subscribe-newsletter-content .subscribe-newsletter-content-full .section-title {
  color: var(--td-heading);
  text-align: center;
  font-size: 48px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.1875;
  letter-spacing: 0.48px;
  margin-bottom: 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .subscribe-newsletter .subscribe-newsletter-content .subscribe-newsletter-content-full .section-title {
    font-size: 40px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .subscribe-newsletter .subscribe-newsletter-content .subscribe-newsletter-content-full .section-title {
    font-size: 35px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .subscribe-newsletter .subscribe-newsletter-content .subscribe-newsletter-content-full .section-title {
    font-size: 30px;
    margin-bottom: 15px;
  }
}
@media (max-width: 767px) {
  .subscribe-newsletter .subscribe-newsletter-content .subscribe-newsletter-content-full .section-title {
    font-size: 26px;
    margin-bottom: 10px;
  }
}
.subscribe-newsletter .subscribe-newsletter-content .subscribe-newsletter-content-full .section-description {
  color: var(--td-text-primary);
  text-align: center;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.625;
  text-transform: capitalize;
  padding: 0 65px;
  margin-bottom: 40px;
}
@media (max-width: 767px) {
  .subscribe-newsletter .subscribe-newsletter-content .subscribe-newsletter-content-full .section-description {
    font-size: 14px;
    margin-bottom: 20px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .subscribe-newsletter .subscribe-newsletter-content .subscribe-newsletter-content-full .section-description {
    padding: 0 35px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .subscribe-newsletter .subscribe-newsletter-content .subscribe-newsletter-content-full .section-description {
    padding: 0 25px;
  }
}
@media (max-width: 767px) {
  .subscribe-newsletter .subscribe-newsletter-content .subscribe-newsletter-content-full .section-description {
    padding: 0 10px;
  }
}
.subscribe-newsletter .subscribe-newsletter-content .subscribe-newsletter-content-full .subscribe-form {
  max-width: 440px;
  margin: 0 auto;
  position: relative;
}
.subscribe-newsletter .subscribe-newsletter-content .subscribe-newsletter-content-full .subscribe-form .form-control {
  font-family: var(--td-heading-font);
  height: 52px;
  border-radius: 0px;
  border: none;
  background: transparent;
  color: var(--td-heading);
  font-size: 14px;
  font-weight: 400;
  line-height: 100%;
  padding-inline-start: 15px;
  padding-inline-end: 112px;
  border: 1px solid rgba(21, 20, 21, 0.16);
}
@media (max-width: 767px) {
  .subscribe-newsletter .subscribe-newsletter-content .subscribe-newsletter-content-full .subscribe-form .form-control {
    height: 40px;
  }
}
.subscribe-newsletter .subscribe-newsletter-content .subscribe-newsletter-content-full .subscribe-form .form-control::placeholder {
  color: var(--td-text-primary);
  font-size: 14px;
  font-weight: 400;
  line-height: 100%;
}
.subscribe-newsletter .subscribe-newsletter-content .subscribe-newsletter-content-full .subscribe-form .form-control:focus {
  border: 1px solid var(--td-primary);
}
.subscribe-newsletter .subscribe-newsletter-content .subscribe-newsletter-content-full .subscribe-form .subscribe-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-end: 8px;
}
.subscribe-newsletter .subscribe-newsletter-content .subscribe-newsletter-content-full .subscribe-form .subscribe-btn .sub-button {
  background-color: var(--td-primary);
  color: var(--td-white);
  display: inline-flex;
  height: 36px;
  padding: 7px 16px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  text-align: center;
  font-family: var(--td-heading-font) !important;
  font-size: 15px;
  font-weight: 400;
  line-height: normal;
  transition: all 0.3s ease-in-out;
}
@media (max-width: 767px) {
  .subscribe-newsletter .subscribe-newsletter-content .subscribe-newsletter-content-full .subscribe-form .subscribe-btn .sub-button {
    height: 28px;
    font-size: 13px;
  }
}
.subscribe-newsletter .subscribe-newsletter-content .subscribe-newsletter-content-full .subscribe-form .subscribe-btn .sub-button:hover {
  background-color: var(--td-heading);
}

/*----------------------------------------*/
/*  testimonial
/*----------------------------------------*/
.testimonial {
  background: var(--td-bg-1);
}
.testimonial .testimonial-box-full {
  display: flex;
  justify-content: center;
  align-items: center;
}
.testimonial .testimonial-box-full .testimonial-box {
  width: 100%;
  max-width: 1095px;
  border: 1px solid #aa8453;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(5px);
  padding: 55px 45px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 76px;
  overflow: hidden;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .testimonial .testimonial-box-full .testimonial-box {
    padding: 35px 25px;
    gap: 40px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .testimonial .testimonial-box-full .testimonial-box {
    padding: 25px 15px;
    gap: 30px;
  }
}
@media (max-width: 767px) {
  .testimonial .testimonial-box-full .testimonial-box {
    padding: 15px 5px;
    gap: 10px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .testimonial .testimonial-box-full .testimonial-box {
    padding: 25px 15px;
    gap: 30px;
  }
}
.testimonial .testimonial-box-full .testimonial-box .left .testimonial-btn,
.testimonial .testimonial-box-full .testimonial-box .right .testimonial-btn {
  display: inline-flex;
  height: 50px;
  width: 50px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
  background: var(--td-primary);
  border: 1px solid #aa8453;
  backdrop-filter: blur(4.5px);
}
@media (max-width: 767px) {
  .testimonial .testimonial-box-full .testimonial-box .left .testimonial-btn,
  .testimonial .testimonial-box-full .testimonial-box .right .testimonial-btn {
    height: 30px;
    width: 30px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .testimonial .testimonial-box-full .testimonial-box .left .testimonial-btn,
  .testimonial .testimonial-box-full .testimonial-box .right .testimonial-btn {
    height: 40px;
    width: 40px;
  }
}
.testimonial .testimonial-box-full .testimonial-box .left .testimonial-btn .arrow,
.testimonial .testimonial-box-full .testimonial-box .right .testimonial-btn .arrow {
  color: var(--td-white);
  font-size: 24px;
  transition: all 0.3s ease-in-out;
}
@media (max-width: 767px) {
  .testimonial .testimonial-box-full .testimonial-box .left .testimonial-btn .arrow,
  .testimonial .testimonial-box-full .testimonial-box .right .testimonial-btn .arrow {
    font-size: 18px;
  }
}
.testimonial .testimonial-box-full .testimonial-box .left .testimonial-btn.disabled,
.testimonial .testimonial-box-full .testimonial-box .right .testimonial-btn.disabled {
  border: 1px solid var(--td-primary);
  background: transparent;
}
.testimonial .testimonial-box-full .testimonial-box .left .testimonial-btn.disabled .arrow,
.testimonial .testimonial-box-full .testimonial-box .right .testimonial-btn.disabled .arrow {
  color: var(--td-heading);
}
.testimonial .testimonial-box-full .testimonial-box .middle {
  flex: 1;
  text-align: center;
  display: flex;
  flex-direction: column;
}
.testimonial .testimonial-box-full .testimonial-box .middle p {
  color: var(--td-white);
  font-size: 18px;
  font-weight: 400;
  line-height: 1.6666666667;
  margin: 0;
}
.testimonial .testimonial-box-full .testimonial-box .middle .main-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.testimonial .testimonial-box-full .testimonial-box .middle .main-content .user-reviews .user-review {
  display: none;
  height: 200px;
  overflow: hidden;
}
.testimonial .testimonial-box-full .testimonial-box .middle .main-content .user-reviews .active-always .user-review {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 200px;
  overflow: hidden;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .testimonial .testimonial-box-full .testimonial-box .middle .main-content .user-reviews .active-always .user-review {
    height: 250px;
  }
}
@media (max-width: 767px) {
  .testimonial .testimonial-box-full .testimonial-box .middle .main-content .user-reviews .active-always .user-review {
    height: fit-content;
    margin-bottom: 20px;
  }
}
.testimonial .testimonial-box-full .testimonial-box .middle .main-content .star {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}
.testimonial .testimonial-box-full .testimonial-box .middle .main-content .description p {
  color: var(--td-text-primary);
  text-align: center;
  font-size: 20px;
  font-weight: 400;
  line-height: 1.5;
}
@media (max-width: 767px) {
  .testimonial .testimonial-box-full .testimonial-box .middle .main-content .description p {
    font-size: 18px;
  }
}
.testimonial .testimonial-box-full .testimonial-box .middle .main-content .users-lists {
  display: flex;
  align-items: center;
  gap: 40px;
  width: 292px;
  overflow: hidden;
}
@media (max-width: 767px) {
  .testimonial .testimonial-box-full .testimonial-box .middle .main-content .users-lists {
    width: 193px;
    gap: 10px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .testimonial .testimonial-box-full .testimonial-box .middle .main-content .users-lists {
    width: 252px;
  }
}
.testimonial .testimonial-box-full .testimonial-box .middle .main-content .users-lists .users-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}
.testimonial .testimonial-box-full .testimonial-box .middle .main-content .users-lists .users-list .img {
  width: 60px;
  height: 60px;
  margin-bottom: 20px;
  transform: translateY(10px);
}
@media (max-width: 767px) {
  .testimonial .testimonial-box-full .testimonial-box .middle .main-content .users-lists .users-list .img {
    height: 50px;
    width: 50px;
    transform: translateY(2px);
  }
}
.testimonial .testimonial-box-full .testimonial-box .middle .main-content .users-lists .users-list .img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.testimonial .testimonial-box-full .testimonial-box .middle .main-content .users-lists .users-list .content h5 {
  color: var(--td-heading);
  text-align: center;
  font-size: 18px;
  font-weight: 400;
  line-height: normal;
  visibility: hidden;
  opacity: 0;
}
.testimonial .testimonial-box-full .testimonial-box .middle .main-content .users-lists .active-always .users-list .img {
  height: 92px;
  width: 92px;
  border: 3px solid var(--td-primary);
  border-radius: 50%;
  transform: translateY(0px);
}
@media (max-width: 767px) {
  .testimonial .testimonial-box-full .testimonial-box .middle .main-content .users-lists .active-always .users-list .img {
    height: 72px;
    width: 72px;
  }
}
.testimonial .testimonial-box-full .testimonial-box .middle .main-content .users-lists .active-always .users-list .content h5 {
  visibility: visible;
  opacity: 1;
}

/*----------------------------------------*/
/*  rooms
/*----------------------------------------*/
.all-rooms-area .title-dropdown-filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .all-rooms-area .title-dropdown-filter {
    flex-direction: column;
    align-items: start;
    gap: 20px;
  }
}
.all-rooms-area .title-dropdown-filter .left .title h2 {
  color: var(--td-heading);
  font-size: 40px;
  font-weight: 400;
  line-height: normal;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .all-rooms-area .title-dropdown-filter .left .title h2 {
    font-size: 35px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .all-rooms-area .title-dropdown-filter .left .title h2 {
    font-size: 30px;
  }
}
@media (max-width: 767px) {
  .all-rooms-area .title-dropdown-filter .left .title h2 {
    font-size: 25px;
  }
}
.all-rooms-area .title-dropdown-filter .right .common-nice-select-dropdown {
  display: flex;
  align-items: center;
  gap: 14px;
}
@media (max-width: 767px) {
  .all-rooms-area .title-dropdown-filter .right .common-nice-select-dropdown {
    flex-direction: column;
    align-items: start;
    gap: 10px;
  }
}

.room-details .room-details-content .left {
  margin-inline-end: 44px;
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .room-details .room-details-content .left {
    margin-inline-end: 0px;
  }
}
.room-details .room-details-content .left .blog-banner-img {
  height: 452px;
  width: 100%;
  overflow: hidden;
}
.room-details .room-details-content .left .blog-banner-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.room-details .room-details-content .left .date-author {
  display: flex;
  align-items: center;
  gap: 22px;
  margin-top: 20px;
}
.room-details .room-details-content .left .date-author .date p {
  color: var(--td-text-secondary);
  font-size: 18px;
  font-weight: 400;
  line-height: normal;
}
.room-details .room-details-content .left .date-author .date p span {
  font-weight: 500;
}
.room-details .room-details-content .left .date-author .author p {
  color: var(--td-text-secondary);
  font-size: 18px;
  font-weight: 400;
  line-height: normal;
}
.room-details .room-details-content .left .date-author .author p span {
  font-weight: 500;
}
.room-details .room-details-content .left .blog-title {
  color: var(--td-heading);
  font-size: 40px;
  font-weight: 400;
  line-height: normal;
  margin-top: 30px;
  margin-bottom: 16px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .room-details .room-details-content .left .blog-title {
    font-size: 32px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .room-details .room-details-content .left .blog-title {
    font-size: 28px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .room-details .room-details-content .left .blog-title {
    font-size: 26px;
  }
}
@media (max-width: 767px) {
  .room-details .room-details-content .left .blog-title {
    font-size: 24px;
  }
}
.room-details .room-details-content .left .description {
  color: var(--td-text-primary);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.625;
}
@media (max-width: 767px) {
  .room-details .room-details-content .left .description {
    font-size: 14px;
  }
}
.room-details .room-details-content .left .desctiption-box {
  margin-top: 48px;
}
.room-details .room-details-content .left .desctiption-box .description-box-content {
  margin-top: 30px;
}
.room-details .room-details-content .left .desctiption-box .description-box-content h4 {
  color: var(--td-heading);
  font-size: 20px;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 16px;
}
.room-details .room-details-content .left .desctiption-box .description-box-content p {
  color: var(--td-text-primary);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.625;
}
@media (max-width: 767px) {
  .room-details .room-details-content .left .desctiption-box .description-box-content p {
    font-size: 14px;
  }
}
.room-details .room-details-content .left .desctiption-box .description-box-banner {
  margin-top: 30px;
  height: 340px;
  width: 100%;
  overflow: hidden;
}
.room-details .room-details-content .left .desctiption-box .description-box-banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.room-details .room-details-content .left .blog-section-saperate {
  border-color: #151415;
  opacity: 0.1;
  border-width: 1px;
  margin: 50px 0 20px 0;
}
.room-details .room-details-content .left .share h5 {
  color: var(--td-heading);
  font-size: 24px;
  font-weight: 400;
  line-height: normal;
}
.room-details .room-details-content .left .share .social {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 16px;
}
.room-details .room-details-content .left .share .social a {
  transition: all 0.3s ease-in-out;
}
.room-details .room-details-content .left .share .social a .social-icon {
  font-size: 24px;
  color: var(--td-text-primary);
}
.room-details .room-details-content .left .share .social a:hover .social-icon {
  color: var(--td-primary);
}
.room-details .room-details-content .right h4 {
  color: var(--td-heading);
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  margin-bottom: 16px;
}

.latest-news {
  background: var(--td-bg-1);
}
.latest-news h3 {
  color: var(--td-heading);
  font-size: 30px;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 30px;
}

/*----------------------------------------*/
/*  Restaurant styles
/*----------------------------------------*/
.our-restaurant-contents .heading-block {
  margin-bottom: 30px;
}
.our-restaurant-contents .heading-block .meta {
  display: flex;
  align-items: center;
  gap: 4px;
}
.our-restaurant-contents .heading-block .meta span {
  flex: 0 0 auto;
}
.our-restaurant-contents .heading-block .meta span img {
  width: 18px;
}

.restaurant-open-schedule {
  border: 1px solid var(--td-primary);
  background: #FFF4E5;
  display: inline-flex;
  align-items: center;
}
@media (max-width: 480px) {
  .restaurant-open-schedule {
    display: flex;
    flex-direction: column;
  }
}
.restaurant-open-schedule .time-block {
  padding: 20px 64px;
  position: relative;
}
@media (max-width: 767px) {
  .restaurant-open-schedule .time-block {
    padding: 20px 40px;
    width: 100%;
  }
}
.restaurant-open-schedule .time-block:not(:last-child)::after {
  position: absolute;
  content: "";
  height: calc(100% - 40px);
  inset-inline-end: 0;
  top: 50%;
  transform: translateY(-50%);
  background: #E6DCCE;
  width: 1px;
}
@media (max-width: 480px) {
  .restaurant-open-schedule .time-block:not(:last-child)::after {
    height: 1px;
    inset-inline-end: 0;
    top: auto;
    bottom: 0;
    width: 100%;
    transform: inherit;
  }
}
.restaurant-open-schedule .time-block .time {
  font-size: 1.5rem;
  color: #151415;
}
.restaurant-open-schedule .time-block .status {
  margin-top: 2px;
  display: block;
}

/*----------------------------------------*/
/*  Terms conditions styles
/*----------------------------------------*/
.td-page-contents h3 {
  font-size: 38px;
  margin-bottom: 12px;
}
.td-page-contents h4 {
  font-size: 24px;
  margin-bottom: 16px;
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .td-page-contents h4 {
    font-size: 14px;
  }
}
.td-page-contents p {
  font-size: 16px;
  line-height: 28px;
}
.td-page-contents p:not(:last-child) {
  margin-bottom: 30px;
}
.td-page-contents ul {
  margin-bottom: 30px;
  list-style-type: disc;
  padding-inline-start: 28px;
}
.td-page-contents ul:last-child {
  margin-bottom: 0;
}
.td-page-contents ul li {
  margin-bottom: 14px;
  color: #444344;
}
.td-page-contents a {
  font-size: 16px;
  font-weight: 600;
  line-height: 26px;
  color: var(--td-primary);
  text-decoration: underline;
}

/*----------------------------------------*/
/*  Contacts styles
/*----------------------------------------*/
.contact-info-item {
  display: flex;
  align-items: center;
  gap: 20px;
  border: 1px solid rgba(21, 20, 21, 0.16);
  padding: 30px 30px;
}
@media (max-width: 767px) {
  .contact-info-item {
    padding: 20px 20px;
  }
}
.contact-info-item .icon {
  flex: 0 0 auto;
}
.contact-info-item .icon img {
  width: 40px;
  height: 40px;
}
.contact-info-item .title {
  font-size: 24px;
}
@media (max-width: 767px) {
  .contact-info-item .title {
    font-size: 20px;
  }
}
.contact-info-item .description {
  margin-top: 6px;
}
.contact-info-item .description a:hover {
  color: var(--td-primary);
}

.google-map-inner {
  position: relative;
  border-radius: 5px;
  overflow: hidden;
}
.google-map-inner iframe {
  min-height: 550px;
  width: 100%;
  margin-bottom: -8px;
}

.contact-form-area {
  background: #FFF4E5;
}

.contact-form-heading {
  margin-bottom: 25px;
}

.contact-form .td-form-group .input-field .form-control,
.contact-form .td-form-group .input-field textarea {
  border-color: rgba(21, 20, 21, 0.16);
  background: transparent;
}
.contact-form .td-form-group .input-field .form-control:focus,
.contact-form .td-form-group .input-field textarea:focus {
  border-color: var(--td-primary);
}

/*----------------------------------------*/
/*  Checkout styles
/*----------------------------------------*/
.checkout-area {
  background: var(--td-bg-1);
}

.checkout-heading .title {
  font-size: 20px;
  margin-bottom: 20px;
  font-weight: 500;
}

.checkout-container {
  max-width: 1000px;
  margin: auto;
  margin-bottom: 60px;
}

.checkout-progress {
  display: flex;
  justify-content: space-between;
  position: relative;
  margin-bottom: 30px;
}
.checkout-progress::before {
  content: "";
  position: absolute;
  top: 25px;
  inset-inline-start: 12.5%;
  inset-inline-end: 12.5%;
  height: 1px;
  background: rgba(0, 0, 0, 0.2);
  z-index: 1;
}
.checkout-progress .step {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 25%;
}
.checkout-progress .step-number {
  width: 46px;
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff4e5;
  border: 1px solid #dddddd;
  margin-bottom: 10px;
  font-weight: bold;
  color: #777777;
  font-size: 20px;
  font-weight: 500;
}
@media (max-width: 767px) {
  .checkout-progress .step-number {
    width: 36px;
    height: 36px;
  }
}
.checkout-progress .step-label {
  text-align: center;
  font-size: 20px;
  color: #777777;
  font-family: var(--td-heading-font);
  font-weight: 400;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .checkout-progress .step-label {
    font-size: 18px;
  }
}
@media (max-width: 767px) {
  .checkout-progress .step-label {
    font-size: 16px;
  }
}
@media (max-width: 480px) {
  .checkout-progress .step-label {
    font-size: 13px;
  }
}
.checkout-progress .step.completed .step-number {
  background-color: #a0855c;
  border-color: #a0855c;
  color: var(--td-white);
  position: relative;
}
.checkout-progress .step.completed .step-label {
  color: #a0855c;
}
.checkout-progress .step.completed .checkmark {
  display: block;
}
.checkout-progress .step.completed .step-number-text {
  display: none;
}
.checkout-progress .step.active .step-number {
  border: 2px solid #a0855c;
  color: #a0855c;
}
.checkout-progress .step.active .step-label {
  color: #333333;
}
.checkout-progress .checkmark {
  display: none;
  width: 20px;
  height: 20px;
}

.checkout-personal-info .payment-method {
  margin-top: 30px;
}
.checkout-personal-info .payment-options {
  display: flex;
  gap: 14px;
  margin-top: 15px;
  flex-wrap: wrap;
}
.checkout-personal-info .payment-option {
  padding: 12px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid rgba(21, 20, 21, 0.16);
  font-weight: 500;
  color: rgba(21, 20, 21, 0.6);
}
.checkout-personal-info .payment-option.active {
  border: 1px solid var(--td-primary);
  background-color: transparent;
  color: #151415;
}
.checkout-personal-info .payment-option.active i {
  color: var(--td-primary);
}
.checkout-personal-info .payment-option i {
  font-size: 18px;
  color: #666;
}

.reservation-summary {
  padding-inline-start: 20px;
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .reservation-summary {
    padding-inline-start: 0;
  }
}
.reservation-summary .booking-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.reservation-summary .selected-date {
  border-bottom: 1px solid #eee;
  border: 1px solid rgba(21, 20, 21, 0.16);
  background: #FFF4E5;
  padding: 10px 16px;
}
.reservation-summary .room-details {
  display: flex;
  gap: 24px;
  align-items: center;
  border: 1px solid rgba(21, 20, 21, 0.16);
}
@media (max-width: 767px) {
  .reservation-summary .room-details {
    flex-direction: column;
    align-items: start;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .reservation-summary .room-details {
    flex-direction: row;
    align-items: center;
  }
}
.reservation-summary .room-image {
  width: 146px;
  height: 146px;
  object-fit: cover;
}
@media (max-width: 767px) {
  .reservation-summary .room-image {
    width: 100%;
    height: 250px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .reservation-summary .room-image {
    width: 146px;
  }
}
@media (max-width: 767px) {
  .reservation-summary .room-info {
    padding-left: 16px;
    padding-right: 16px;
    padding-bottom: 16px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .reservation-summary .room-info {
    padding-left: 0px;
    padding-right: 0px;
    padding-bottom: 0px;
  }
}
.reservation-summary .room-info h3 {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 5px;
}
@media (max-width: 767px) {
  .reservation-summary .room-info h3 {
    font-size: 20px;
  }
}
.reservation-summary .pirce-info {
  display: flex;
  align-items: center;
  font-weight: 500;
}
.reservation-summary .pirce-info .price {
  color: #b89468;
  font-size: 20px;
}
.reservation-summary .room-amenities {
  display: flex;
  gap: 12px 16px;
  margin-top: 10px;
  flex-wrap: wrap;
}
.reservation-summary .amenity {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #666;
}
.reservation-summary .amenity .icon {
  flex: 0 0 auto;
}
.reservation-summary .amenity .icon img {
  width: 18px;
}
.reservation-summary .price-summary {
  border-radius: 3px;
  padding: 30px 30px;
  border: 1px solid rgba(21, 20, 21, 0.16);
}
.reservation-summary .price-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
.reservation-summary .price-row h6 {
  font-size: 14px;
}
.reservation-summary .price-row span {
  font-size: 14px;
  font-weight: 500;
}
.reservation-summary .price-row.total {
  margin-top: 10px;
  border-top: 1px solid rgba(0, 0, 0, 0.2);
  padding-top: 15px;
  font-size: 16px;
  font-weight: bold;
}
.reservation-summary .negative {
  color: #d12e5c;
}
.reservation-summary .terms {
  margin-top: 30px;
  display: flex;
  align-items: flex-start;
  gap: 10px;
}
.reservation-summary .action-buttons {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}
@media (max-width: 767px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .reservation-summary .action-buttons {
    flex-wrap: wrap;
  }
}

.reservation-status-header {
  text-align: center;
}
.reservation-status-header .icon {
  width: 160px;
  height: 160px;
  border-radius: 50%;
  margin: 0 auto 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
@media (max-width: 767px) {
  .reservation-status-header .icon {
    width: 120px;
    height: 120px;
  }
}
.reservation-status-header .reservation-title {
  font-size: 28px;
  margin-bottom: 15px;
}
@media (max-width: 767px) {
  .reservation-status-header .reservation-title {
    font-size: 24px;
  }
}
.reservation-status-header .reservation-message {
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  font-size: 14px;
  line-height: 1.6;
}

.reservation-booking-details {
  flex: 1;
  padding: 24px;
  border: 1px solid rgba(21, 20, 21, 0.16);
}
.reservation-booking-details .details-section {
  margin-bottom: 15px;
}
.reservation-booking-details .details-row {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}
.reservation-booking-details .details-row:not(:first-child) {
  padding-top: 18px;
}
.reservation-booking-details .details-row:not(:last-child) {
  padding-bottom: 18px;
  border-bottom: 1px solid rgba(21, 20, 21, 0.16);
}
.reservation-booking-details .details-label {
  font-size: 14px;
}
.reservation-booking-details .details-value {
  font-weight: 500;
  text-align: right;
  color: #444344;
}
.reservation-booking-details .price-total {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
}
.reservation-booking-details .discount {
  color: #e74c3c;
}
.reservation-booking-details .status-badge {
  display: inline-block;
  padding: 3px 10px;
  font-size: 12px;
  font-weight: 600;
  color: var(--td-white);
}
.reservation-booking-details .status-booked {
  background-color: var(--td-primary);
}
.reservation-booking-details .status-paid {
  background-color: var(--td-primary);
}

.reservation-room-details {
  border: 1px solid rgba(21, 20, 21, 0.16);
  padding: 16px;
}
.reservation-room-details .details-title {
  font-size: 24px;
  margin-bottom: 10px;
  padding-bottom: 5px;
}
.reservation-room-details .room-image {
  width: 100%;
  height: 240px;
  object-fit: cover;
  border-radius: 0;
  margin-bottom: 20px;
}
.reservation-room-details .room-title {
  font-size: 20px;
  margin-bottom: 10px;
  color: var(--text-color);
}
.reservation-room-details .room-info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 14px;
}
.reservation-room-details .room-info-label {
  font-weight: 500;
}

.reservation-footer-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 20px;
  flex-wrap: wrap;
}
@media (max-width: 480px) {
  .reservation-footer-buttons {
    flex-direction: column;
  }
}

/*----------------------------------------*/
/*  Promo styles
/*----------------------------------------*/
.popup-overlay {
  position: fixed;
  top: 0;
  inset-inline-start: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(21, 20, 21, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  transition: all 0.3s;
  visibility: visible;
  opacity: 1;
}
.popup-overlay.hidden {
  visibility: hidden;
  opacity: 0;
}

.promo-popup-main {
  position: relative;
  display: flex;
  width: 900px;
  max-width: 90%;
  background-color: var(--td-white);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  z-index: 1001;
}
.promo-popup-main .promo-contents {
  flex: 1;
  padding: 40px 40px;
  background-color: var(--td-white);
  display: flex;
  flex-direction: column;
  justify-content: center;
}
@media (max-width: 767px) {
  .promo-popup-main .promo-contents {
    padding: 30px 30px;
  }
}
.promo-popup-main .promo-contents .logo {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
}
.promo-popup-main .promo-contents .heading {
  font-weight: 500;
  margin-bottom: 15px;
  font-family: var(--td-body-font);
  font-size: 20px;
}
.promo-popup-main .promo-contents .discount {
  font-weight: 400;
  color: #b79668;
  margin-bottom: 20px;
  font-size: 50px;
  font-family: var(--td-body-font);
}
@media (max-width: 767px) {
  .promo-popup-main .promo-contents .discount {
    font-size: 36px;
  }
}
.promo-popup-main .promo-contents .description {
  margin-bottom: 25px;
  line-height: 1.5;
}
@media (max-width: 767px) {
  .promo-popup-main .promo-contents .description {
    margin-bottom: 16px;
  }
}
.promo-popup-main .promo-contents .subscription-form {
  margin-top: 10px;
  position: relative;
}
.promo-popup-main .promo-contents .email-input {
  border: 1px solid rgba(21, 20, 21, 0.16);
  height: 52px;
  padding-inline-end: 150px;
}
.promo-popup-main .promo-contents .email-input::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: var(--td-text-primary) !important;
}
.promo-popup-main .promo-contents .email-input::-moz-placeholder {
  /* Firefox 19+ */
  color: var(--td-text-primary) !important;
}
.promo-popup-main .promo-contents .email-input:-moz-placeholder {
  /* Firefox 4-18 */
  color: var(--td-text-primary) !important;
}
.promo-popup-main .promo-contents .email-input:-ms-input-placeholder {
  /* IE 10+  Edge*/
  color: var(--td-text-primary) !important;
}
.promo-popup-main .promo-contents .email-input::placeholder {
  /* MODERN BROWSER */
  color: var(--td-text-primary) !important;
}
.promo-popup-main .promo-contents .email-input:focus {
  border-color: var(--td-primary);
  color: var(--td-text-primary) !important;
}
@media (max-width: 480px) {
  .promo-popup-main .promo-contents .email-input {
    padding-inline-end: 16px;
  }
}
.promo-popup-main .promo-contents .submit-button {
  height: 36px;
  position: absolute;
  inset-inline-end: 10px;
  top: 50%;
  transform: translateY(-50%);
  padding: 0 16px;
  border: 0;
}
@media (max-width: 480px) {
  .promo-popup-main .promo-contents .submit-button {
    position: inherit;
    top: inherit;
    transform: inherit;
    margin-top: 16px;
    width: 100%;
  }
}
.promo-popup-main .promo-contents .submit-button:hover {
  background-color: #a68457;
}
.promo-popup-main .promo-contents .submit-button:hover:hover {
  color: var(--td-white);
  background-color: var(--td-heading);
}
.promo-popup-main .promp-image {
  flex: 1;
  position: relative;
}
@media (max-width: 767px) {
  .promo-popup-main .promp-image {
    display: none;
  }
}
.promo-popup-main .promp-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.promo-popup-main .close-btn {
  position: absolute;
  top: -15px;
  inset-inline-end: -15px;
  width: 30px;
  height: 30px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 10;
  transition: background-color 0.3s;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}
.promo-popup-main .close-btn svg {
  transform: rotate(180deg);
  transition: all 0.3s;
}
.promo-popup-main .close-btn:hover {
  background-color: rgb(255, 255, 255);
}
.promo-popup-main .close-btn:hover svg {
  transform: rotate(60deg);
}
.promo-popup-main .image-overlay {
  position: absolute;
  top: 50%;
  inset-inline-start: 50%;
  pointer-events: none;
  width: calc(100% - 30px);
  height: calc(100% - 30px);
  border: 1px solid #FFE4C0;
  transform: translate(-50%, -50%);
}
[dir=rtl] .promo-popup-main .image-overlay {
  inset-inline-start: auto;
  inset-inline-end: 50%;
}

/*----------------------------------------*/
/*  5.1 footer-1
/*----------------------------------------*/
.footer-1 .main-footer {
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
}
.footer-1 .main-footer::after {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  width: 100%;
  height: 100%;
  content: "";
  background: rgba(21, 20, 21, 0.78);
  z-index: 1;
}
.footer-1 .main-footer .full-footer {
  display: flex;
  align-items: start;
  gap: 135px;
  position: relative;
  z-index: 2;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .footer-1 .main-footer .full-footer {
    flex-direction: column;
    align-items: start;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .footer-1 .main-footer .full-footer {
    gap: 100px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-1 .main-footer .full-footer {
    gap: 50px;
  }
}
@media (max-width: 767px) {
  .footer-1 .main-footer .full-footer {
    gap: 40px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-1 .main-footer .full-footer {
    gap: 50px;
  }
}
.footer-1 .main-footer .full-footer .left {
  width: 30%;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .footer-1 .main-footer .full-footer .left {
    width: 25%;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .footer-1 .main-footer .full-footer .left {
    width: 20%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .footer-1 .main-footer .full-footer .left {
    width: 100%;
  }
}
.footer-1 .main-footer .full-footer .left .logo {
  margin-bottom: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .footer-1 .main-footer .full-footer .left .logo {
    margin-bottom: 14px;
  }
}
.footer-1 .main-footer .full-footer .left .logo a {
  display: inline-block;
  height: 40px;
}
.footer-1 .main-footer .full-footer .left .logo a img {
  height: 100%;
}
.footer-1 .main-footer .full-footer .left .description {
  color: #E3E3E3;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.625;
}
.footer-1 .main-footer .full-footer .left .social-buttons {
  margin-top: 30px;
  display: flex;
  align-items: center;
  gap: 16px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .footer-1 .main-footer .full-footer .left .social-buttons {
    margin-top: 14px;
  }
}
.footer-1 .main-footer .full-footer .left .social-buttons a {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  transition: all 0.3s ease-in-out;
}
.footer-1 .main-footer .full-footer .left .social-buttons a .social-icon {
  font-size: 24px;
  color: var(--td-white);
}
.footer-1 .main-footer .full-footer .left .social-buttons a:hover .social-icon {
  color: var(--td-primary);
}
.footer-1 .main-footer .full-footer .right {
  width: 70%;
  display: grid;
  grid-template-columns: repeat(3, 1fr) 235px;
  gap: 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .footer-1 .main-footer .full-footer .right {
    width: 65%;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .footer-1 .main-footer .full-footer .right {
    width: 80%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .footer-1 .main-footer .full-footer .right {
    width: 100%;
  }
}
@media (max-width: 767px) {
  .footer-1 .main-footer .full-footer .right {
    grid-template-columns: repeat(1, 1fr);
    gap: 30px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-1 .main-footer .full-footer .right {
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
  }
}
.footer-1 .main-footer .full-footer .right .footer-link-list h5 {
  color: var(--td-white);
  font-size: 18px;
  font-weight: 400;
  line-height: 1.3333333333;
  text-transform: capitalize;
  margin-bottom: 38px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-1 .main-footer .full-footer .right .footer-link-list h5 {
    margin-bottom: 26px;
  }
}
@media (max-width: 767px) {
  .footer-1 .main-footer .full-footer .right .footer-link-list h5 {
    margin-bottom: 18px;
  }
}
.footer-1 .main-footer .full-footer .right .footer-link-list ul {
  list-style-type: none;
}
.footer-1 .main-footer .full-footer .right .footer-link-list ul li {
  margin-bottom: 16px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-1 .main-footer .full-footer .right .footer-link-list ul li {
    margin-bottom: 14px;
  }
}
@media (max-width: 767px) {
  .footer-1 .main-footer .full-footer .right .footer-link-list ul li {
    margin-bottom: 5px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-1 .main-footer .full-footer .right .footer-link-list ul li {
    margin-bottom: 10px;
  }
}
.footer-1 .main-footer .full-footer .right .footer-link-list ul li:last-child {
  margin-bottom: 0;
}
.footer-1 .main-footer .full-footer .right .footer-link-list ul li a {
  color: #E3E3E3;
  font-family: var(--td-body-font);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  transition: all 0.3s ease-in-out;
  display: flex;
  align-items: start;
  gap: 10px;
}
.footer-1 .main-footer .full-footer .right .footer-link-list ul li a .contact-icon-box {
  display: flex;
  justify-content: center;
  align-items: center;
  transform: translateY(2px);
}
.footer-1 .main-footer .full-footer .right .footer-link-list ul li a .contact-icon-box .contact-icon {
  font-size: 20px;
}
.footer-1 .main-footer .full-footer .right .footer-link-list ul li a:hover {
  color: var(--td-primary);
}
.footer-1 .bottom-footer {
  background-color: var(--td-heading);
  padding: 26px 0;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .footer-1 .bottom-footer {
    padding: 20px 0;
  }
}
.footer-1 .bottom-footer .bottom-footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}
@media (max-width: 767px) {
  .footer-1 .bottom-footer .bottom-footer-content {
    flex-direction: column;
    align-items: start;
    gap: 10px;
  }
}
.footer-1 .bottom-footer .bottom-footer-content .left p {
  color: var(--td-white);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.7142857143;
}
.footer-1 .bottom-footer .bottom-footer-content .right {
  display: flex;
  align-items: center;
  gap: 24px;
}
@media (max-width: 767px) {
  .footer-1 .bottom-footer .bottom-footer-content .right {
    gap: 10px;
  }
}
.footer-1 .bottom-footer .bottom-footer-content .right a {
  color: var(--td-white);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.7142857143;
  transition: all 0.3s ease-in-out;
}
.footer-1 .bottom-footer .bottom-footer-content .right a:hover {
  color: var(--td-primary);
}

/*----------------------------------------*/
/*  5.2 footer-2
/*----------------------------------------*/
.footer-2 {
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
  overflow: hidden;
}
.footer-2 .all-footer-item {
  position: relative;
  z-index: 2;
}
.footer-2 .all-footer-item .subscribe-box .subscribe-box-content {
  display: flex;
  justify-content: center;
  align-items: center;
}
.footer-2 .all-footer-item .subscribe-box .subscribe-box-content .full {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  width: 970px;
  background-color: var(--td-primary);
  padding: 60px 60px;
  border-radius: 16px;
  margin-top: 100px;
  margin-bottom: 24px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .footer-2 .all-footer-item .subscribe-box .subscribe-box-content .full {
    padding: 40px 60px;
    margin-top: 60px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-2 .all-footer-item .subscribe-box .subscribe-box-content .full {
    padding: 30px 40px;
    margin-top: 60px;
    width: 100%;
  }
}
@media (max-width: 767px) {
  .footer-2 .all-footer-item .subscribe-box .subscribe-box-content .full {
    padding: 20px 30px;
    margin-top: 30px;
    width: 100%;
  }
}
@media (max-width: 767px) {
  .footer-2 .all-footer-item .subscribe-box .subscribe-box-content .full {
    flex-direction: column;
    align-items: start;
    justify-content: start;
  }
}
.footer-2 .all-footer-item .subscribe-box .subscribe-box-content .full .left {
  width: 40%;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-2 .all-footer-item .subscribe-box .subscribe-box-content .full .left {
    width: 45%;
  }
}
@media (max-width: 767px) {
  .footer-2 .all-footer-item .subscribe-box .subscribe-box-content .full .left {
    width: 100%;
  }
}
.footer-2 .all-footer-item .subscribe-box .subscribe-box-content .full .left h2 {
  color: var(--td-white);
  font-size: 2rem;
  font-weight: 600;
  line-height: 1;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-2 .all-footer-item .subscribe-box .subscribe-box-content .full .left h2 {
    font-size: 1.625rem;
  }
}
@media (max-width: 767px) {
  .footer-2 .all-footer-item .subscribe-box .subscribe-box-content .full .left h2 {
    font-size: 1.25rem;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-2 .all-footer-item .subscribe-box .subscribe-box-content .full .left h2 {
    font-size: 1.625rem;
  }
}
.footer-2 .all-footer-item .subscribe-box .subscribe-box-content .full .right {
  width: 60%;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-2 .all-footer-item .subscribe-box .subscribe-box-content .full .right {
    width: 55%;
  }
}
@media (max-width: 767px) {
  .footer-2 .all-footer-item .subscribe-box .subscribe-box-content .full .right {
    width: 100%;
  }
}
.footer-2 .all-footer-item .subscribe-box .subscribe-box-content .full .right .subscribe-input {
  position: relative;
}
.footer-2 .all-footer-item .subscribe-box .subscribe-box-content .full .right .subscribe-input input[type=text],
.footer-2 .all-footer-item .subscribe-box .subscribe-box-content .full .right .subscribe-input input[type=email] {
  outline: none;
  height: 68px;
  width: 100%;
  padding: 8px 140px 8px 15px;
  border-radius: 16px;
  color: #6B7280;
  background: var(--td-white);
  font-size: 16px;
}
@media (max-width: 767px) {
  .footer-2 .all-footer-item .subscribe-box .subscribe-box-content .full .right .subscribe-input input[type=text],
  .footer-2 .all-footer-item .subscribe-box .subscribe-box-content .full .right .subscribe-input input[type=email] {
    height: 48px;
  }
}
.footer-2 .all-footer-item .subscribe-box .subscribe-box-content .full .right .subscribe-input input[type=text]::placeholder,
.footer-2 .all-footer-item .subscribe-box .subscribe-box-content .full .right .subscribe-input input[type=email]::placeholder {
  color: #6B7280;
}
.footer-2 .all-footer-item .subscribe-box .subscribe-box-content .full .right .subscribe-input .subscribe-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-end: 8px;
}
.footer-2 .all-footer-item .footer-links .left {
  padding-inline-end: 90px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .footer-2 .all-footer-item .footer-links .left {
    padding-inline-end: 0px;
    padding-bottom: 20px;
  }
}
.footer-2 .all-footer-item .footer-links .left .logo {
  display: inline-block;
  height: 32px;
  margin-bottom: 20px;
}
.footer-2 .all-footer-item .footer-links .left .logo img {
  height: 100%;
}
.footer-2 .all-footer-item .footer-links .left p {
  color: #E4E6EA;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.5;
}
.footer-2 .all-footer-item .footer-links .left .social {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
}
.footer-2 .all-footer-item .footer-links .left .social a {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 36px;
  flex-shrink: 0;
  border-radius: 8px;
  border: 1px solid var(--td-white);
  transition: all 0.3s ease-in-out;
}
.footer-2 .all-footer-item .footer-links .left .social a span {
  display: flex;
}
.footer-2 .all-footer-item .footer-links .left .social a span .social-icon {
  font-size: 26px;
  color: var(--td-white);
}
.footer-2 .all-footer-item .footer-links .left .social a:hover {
  background-color: var(--td-white);
}
.footer-2 .all-footer-item .footer-links .left .social a:hover span .social-icon {
  color: var(--td-primary);
}
.footer-2 .all-footer-item .footer-links .right {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 24px;
}
@media (max-width: 767px) {
  .footer-2 .all-footer-item .footer-links .right {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-2 .all-footer-item .footer-links .right {
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
  }
}
.footer-2 .all-footer-item .footer-links .right .link-box h5 {
  color: var(--td-white);
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1;
  margin-bottom: 20px;
}
@media (max-width: 767px) {
  .footer-2 .all-footer-item .footer-links .right .link-box h5 {
    margin-bottom: 12px;
  }
}
.footer-2 .all-footer-item .footer-links .right .link-box ul {
  list-style-type: none;
}
.footer-2 .all-footer-item .footer-links .right .link-box ul li {
  margin-bottom: 16px;
}
@media (max-width: 767px) {
  .footer-2 .all-footer-item .footer-links .right .link-box ul li {
    margin-bottom: 8px;
  }
}
.footer-2 .all-footer-item .footer-links .right .link-box ul li:last-child {
  margin-bottom: 0;
}
.footer-2 .all-footer-item .footer-links .right .link-box ul li a {
  color: #E4E6EA;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1;
  transition: all 0.3s ease-in-out;
}
.footer-2 .all-footer-item .footer-links .right .link-box ul li a:hover {
  color: var(--td-white);
}
.footer-2 .all-footer-item hr {
  border-color: rgba(255, 255, 255, 0.3);
  margin: 24px 0;
}
.footer-2 .all-footer-item .footer-copyright {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
  padding-bottom: 24px;
}
@media (max-width: 767px) {
  .footer-2 .all-footer-item .footer-copyright {
    flex-direction: column;
    justify-content: start;
    align-items: start;
  }
}
.footer-2 .all-footer-item .footer-copyright .middle p {
  color: #E4E6EA;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.5;
}
.footer-2 .all-footer-item .footer-copyright .right ul {
  display: flex;
  align-items: center;
  list-style-type: none;
  gap: 16px;
}
.footer-2 .all-footer-item .footer-copyright .right ul li a {
  color: var(--gray-200, #E4E6EA);
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.5;
}
.footer-2 .circle-1 {
  position: absolute;
  inset-inline-start: -90px;
  top: 117px;
  z-index: 1;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .footer-2 .circle-1 img {
    display: none;
  }
}
.footer-2 .circle-2 {
  position: absolute;
  inset-inline-end: -21%;
  bottom: -62%;
  z-index: 1;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .footer-2 .circle-2 img {
    display: none;
  }
}

/*# sourceMappingURL=styles.css.map */
