@use '../../utils' as *;

/*----------------------------------------*/
/*  About
/*----------------------------------------*/
.about-us {
  position: relative;

  .about-us-content {
    position: relative;
    z-index: 2;

    .left {
      margin-inline-end: 30px;
      position: relative;

      @media #{$lg} {
        margin-inline-end: 10px;
      }

      @media #{$md,$xs} {
        margin-inline-end: 0px;
      }

      .left-element {
        position: absolute;
        top: 0%;
        inset-inline-start: 0%;
        z-index: 1;

        img {
          @media #{$lg,$md,$xs} {
            width: 450px;
          }

          @media #{$xs} {
            width: 350px;
          }

          @media #{$sm} {
            width: 450px;
          }
        }
      }

      .all-img {

        .left-img {
          display: flex;
          flex-direction: column;
          justify-content: end;

          .short-img {
            width: 100%;
            height: 224px;
            position: relative;

            @media #{$lg,$md,$xs} {
              height: 180px;
            }

            @media #{$xs} {
              height: 100px;
            }

            @media #{$sm} {
              height: 180px;
            }

            &::before {
              content: '';
              position: absolute;
              top: 50%;
              inset-inline-start: 50%;
              transform: translate(-50%, -50%);
              width: 88%;
              height: 88%;
              background: rgba(0, 0, 0, 0.0);
              border: 1px solid rgba(255, 255, 255, 0.4);
              z-index: 1;

              @include rtl {
                inset-inline-start: auto;
                inset-inline-end: 50%;
              }
            }

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
        }

        .right-img {
          .long-img {
            width: 100%;
            height: 588px;
            position: relative;

            @media #{$lg,$md,$xs} {
              height: 488px;
            }

            @media #{$xs} {
              height: 288px;
            }

            @media #{$sm} {
              height: 488px;
            }

            &::before {
              content: '';
              position: absolute;
              top: 50%;
              inset-inline-start: 50%;
              transform: translate(-50%, -50%);
              width: 88%;
              height: 94%;
              background: rgba(0, 0, 0, 0.0);
              border: 1px solid rgba(255, 255, 255, 0.4);
              z-index: 1;

              @include rtl {
                inset-inline-start: auto;
                inset-inline-end: 50%;
              }
            }

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
        }
      }
    }
  }

  .about-element {
    position: absolute;
    inset-inline-end: 0%;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;

    @media #{$md,$xs} {
      display: none;
    }

    img {
      @media #{$xxl,$xl} {
        width: 400px;
      }

      @media #{$lg,$md,$xs} {
        width: 300px;
      }
    }
  }
}

// About style two
.about-contents-two {
  padding-inline-end: 50px;

  @media #{$xs,$sm,$md} {
    padding-inline-end: 0;
  }

  .heading-contents {
    .title {
      font-size: 40px;
      margin-bottom: rem(16);
    }
  }

  .signature {
    img {
      height: 60px;
    }
  }

  .btn-link {
    margin-top: 40px;
  }
}

.about-thumb-wrapper-two {
  position: relative;
  margin-bottom: 145px;

  .about-thumb-one {
    img {
      width: 338px;
    }
  }

  .about-thumb-two {
    position: absolute;
    top: 145px;
    inset-inline-end: 0;

    img {
      width: 338px;
    }
  }

  .border-shape {
    width: 338px;
    height: 303px;
    border: 4px solid #AA8453;
    position: absolute;
    inset-inline-end: 60px;
    top: 47px;
    z-index: -1;

    @media #{$xxs} {
      width: 300px;
      height: 300px;
      inset-inline-end: 20px;
    }
  }
}

.about-two-shapes {
  .shape-one {
    position: absolute;
    bottom: 9%;
    inset-inline-start: 48%;
    transform: translateX(-50%);

    img {
      width: 430px;

      @media #{$lg,$xl} {
        width: 260px;
      }
    }
  }
}