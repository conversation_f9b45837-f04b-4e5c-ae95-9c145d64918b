@use '../../utils' as *;

/*----------------------------------------*/
/*  rooms
/*----------------------------------------*/
.all-rooms-area {
  .title-dropdown-filter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 30px;

    @media #{$md,$xs} {
      flex-direction: column;
      align-items: start;
      gap: 20px;
    }

    .left {
      .title {
        h2 {
          color: var(--td-heading);
          font-size: 40px;
          font-weight: 400;
          line-height: normal;

          @media #{$lg,$md,$xs} {
            font-size: 35px;
          }

          @media #{$md} {
            font-size: 30px;
          }

          @media #{$xs} {
            font-size: 25px;
          }
        }
      }
    }

    .right {
      .common-nice-select-dropdown {
        display: flex;
        align-items: center;
        gap: 14px;

        @media #{$xs} {
          flex-direction: column;
          align-items: start;
          gap: 10px;
        }
      }
    }
  }
}

.room-details {
  .room-details-content {
    .left {
      margin-inline-end: 44px;

      @media #{$xs,$sm,$md} {
        margin-inline-end: 0px;
      }

      .blog-banner-img {
        height: 452px;
        width: 100%;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .date-author {
        display: flex;
        align-items: center;
        gap: 22px;
        margin-top: 20px;

        .date {
          p {
            color: var(--td-text-secondary);
            font-size: 18px;
            font-weight: 400;
            line-height: normal;

            span {
              font-weight: 500;
            }
          }
        }

        .author {
          p {
            color: var(--td-text-secondary);
            font-size: 18px;
            font-weight: 400;
            line-height: normal;

            span {
              font-weight: 500;
            }
          }
        }

      }

      .blog-title {
        color: var(--td-heading);
        font-size: 40px;
        font-weight: 400;
        line-height: normal;
        margin-top: 30px;
        margin-bottom: 16px;

        @media #{$xl} {
          font-size: 32px;
        }

        @media #{$lg} {
          font-size: 28px;
        }

        @media #{$md} {
          font-size: 26px;
        }

        @media #{$xs} {
          font-size: 24px;
        }
      }

      .description {
        color: var(--td-text-primary);
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: lh(26, 16);

        @media #{$xs} {
          font-size: 14px;
        }
      }

      .desctiption-box {
        margin-top: 48px;

        .description-box-content {
          margin-top: 30px;

          h4 {
            color: var(--td-heading);
            font-size: 20px;
            font-weight: 400;
            line-height: normal;
            margin-bottom: 16px;
          }

          p {
            color: var(--td-text-primary);
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: lh(26, 16);

            @media #{$xs} {
              font-size: 14px;
            }
          }
        }

        .description-box-banner {
          margin-top: 30px;
          height: 340px;
          width: 100%;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }

      .blog-section-saperate {
        border-color: #151415;
        opacity: 0.1;
        border-width: 1px;
        margin: 50px 0 20px 0;
      }

      .share {
        h5 {
          color: var(--td-heading);
          font-size: 24px;
          font-weight: 400;
          line-height: normal;
        }

        .social {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-top: 16px;

          a {
            transition: all 0.3s ease-in-out;

            .social-icon {
              font-size: 24px;
              color: var(--td-text-primary);
            }

            &:hover {
              .social-icon {
                color: var(--td-primary);
              }
            }
          }
        }
      }
    }

    .right {
      h4 {
        color: var(--td-heading);
        font-size: 24px;
        font-style: normal;
        font-weight: 400;
        margin-bottom: 16px;
      }
    }
  }
}

.latest-news {
  background: var(--td-bg-1);

  h3 {
    color: var(--td-heading);
    font-size: 30px;
    font-weight: 400;
    line-height: normal;
    margin-bottom: 30px;
  }
}