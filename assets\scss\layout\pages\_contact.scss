@use '../../utils' as *;

/*----------------------------------------*/
/*  Contacts styles
/*----------------------------------------*/

.contact-info-item {
    display: flex;
    align-items: center;
    gap: 20px;
    border: 1px solid rgba(21, 20, 21, 0.16);
    padding: 30px 30px;

    @media #{$xs} {
        padding: 20px 20px
    }

    .icon {
        flex: 0 0 auto;

        img {
            width: 40px;
            height: 40px;
        }
    }

    .title {
        font-size: 24px;

        @media #{$xs} {
            font-size: 20px;
        }
    }

    .description {
        margin-top: 6px;

        a {
            &:hover {
                color: var(--td-primary);
            }
        }
    }
}

// google map
.google-map-inner {
    position: relative;
    border-radius: 5px;
    overflow: hidden;

    iframe {
        min-height: 550px;
        width: 100%;
        margin-bottom: -8px;
    }
}

// contact form
.contact-form-area {
    background: #FFF4E5;
}

.contact-form-heading {
    margin-bottom: 25px;
}

.contact-form {
    .td-form-group {
        .input-field {

            .form-control,
            textarea {
                border-color: rgba(21, 20, 21, 0.16);
                background: transparent;

                &:focus {
                    border-color: var(--td-primary);
                }
            }
        }
    }
}